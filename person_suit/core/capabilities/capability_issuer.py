# -*- coding: utf-8 -*-
"""
File: person_suit/core/capabilities/capability_issuer.py
Purpose: Placeholder service for issuing signed CAW Capability Contexts.

NOTE: This file was moved from core/security/ during refactoring.

This module demonstrates how capabilities might be signed before being
used or passed to other components.
"""

import logging
from dataclasses import fields  # Added fields
from datetime import datetime, timezone
from typing import Optional, Set, Tuple
from uuid import UUID, uuid4

# Core schemas
from ..caw.schemas import (
    Capability,  # Use helper
    CapabilityContext,
    _deterministic_json_dumps,
)

# Crypto utils - Assuming this file is also moved to core/capabilities/
from .crypto_utils import (  # Import the dummy getter for issuer demo
    CryptoError,
    KeyManagementError,
    SigningError,
    _get_dummy_private_key,
    generate_oqs_keypair,
    get_public_key,
    sign_capability_data,  # Import for verification example
)

logger = logging.getLogger(__name__)  # Added logger


class CapabilityIssuer:
    """Placeholder service responsible for generating signed CapabilityContexts."""

    def __init__(self):
        """Initializes the CapabilityIssuer."""
        logging.info("CapabilityIssuer initialized.")
        # In a real system, this might load issuer keys or connect to a KMS

    def _ensure_issuer_keys(self, issuer_id: str):
        """Generates keys for an issuer if they don't exist using crypto_utils."""
        if not get_public_key(issuer_id):
            logging.info(
                f"Keys not found for issuer {issuer_id}, generating keys via crypto_utils."
            )
            try:
                keypair = generate_oqs_keypair(issuer_id)
                if keypair:
                    logging.warning(
                        f"Issuer {issuer_id} generated new keys. Private key returned - SECURE STORAGE IS REQUIRED."
                    )
            except KeyManagementError as e:
                logging.error(f"Failed to ensure keys for issuer {issuer_id}: {e}")
                raise  # Re-raise key management errors as they are critical
            except CryptoError as e:
                logging.error(
                    f"A crypto error occurred ensuring keys for {issuer_id}: {e}"
                )
                raise  # Re-raise other crypto errors

    def _prepare_signing_data(self, context: CapabilityContext) -> bytes:
        """
        Serializes the CapabilityContext data (excluding signature) deterministically.
        Uses the helper from caw.schemas to ensure consistency.
        """
        # Create a dictionary of all fields *except* signature and signature_algorithm
        # Use fields(context) to get defined fields
        data_to_sign = {
            f.name: getattr(context, f.name)
            for f in fields(context)
            if f.name not in ("signature", "signature_algorithm")
        }
        try:
            # Use the centralized deterministic JSON dumper
            serialized_data = _deterministic_json_dumps(data_to_sign)
            logging.debug(
                f"Data prepared for signing ({len(serialized_data)} bytes): {serialized_data}"
            )
            return serialized_data.encode("utf-8")
        except Exception as e:
            logging.exception(f"Error serializing CapabilityContext for signing: {e}")
            raise ValueError("Failed to prepare context data for signing") from e

    def issue_signed_context(
        self,
        issuer_id: str,
        capabilities_to_grant: Set[Capability],
        request_id: Optional[UUID] = None,
    ) -> Optional[CapabilityContext]:
        """
        Creates and signs a CapabilityContext for a given set of capabilities.

        Args:
            issuer_id: The identifier of the entity issuing the capabilities.
            capabilities_to_grant: The set of Capability objects to include.
            request_id: Optional specific request ID to associate.

        Returns:
            A signed CapabilityContext object, or None if signing fails.
        """
        logging.info(
            f"Attempting to issue signed capability context for issuer {issuer_id} with {len(capabilities_to_grant)} capabilities."
        )
        try:
            self._ensure_issuer_keys(issuer_id)
        except CryptoError as e:
            logging.error(f"Cannot issue context for {issuer_id} due to key error: {e}")
            return None

        private_key = _get_dummy_private_key(issuer_id)
        if not private_key:
            logging.error(
                f"CRITICAL: Could not retrieve private key for issuer {issuer_id}. Cannot sign context."
            )
            return None

        req_id = request_id or uuid4()
        timestamp = datetime.now(timezone.utc)

        # Create the unsigned context first
        unsigned_context = CapabilityContext(
            request_id=req_id,
            timestamp=timestamp,
            granted_capabilities=frozenset(capabilities_to_grant),
            issuer_id=issuer_id,
            signature=None,  # Signature added after signing
            signature_algorithm=None,  # Algorithm added after signing
        )

        try:
            data_to_sign_bytes = self._prepare_signing_data(unsigned_context)
        except ValueError:
            logging.error(f"Failed to prepare signing data for issuer {issuer_id}.")
            return None

        signing_result: Optional[Tuple[bytes, str]] = None  # Expect bytes, str
        try:
            signature_bytes, algorithm = sign_capability_data(
                data_to_sign=data_to_sign_bytes,
                private_key=private_key,
                issuer_id_for_dummy=issuer_id,
            )
            signing_result = (signature_bytes, algorithm)

        except SigningError as e:
            logging.error(f"Failed to sign capability data for issuer {issuer_id}: {e}")
            return None
        except CryptoError as e:
            logging.error(
                f"A crypto error occurred during signing for {issuer_id}: {e}"
            )
            return None

        if not signing_result:
            logging.error(
                f"Signing process failed silently for issuer {issuer_id}. Cannot issue context."
            )
            return None

        signature_bytes, algorithm = signing_result

        # Create the final signed context using replace (since it's frozen)
        signed_context = dataclasses.replace(
            unsigned_context, signature=signature_bytes, signature_algorithm=algorithm
        )

        logging.info(f"Successfully issued signed CapabilityContext: {signed_context}")
        return signed_context


# Example Usage (Keep commented out or move to tests)
# if __name__ == '__main__':
#     from ..caw.schemas import EffectCategory # Example

#     logging.basicConfig(level=logging.DEBUG)
#     issuer_service = CapabilityIssuer()

#     # Example capabilities to grant
#     caps = {
#         Capability(...), # Need full definition based on caw.schemas
#         Capability(...)
#     }

#     issued_context = issuer_service.issue_signed_context("test_granter", caps)

#     if issued_context:
#         print("\nIssued Context:")
#         print(issued_context)
#         print(f"Signature: {issued_context.signature!r}")

#         # Simulate Verification
#         print("\nSimulating Verification (using crypto_utils):")
#         try:
#             # Re-prepare data exactly as the issuer did
#             data_to_verify_bytes = issuer_service._prepare_signing_data(issued_context)

#             # Note: verify_capability_signature might need adjustment depending on its final API
#             is_valid = verify_capability_signature(
#                 signed_data=data_to_verify_bytes,
#                 signature=Signature(issued_context.signature), # Signature type if defined
#                 issuer_id=issued_context.issuer_id,
#                 algorithm=issued_context.signature_algorithm
#             )
#             print(f"Verification attempt result: {is_valid}") # Should be True if crypto works
#         except Exception as e:
#             print(f"Error during verification simulation: {e}")
#     else:
#         print("\nFailed to issue context.")
