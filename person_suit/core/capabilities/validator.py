# -*- coding: utf-8 -*-
"""
Implementation of the Capability Validator.

This module provides the concrete implementation for validating CAW Capability
tokens against proposed effects, current state, and context, adhering to the
design outlined in docs/design/Capability_Management_Design.md.

Related Files:
- schemas.python_schema.py
- docs/design/Capability_Management_Design.md
- person_suit.core.actors.central_state_actor.py (Uses this validator)
"""

import fnmatch  # For wildcard pattern matching
import logging
import time
from collections import defaultdict, deque
from typing import DefaultDict, Deque, Tuple

from cryptography.exceptions import InvalidSignature

# Cryptography
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import ec, padding, rsa, utils

from ..caw.schemas import (  # Type hint only, state access might be limited; To potentially return specific errors; Import helper for preparing data
    BaseEffect,
    Capability,
    ConstraintRule,
    Context,
    DualInformation,
    PermissionRule,
    RejectionErrorType,
)

# Placeholder for the actual interface definition if needed separately
# from person_suit.core.actors.central_state_actor import CapabilityValidatorInterface

# --- Cryptographic Verification Helper --- #


def _get_public_key_pem_for_granter(granter_id: str) -> Optional[bytes]:
    """
    Placeholder function to retrieve the PEM-encoded public key for a given granter.

    In a real system, this would interact with a key management service, database,
    or configuration to securely fetch the appropriate public key.
    Returning None indicates the key could not be found.

    *** TEMPORARY IMPLEMENTATION FOR TESTING ***
    Checks for a specific granter_id ('test_granter') and attempts to load
    a corresponding public key file ('test_granter_pub.pem') from the current
    working directory.
    """
    # Specific ID for the temporary test key
    test_granter_id = "test_granter"
    test_public_key_file = "test_granter_pub.pem"  # Relative to workspace root

    if granter_id == test_granter_id:
        logging.debug(
            f"Attempting to load temporary public key for {granter_id} from {test_public_key_file}"
        )
        try:
            # Ensure the path is correct relative to where the script is run from
            # For consistency, assume it's relative to the workspace root.
            # CWD might vary depending on execution context.
            # Using a simple relative path here.
            with open(test_public_key_file, "rb") as key_file:
                key_bytes = key_file.read()
                logging.info(
                    f"Successfully loaded test public key for granter {granter_id}"
                )
                return key_bytes
        except FileNotFoundError:
            logging.error(
                f"Test public key file '{test_public_key_file}' not found for granter {granter_id}. Generate keys and place file in workspace root."
            )
            return None
        except Exception as e:
            logging.exception(
                f"Error reading test public key file '{test_public_key_file}' for granter {granter_id}: {e}"
            )
            return None
    else:
        logging.warning(
            f"No public key retrieval configured for granter_id '{granter_id}'. Returning None."
        )
        return None


def verify_capability_signature(capability: Capability, public_key_pem: bytes) -> bool:
    """
    Verifies the capability's signature using the provided public key.

    Args:
        capability: The Capability object.
        public_key_pem: The PEM-encoded public key of the granter.

    Returns:
        True if the signature is valid, False otherwise.
    """
    try:
        public_key = serialization.load_pem_public_key(public_key_pem)
        data_to_verify = capability._prepare_for_signing()

        # Determine padding/algorithm based on key type
        if isinstance(public_key, rsa.RSAPublicKey):
            padding_algo = padding.PSS(
                mgf=padding.MGF1(hashes.SHA256()), salt_length=padding.PSS.MAX_LENGTH
            )
            hash_algo = hashes.SHA256()
        elif isinstance(public_key, ec.EllipticCurvePublicKey):
            padding_algo = ec.ECDSA(
                utils.Prehashed(hashes.SHA256())
            )  # Sign hash directly
            hash_algo = hashes.SHA256()  # Need to hash data first for ECDSA verify
            # Important: ECDSA's verify method expects the hash of the data, not the data itself.
            hasher = hashes.Hash(hash_algo)
            hasher.update(data_to_verify)
            data_to_verify = hasher.finalize()  # Verify the hash
        else:
            logging.error(
                f"Unsupported public key type for verification: {type(public_key)}"
            )
            return False

        # Perform verification
        public_key.verify(
            capability.signature,
            data_to_verify,  # This is the hash for ECDSA
            padding_algo,  # This is the ECDSA object for EC keys
            hash_algo,  # This is ignored by ECDSA verify, but required by API
        )
        return True  # Verification successful

    except InvalidSignature:
        logging.warning(f"Invalid signature for capability {capability.capability_id}")
        return False
    except ValueError as e:
        # Catches errors like incorrect key format/size
        logging.error(
            f"ValueError during signature verification for {capability.capability_id}: {e}"
        )
        return False
    except Exception as e:
        # Catch other unexpected errors during verification
        logging.exception(
            f"Unexpected error verifying signature for {capability.capability_id}: {e}"
        )
        return False


class CapabilityValidationError(Exception):
    """Custom exception for validation failures."""

    def __init__(self, message: str, error_type: RejectionErrorType):
        super().__init__(message)
        self.error_type = error_type


class CapabilityValidator:  # Implements CapabilityValidatorInterface conceptually
    """
    Validates CAW Capability tokens.
    """

    def __init__(self):
        # Example state for rate limiting (NOT thread-safe without locks!)
        # { (capability_id, rule_hash) -> Deque[timestamp] }
        self._rate_limit_timestamps: DefaultDict[Tuple[str, int], Deque[float]] = (
            defaultdict(deque)
        )

    async def validate(
        self,
        capability: Capability,
        effect: BaseEffect,
        current_state: DualInformation,
        context: Context,
    ) -> bool:
        """
        Validates a capability token against an effect, state, and context.

        Args:
            capability: The Capability token presented.
            effect: The BaseEffect being attempted.
            current_state: The current DualInformation state (may be used by constraints).
            context: The current Context (may be used by constraints).

        Returns:
            True if the capability is valid for the request, False otherwise.

        Raises:
            CapabilityValidationError: Can optionally raise specific errors instead of returning False.
                                      (Current implementation returns bool).
        """
        try:
            # 1. Check Signature (Using new verification helper)
            if not self._verify_signature(capability):
                # Keep log inside _verify_signature for specifics
                # raise CapabilityValidationError("Invalid signature", RejectionErrorType.SIGNATURE_VERIFICATION_FAILED)
                return False  # Validation failed

            # 2. Check Expiration
            if capability.is_expired():
                logging.warning(f"Capability {capability.capability_id} has expired.")
                # raise CapabilityValidationError("Capability expired", RejectionErrorType.CAPABILITY_EXPIRED)
                return False

            # 3. Check Permissions
            if not self._check_permissions(capability.permissions, effect):
                logging.warning(
                    f"Capability {capability.capability_id} permissions do not match effect {effect.effect_type} on target {effect.target_entity_id}."
                )
                # raise CapabilityValidationError("Permission denied", RejectionErrorType.CAPABILITY_INVALID)
                return False

            # 4. Check Constraints
            if not await self._check_constraints(capability, context, current_state):
                logging.warning(
                    f"Capability {capability.capability_id} constraints not met."
                )
                # Specific constraint type violation could be raised here
                # raise CapabilityValidationError("Constraint violation", RejectionErrorType.CONSTRAINT_VIOLATION)
                return False

            # If all checks pass
            logging.info(
                f"Capability {capability.capability_id} validation successful for effect {effect.effect_type}."
            )
            return True

        except Exception as e:
            # Catch potential errors during validation itself
            logging.exception(
                f"Error during capability validation for {capability.capability_id}: {e}"
            )
            return False

    def _verify_signature(self, capability: Capability) -> bool:
        """Verifies the cryptographic signature using the granter's public key."""
        # 1. Get Granter's Public Key
        public_key_pem = _get_public_key_pem_for_granter(capability.granter_id)
        if not public_key_pem:
            logging.error(
                f"Could not retrieve public key for granter {capability.granter_id}. Cannot verify capability {capability.capability_id}."
            )
            return False

        # 2. Perform Verification
        is_valid = verify_capability_signature(capability, public_key_pem)
        if not is_valid:
            # Specific reason logged within verify_capability_signature
            pass
        return is_valid

    def _check_permissions(
        self, permissions: list[PermissionRule], effect: BaseEffect
    ) -> bool:
        """Checks if any permission rule allows the given effect, including parameter constraints."""
        for rule in permissions:
            effect_match = fnmatch.fnmatch(effect.effect_type, rule.effect_type_pattern)
            target_match = fnmatch.fnmatch(
                str(effect.target_entity_id), rule.target_entity_pattern
            )

            params_match = True  # Assume true initially
            if rule.parameter_constraints:
                # --- Parameter Constraint Check ---
                # Example: Check if effect parameters match required values/patterns
                for key, required_value in rule.parameter_constraints.items():
                    effect_value = effect.parameters.get(key)
                    if effect_value is None:
                        params_match = False
                        logging.debug(
                            f"Parameter constraint failed: Missing key '{key}' in effect params."
                        )
                        break  # Key missing, rule doesn't match

                    # Simple equality check - could be extended with patterns, ranges etc.
                    if (
                        isinstance(required_value, str) and "*" in required_value
                    ):  # Basic wildcard support
                        if not fnmatch.fnmatch(str(effect_value), required_value):
                            params_match = False
                            logging.debug(
                                f"Parameter constraint failed: Value '{effect_value}' for key '{key}' does not match pattern '{required_value}'."
                            )
                            break
                    elif effect_value != required_value:
                        params_match = False
                        logging.debug(
                            f"Parameter constraint failed: Value '{effect_value}' for key '{key}' does not equal required '{required_value}'."
                        )
                        break  # Value doesn't match
                if not params_match:
                    continue  # Try the next permission rule

            if effect_match and target_match and params_match:
                logging.debug(f"Permission rule matched: {rule}")
                return True  # Found a matching rule
        return False  # No matching rule found

    async def _check_constraints(
        self, capability: Capability, context: Context, current_state: DualInformation
    ) -> bool:
        """Checks if all constraint rules are satisfied."""
        for rule in capability.constraints:
            # Pass capability for stateful checks like rate limiting
            if not await self._evaluate_constraint(
                rule, capability, context, current_state
            ):
                logging.debug(f"Constraint rule failed: {rule}")
                return False  # One failed constraint invalidates the capability use
        return True  # All constraints passed

    async def _evaluate_constraint(
        self,
        rule: ConstraintRule,
        capability: Capability,
        context: Context,
        current_state: DualInformation,
    ) -> bool:
        """Evaluates a single constraint rule."""
        try:
            params = rule.parameters
            if rule.constraint_type == "TIME_BOUND":
                now = time.time()
                after = params.get("after")
                before = params.get("before")
                if after is not None and now < after:
                    return False
                if before is not None and now >= before:
                    return False
                return True

            elif rule.constraint_type == "CONTEXT_MATCH":
                # Refined CONTEXT_MATCH
                required_key = params.get("required_key")
                required_value_pattern = params.get(
                    "required_value_pattern", "*"
                )  # Default to any value if pattern omitted
                context_location = params.get(
                    "context_location", "any"
                )  # Where to look: 'any', 'root', 'custom', 'situational', 'acf'

                if not required_key:
                    logging.warning(
                        f"Invalid CONTEXT_MATCH params: missing 'required_key' in {params}"
                    )
                    return False

                context_value = None
                found = False

                # Check specific locations based on 'context_location'
                if context_location in ["root", "any"] and hasattr(
                    context, required_key
                ):
                    context_value = getattr(context, required_key)
                    found = True
                elif (
                    context_location in ["custom", "any"]
                    and required_key in context.custom_context
                ):
                    context_value = context.custom_context.get(required_key)
                    found = True
                elif (
                    context_location in ["situational", "any"]
                    and required_key in context.situational_awareness
                ):
                    context_value = context.situational_awareness.get(required_key)
                    found = True
                elif context_location in ["acf", "any"] and hasattr(
                    context.acf_params, required_key
                ):
                    context_value = getattr(context.acf_params, required_key)
                    found = True
                # Add checks for emotional/neurochemical analogs if needed

                if not found:
                    return False  # Key not found in specified location(s)

                # Check value against pattern
                return fnmatch.fnmatch(str(context_value), required_value_pattern)

            elif rule.constraint_type == "ACF_LEVEL_MAX":
                max_res = params.get("wave_resolution_scale")
                if (
                    max_res is not None
                    and context.acf_params.wave_resolution_scale > max_res
                ):
                    return False
                # Add checks for other ACF params if needed
                return True

            elif rule.constraint_type == "ACF_LEVEL_MIN":
                min_res = params.get("wave_resolution_scale")
                if (
                    min_res is not None
                    and context.acf_params.wave_resolution_scale < min_res
                ):
                    return False
                return True

            elif rule.constraint_type == "RATE_LIMIT":
                # Placeholder implementation - Requires stateful tracking
                # This naive version is NOT suitable for production without proper locking & eviction
                period = params.get("period_seconds")
                max_calls = params.get("max_calls")
                if period is None or max_calls is None:
                    logging.warning(f"Invalid RATE_LIMIT params: {params}")
                    return False

                now = time.time()
                # Use capability ID and hash of rule parameters as key for tracking
                rule_key = (capability.capability_id, hash(frozenset(params.items())))

                timestamps = self._rate_limit_timestamps[rule_key]

                # Remove timestamps older than the period
                while timestamps and timestamps[0] <= now - period:
                    timestamps.popleft()

                # Check if max calls exceeded
                if len(timestamps) >= max_calls:
                    logging.debug(
                        f"Rate limit exceeded for cap {capability.capability_id}, rule {rule.parameters}"
                    )
                    return False
                else:
                    # Record current call timestamp (state mutation!)
                    timestamps.append(now)
                    logging.debug(
                        f"Rate limit check passed for cap {capability.capability_id}, rule {rule.parameters}. Count: {len(timestamps)}"
                    )
                    return True

            # --- Add evaluation logic for other constraint types --- #
            # Example: STATE_MATCH (requires querying current_state)
            elif rule.constraint_type == "STATE_MATCH":
                query_path = params.get(
                    "query_path"
                )  # e.g., "particle_state.nodes.node_xyz.metadata.status"
                expected_value = params.get("expected_value")
                if not query_path or expected_value is None:
                    logging.warning(f"Invalid STATE_MATCH params: {params}")
                    return False
                # Implement a safe way to query the nested state structure
                # This is non-trivial for immutable hypergraphs/tensors.
                # Needs a dedicated query helper.
                # current_value = _query_state(current_state, query_path)
                # return current_value == expected_value
                logging.warning(
                    "STATE_MATCH constraint evaluation not fully implemented."
                )
                return True  # Placeholder: Assume pass for now

            else:
                logging.warning(
                    f"Unsupported constraint type encountered: {rule.constraint_type}"
                )
                return False  # Unknown constraint type fails validation

        except Exception as e:
            logging.exception(f"Error evaluating constraint rule {rule}: {e}")
            return False
