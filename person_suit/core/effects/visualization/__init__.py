"""
Visualization Tools for Effect Systems
==================================

This package provides visualization tools for the Effect Systems framework,
allowing for visualization of effects, information flows, and system behavior.

Related Files:
- person_suit/core/infrastructure/effects/context_effects.py: Context-aware effects
- person_suit/core/infrastructure/effects/core.py: Core effect tracking
- person_suit/core/infrastructure/wave/core.py: Wave-based context representation

Dependencies:
- typing>=4.0.0: For type annotations
- logging>=0.5.1.2: For logging functionality
- json>=2.0.9: For JSON serialization
"""

from ...infrastructure.effects.visualization.dashboard import (
    EffectDashboard,
    generate_effect_dashboard,
    get_effect_dashboard,
)
from ...infrastructure.effects.visualization.information_flow_visualizer import (
    InformationFlowVisualizer,
    get_flow_visualizer,
)

__all__ = [
    "InformationFlowVisualizer",
    "get_flow_visualizer",
    "EffectDashboard",
    "get_effect_dashboard",
    "generate_effect_dashboard",
]
