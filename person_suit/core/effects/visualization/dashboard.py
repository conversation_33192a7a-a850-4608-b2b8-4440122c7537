"""
File: dashboard.py

Purpose:
    Implements a dashboard for visualizing context-aware effects within the Person Suit system.

This module provides tools for visualizing the propagation and transformation of effects
across different contexts, supporting the foundational Contextual Adaptive Wave Programming (CAW) paradigm.
By making effect flows explicit, the dashboard aids in understanding duality (wave/potential vs. particle/actual)
and adaptive computation within the system's conceptual spacetime.

Related Files:
- person_suit/core/infrastructure/effects/context_effects.py: Context-aware effects
- person_suit/core/infrastructure/effects/core.py: Core effect tracking
- person_suit/core/infrastructure/wave/core.py: Wave-based context representation

Dependencies:
- typing>=4.0.0: For type annotations
- logging>=0.5.1.2: For logging functionality
- json>=2.0.9: For JSON serialization
"""

import json
import logging
import os
import time
from typing import Any, Dict, List, Optional

from ...infrastructure.effects.context_effects import (
    ContextualEffectTracker,
    EffectAnalysisType,
    get_contextual_effect_tracker,
)
from ...infrastructure.wave.core import Context

# Configure logger
logger = logging.getLogger(__name__)


class EffectDashboard:
    """
    Dashboard for visualizing context-aware effects.

    This dashboard provides interactive visualizations of effect propagation, transitions,
    and contextual dependencies, supporting CAW principles of duality and context propagation.
    It enables developers and analysts to observe how effects evolve and interact within
    different conceptual contexts, facilitating adaptive computation and system introspection.

    Attributes:
        tracker: The ContextualEffectTracker instance used for effect data aggregation.
        output_dir: Directory where dashboard HTML files are generated.
    """

    def __init__(self, tracker: Optional[ContextualEffectTracker] = None):
        """
        Initialize the effect dashboard.

        Args:
            tracker: The effect tracker to use (default: global tracker)
        """
        self.tracker = tracker or get_contextual_effect_tracker()
        self.output_dir = os.path.join(os.getcwd(), "effect_dashboard")

        # Create output directory if it doesn't exist
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)

        logger.info(
            f"Effect dashboard initialized with output directory: {self.output_dir}"
        )

    def generate_dashboard(self, contexts: Optional[List[Context]] = None) -> str:
        """
        Generate a context-aware effect dashboard as an HTML file.

        This method visualizes the flow and transformation of effects across specified
        contexts, supporting CAW's emphasis on context propagation and adaptive computation.

        Args:
            contexts: Optional list of Context objects to filter the visualization.
                      If None, includes all available contexts.

        Returns:
            Path to the generated dashboard HTML file.

        Raises:
            OSError: If the output directory cannot be created or written to.
        """
        # Generate dashboard data
        data = self._generate_dashboard_data(contexts)

        # Generate HTML
        html = self._generate_html(data)

        # Write HTML to file
        timestamp = int(time.time())
        output_path = os.path.join(
            self.output_dir, f"effect_dashboard_{timestamp}.html"
        )
        with open(output_path, "w", encoding="utf-8") as f:
            f.write(html)

        logger.info(f"Generated effect dashboard: {output_path}")
        return output_path

    def _generate_dashboard_data(
        self, contexts: Optional[List[Context]] = None
    ) -> Dict[str, Any]:
        """
        Generate dashboard data.

        Args:
            contexts: The contexts to include in the dashboard

        Returns:
            Dashboard data
        """
        # Get all effects
        all_effects = {}
        for context_key, effects in self.tracker._context_effects.items():
            if contexts is None or any(
                context.domain == context_key for context in contexts
            ):
                all_effects[context_key] = [
                    {
                        "type": effect.effect_type.name,
                        "source": effect.source,
                        "timestamp": effect.timestamp,
                        "metadata": effect.metadata,
                    }
                    for effect in effects
                ]

        # Get all transitions
        transitions = []
        for from_effect, to_effect, weight in self.tracker._effect_transitions:
            from_context = (
                from_effect.context.domain if from_effect.context else "default"
            )
            to_context = to_effect.context.domain if to_effect.context else "default"

            if contexts is None or any(
                context.domain in (from_context, to_context) for context in contexts
            ):
                transitions.append(
                    {
                        "from": {
                            "type": from_effect.effect_type.name,
                            "source": from_effect.source,
                            "context": from_context,
                        },
                        "to": {
                            "type": to_effect.effect_type.name,
                            "source": to_effect.source,
                            "context": to_context,
                        },
                        "weight": weight,
                    }
                )

        # Get all chains
        chains = {}
        for context_key, context_chains in self.tracker._effect_chains.items():
            if contexts is None or any(
                context.domain == context_key for context in contexts
            ):
                chains[context_key] = [
                    [
                        {"type": effect.effect_type.name, "source": effect.source}
                        for effect in chain
                    ]
                    for chain in context_chains
                ]

        # Perform analyses
        analyses = {}
        analysis_types = [
            EffectAnalysisType.FLOW,
            EffectAnalysisType.DEPENDENCY,
            EffectAnalysisType.CONTEXT_TRANSITION,
            EffectAnalysisType.INTERFERENCE,
            EffectAnalysisType.COMPOSITION,
        ]

        for analysis_type in analysis_types:
            if contexts:
                for context in contexts:
                    result = self.tracker.analyze_effects(analysis_type, context)
                    if context.domain not in analyses:
                        analyses[context.domain] = {}
                    analyses[context.domain][analysis_type.name] = result.data
            else:
                result = self.tracker.analyze_effects(analysis_type)
                analyses["global"] = analyses.get("global", {})
                analyses["global"][analysis_type.name] = result.data

        # Combine all data
        return {
            "timestamp": time.time(),
            "effects": all_effects,
            "transitions": transitions,
            "chains": chains,
            "analyses": analyses,
        }

    def _generate_html(self, data: Dict[str, Any]) -> str:
        """
        Generate HTML for the dashboard.

        Args:
            data: Dashboard data

        Returns:
            HTML string
        """
        # Convert data to JSON for JavaScript
        try:
            json_data = json.dumps(data, indent=2, default=str)
        except TypeError as e:
            logger.error(f"Error serializing dashboard data to JSON: {e}")
            json_data = json.dumps(
                {"error": "Failed to serialize dashboard data", "details": str(e)}
            )

        # Generate HTML
        html = f"""<!DOCTYPE html>
<html>
<head>
    <title>Effect Dashboard</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body {{
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }}

        .container {{
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }}

        .header {{
            background-color: #333;
            color: white;
            padding: 20px;
            text-align: center;
        }}

        .section {{
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            padding: 20px;
        }}

        .section-title {{
            border-bottom: 1px solid #eee;
            margin-top: 0;
            padding-bottom: 10px;
        }}

        .tab {{
            overflow: hidden;
            border: 1px solid #ccc;
            background-color: #f1f1f1;
            border-radius: 5px 5px 0 0;
        }}

        .tab button {{
            background-color: inherit;
            float: left;
            border: none;
            outline: none;
            cursor: pointer;
            padding: 14px 16px;
            transition: 0.3s;
            font-size: 16px;
        }}

        .tab button:hover {{
            background-color: #ddd;
        }}

        .tab button.active {{
            background-color: #ccc;
        }}

        .tabcontent {{
            display: none;
            padding: 20px;
            border: 1px solid #ccc;
            border-top: none;
            border-radius: 0 0 5px 5px;
        }}

        .chart-container {{
            width: 100%;
            height: 400px;
        }}

        table {{
            width: 100%;
            border-collapse: collapse;
        }}

        th, td {{
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }}

        tr:hover {{
            background-color: #f5f5f5;
        }}

        th {{
            background-color: #f2f2f2;
        }}

        .context-badge {{
            display: inline-block;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            color: white;
            background-color: #007bff;
            margin-right: 5px;
        }}

        .effect-type-badge {{
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
            color: white;
            margin-right: 5px;
        }}

        .effect-IO {{ background-color: #28a745; }}
        .effect-DATABASE {{ background-color: #dc3545; }}
        .effect-NETWORK {{ background-color: #fd7e14; }}
        .effect-STATE {{ background-color: #6f42c1; }}
        .effect-MEMORY {{ background-color: #e83e8c; }}
        .effect-SECURITY {{ background-color: #20c997; }}
        .effect-LOGGING {{ background-color: #17a2b8; }}
        .effect-TELEMETRY {{ background-color: #6c757d; }}
        .effect-COMPUTATION {{ background-color: #007bff; }}
        .effect-CONTEXT_SWITCH {{ background-color: #ffc107; color: #333; }}
        .effect-DEFAULT {{ background-color: #adb5bd; }}
    </style>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vis-network@9.1.2/dist/vis-network.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vis-data@7.1.4/dist/vis-data.min.js"></script>
</head>
<body>
    <div class="header">
        <h1>Effect Dashboard</h1>
        <p>Generated at {time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(data.get("timestamp", time.time())))}</p>
    </div>

    <div class="container">
        <div class="section">
            <h2 class="section-title">Overview</h2>
            <div class="chart-container">
                <canvas id="effectTypesChart"></canvas>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">Effect Flow</h2>
            <div id="flowNetwork" class="chart-container"></div>
        </div>
    </div>

    <script>
        // Dashboard data
        const data = {json_data};

        // Initialize charts when the page loads
        document.addEventListener('DOMContentLoaded', function() {{
            // Initialize charts
            initializeOverviewChart();
            initializeFlowNetwork();
        }});

        // Initialize overview chart
        function initializeOverviewChart() {{
            const effectTypes = {{}};

            // Count effect types
            for (const context in data.effects) {{
                for (const effect of data.effects[context]) {{
                    if (!effectTypes[effect.type]) {{
                        effectTypes[effect.type] = 0;
                    }}
                    effectTypes[effect.type]++;
                }}
            }}

            // Create chart
            const ctx = document.getElementById('effectTypesChart').getContext('2d');
            new Chart(ctx, {{
                type: 'bar',
                data: {{
                    labels: Object.keys(effectTypes),
                    datasets: [{{
                        label: 'Effect Types',
                        data: Object.values(effectTypes),
                        backgroundColor: [
                            '#28a745', '#dc3545', '#fd7e14', '#6f42c1',
                            '#e83e8c', '#20c997', '#17a2b8', '#6c757d',
                            '#007bff', '#ffc107'
                        ]
                    }}]
                }},
                options: {{
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {{
                        y: {{
                            beginAtZero: true,
                            title: {{
                                display: true,
                                text: 'Count'
                            }}
                        }},
                        x: {{
                            title: {{
                                display: true,
                                text: 'Effect Type'
                            }}
                        }}
                    }}
                }}
            }});
        }}

        // Initialize flow network
        function initializeFlowNetwork() {{
            // Create nodes
            const nodes = new vis.DataSet();
            const nodeIds = {{}};
            let nodeId = 1;

            for (const transition of data.transitions) {{
                const fromKey = `${{transition.from.context}}:${{transition.from.type}}:${{transition.from.source}}`;
                const toKey = `${{transition.to.context}}:${{transition.to.type}}:${{transition.to.source}}`;

                if (!nodeIds[fromKey]) {{
                    nodeIds[fromKey] = nodeId++;
                    nodes.add({{
                        id: nodeIds[fromKey],
                        label: transition.from.type + '\\n' + transition.from.source.split('.').pop(),
                        group: transition.from.context,
                        title: transition.from.source + ' (' + transition.from.context + ')'
                    }});
                }}

                if (!nodeIds[toKey]) {{
                    nodeIds[toKey] = nodeId++;
                    nodes.add({{
                        id: nodeIds[toKey],
                        label: transition.to.type + '\\n' + transition.to.source.split('.').pop(),
                        group: transition.to.context,
                        title: transition.to.source + ' (' + transition.to.context + ')'
                    }});
                }}
            }}

            // Create edges
            const edges = new vis.DataSet();
            for (const transition of data.transitions) {{
                const fromKey = `${{transition.from.context}}:${{transition.from.type}}:${{transition.from.source}}`;
                const toKey = `${{transition.to.context}}:${{transition.to.type}}:${{transition.to.source}}`;

                edges.add({{
                    from: nodeIds[fromKey],
                    to: nodeIds[toKey],
                    value: transition.weight,
                    title: 'Weight: ' + transition.weight
                }});
            }}

            // Create network
            const container = document.getElementById('flowNetwork');
            const networkData = {{
                nodes: nodes,
                edges: edges
            }};
            const options = {{
                nodes: {{
                    shape: 'box',
                    font: {{
                        size: 12
                    }},
                    margin: 10
                }},
                edges: {{
                    arrows: {{
                        to: {{
                            enabled: true,
                            scaleFactor: 0.5
                        }}
                    }},
                    smooth: {{
                        type: 'continuous'
                    }}
                }},
                physics: {{
                    stabilization: {{
                        iterations: 100
                    }},
                    barnesHut: {{
                        gravitationalConstant: -2000,
                        centralGravity: 0.3,
                        springLength: 150,
                        springConstant: 0.04,
                        damping: 0.09
                    }}
                }},
                groups: {{
                    standard: {{
                        color: {{
                            background: '#007bff',
                            border: '#0056b3',
                            highlight: {{
                                background: '#0069d9',
                                border: '#0056b3'
                            }}
                        }},
                        borderWidth: 2,
                        shadow: true
                    }},
                    high_priority: {{
                        color: {{
                            background: '#dc3545',
                            border: '#bd2130',
                            highlight: {{
                                background: '#c82333',
                                border: '#bd2130'
                            }}
                        }},
                        borderWidth: 2,
                        shadow: true
                    }},
                    low_priority: {{
                        color: {{
                            background: '#28a745',
                            border: '#1e7e34',
                            highlight: {{
                                background: '#218838',
                                border: '#1e7e34'
                            }}
                        }},
                        borderWidth: 2,
                        shadow: true
                    }},
                    emergency: {{
                        color: {{
                            background: '#ffc107',
                            border: '#d39e00',
                            highlight: {{
                                background: '#e0a800',
                                border: '#d39e00'
                            }}
                        }},
                        borderWidth: 2,
                        shadow: true
                    }},
                    default: {{
                        color: {{
                            background: '#6c757d',
                            border: '#545b62',
                            highlight: {{
                                background: '#5a6268',
                                border: '#545b62'
                            }}
                        }},
                        borderWidth: 2,
                        shadow: true
                    }}
                }}
            }};
            new vis.Network(container, networkData, options);
        }}
    </script>
</body>
</html>
"""

        return html


# Global instance
_effect_dashboard = None


def get_effect_dashboard() -> EffectDashboard:
    """
    Retrieve the global EffectDashboard instance.

    Ensures a singleton dashboard instance for consistent effect visualization
    across the application, supporting CAW's context propagation.

    Returns:
        EffectDashboard: The global dashboard instance.
    """
    global _effect_dashboard
    if _effect_dashboard is None:
        _effect_dashboard = EffectDashboard()

    return _effect_dashboard


def generate_effect_dashboard(contexts: Optional[List[Context]] = None) -> str:
    """
    Generate and return the path to a context-aware effect dashboard HTML file.

    This function provides a convenient entry point for generating effect
    visualizations, supporting CAW-aligned introspection and analysis.

    Args:
        contexts: Optional list of Context objects to include in the dashboard.

    Returns:
        str: Path to the generated dashboard HTML file.
    """
    dashboard = get_effect_dashboard()
    return dashboard.generate_dashboard(contexts)
