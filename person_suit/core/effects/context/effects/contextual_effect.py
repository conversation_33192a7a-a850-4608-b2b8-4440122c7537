"""
File: contextual_effect.py
Purpose: Defines the ContextualEffect class for CAW-aware effect tracking.

This class extends the base Effect to include context information and wave properties,
enabling context-sensitive effect tracking and interference-based analysis as
part of the Contextual Adaptive Wave (CAW) programming paradigm.

Related Files:
- person_suit/core/effects/core.py: Base Effect class definition.
- person_suit/core/effects/types.py: Effect types definitions.
- person_suit/core/infrastructure/wave/core.py: Context and wave-related classes.
"""

import time
from typing import Any, Dict, Optional

# Update imports: Remove try/except and import directly from caw.schemas
from ....caw.schemas import (  # Removed Information, Interpretation
    Context,
    WaveState,
)
from ...core import Effect, EffectType

# from person_suit.core.effects.interfaces.context import ContextProtocol # Not needed if using concrete Context


class ContextualEffect(Effect):
    """
    Effect with context information and optional CAW wave properties.

    This class extends the standard Effect class with context information and
    wave properties, enabling context-sensitive effect tracking and interference-based
    analysis. It integrates with the CAW paradigm to provide rich contextual effect
    tracking and handling.
    """

    def __init__(
        self,
        effect_type: EffectType,
        source: str,
        timestamp: Optional[float] = None,
        context: Optional[Context] = None,
        metadata: Optional[Dict[str, Any]] = None,
        # Optional wave properties - consider if these are truly needed on every effect
        # or if they emerge from the effect's interaction in context via dynamics.
        # Keeping them for now, but mark as potentially deprecated/simplified later.
        wave_amplitude: Optional[float] = None,  # Make optional
        wave_phase: Optional[float] = None,  # Make optional
        wave_function: Optional[WaveState] = None,  # Changed type hint to WaveState
    ):
        """
        Initialize a contextual effect.

        Args:
            effect_type: The type of the effect
            source: The source of the effect
            timestamp: The timestamp of the effect. Defaults to current time.
            context: The CAW context of the effect.
            metadata: Additional metadata for the effect.
            wave_amplitude: Optional amplitude of the effect wave.
            wave_phase: Optional phase of the effect wave.
            wave_function: Optional CAW wave function associated with the effect.
        """
        super().__init__(
            effect_type=effect_type,
            source=source,
            timestamp=timestamp or time.time(),
            metadata=metadata or {},
        )
        self.context: Optional[Context] = context
        # Store optional wave properties
        self.wave_amplitude: Optional[float] = wave_amplitude
        self.wave_phase: Optional[float] = wave_phase
        self.wave_function: Optional[WaveState] = wave_function

    def with_context(self, context: Context) -> "ContextualEffect":
        """
        Create a new effect with the specified context.

        Args:
            context: The context to use.

        Returns:
            A new effect with the specified context.
        """
        # Create a new instance with updated context, copying other fields
        # Use getattr to handle potentially None wave attributes
        return ContextualEffect(
            effect_type=self.effect_type,
            source=self.source,
            timestamp=self.timestamp,
            context=context,
            metadata=self.metadata.copy(),
            wave_amplitude=getattr(self, "wave_amplitude", None),
            wave_phase=getattr(self, "wave_phase", None),
            wave_function=getattr(self, "wave_function", None),
        )

    def __str__(self) -> str:
        """
        Get a string representation of the effect.

        Returns:
            String representation.
        """
        context_str = (
            f", context={self.context.context_id}"
            if self.context and hasattr(self.context, "context_id")
            else ""
        )
        wave_str = ""
        if self.wave_amplitude is not None:
            wave_str += f", amplitude={self.wave_amplitude:.2f}"
        if self.wave_phase is not None:
            wave_str += f", phase={self.wave_phase:.2f}"
        # Use EffectType name if available, otherwise fallback
        effect_type_name = (
            self.effect_type.name
            if hasattr(self.effect_type, "name")
            else str(self.effect_type)
        )
        return (
            f"ContextualEffect(type={effect_type_name}, source={self.source}"
            f"{context_str}{wave_str}, timestamp={self.timestamp})"
        )
