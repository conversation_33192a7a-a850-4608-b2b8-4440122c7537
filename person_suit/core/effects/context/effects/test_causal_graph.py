"""
test_causal_graph.py

Unit tests for CausalGraph, SpacetimeNode, and CausalEdge in the CAW paradigm.
Covers node/edge addition, error handling, and ancestor/descendant traversal.

Run with: pytest test_causal_graph.py
"""

import numpy as np
import pytest

from .causal_edge import (
    CausalEdge,
    CausalEdgeType,
)
from .causal_graph import CausalGraph
from .spacetime_node import SpacetimeNode


def make_node(label: str) -> SpacetimeNode:
    return SpacetimeNode(label=label, state_vector=np.zeros(2048))


def make_edge(src: SpacetimeNode, tgt: SpacetimeNode) -> CausalEdge:
    return CausalEdge(
        source_id=src.node_id, target_id=tgt.node_id, edge_type=CausalEdgeType.CAUSE
    )


def test_add_node_and_get_node() -> None:
    graph = CausalGraph()
    node = make_node("A")
    graph.add_node(node)
    assert graph.get_node(node.node_id) is node
    # Duplicate node
    with pytest.raises(ValueError):
        graph.add_node(node)


def test_add_edge_and_get_edges() -> None:
    graph = CausalGraph()
    n1, n2 = make_node("A"), make_node("B")
    graph.add_node(n1)
    graph.add_node(n2)
    edge = make_edge(n1, n2)
    graph.add_edge(edge)
    assert edge in graph.get_outgoing_edges(n1.node_id)
    assert edge in graph.get_incoming_edges(n2.node_id)
    # Edge with missing node
    n3 = make_node("C")
    bad_edge = make_edge(n1, n3)
    with pytest.raises(KeyError):
        graph.add_edge(bad_edge)


def test_ancestors_and_descendants_simple() -> None:
    graph = CausalGraph()
    n1, n2, n3 = make_node("A"), make_node("B"), make_node("C")
    for n in (n1, n2, n3):
        graph.add_node(n)
    graph.add_edge(make_edge(n1, n2))
    graph.add_edge(make_edge(n2, n3))
    # n1 -> n2 -> n3
    assert graph.get_ancestors(n3.node_id) == {n1.node_id, n2.node_id}
    assert graph.get_descendants(n1.node_id) == {n2.node_id, n3.node_id}


def test_ancestors_and_descendants_with_cycle() -> None:
    graph = CausalGraph()
    n1, n2 = make_node("A"), make_node("B")
    graph.add_node(n1)
    graph.add_node(n2)
    graph.add_edge(make_edge(n1, n2))
    graph.add_edge(make_edge(n2, n1))  # cycle
    # Both are ancestors/descendants of each other
    assert n1.node_id in graph.get_ancestors(n2.node_id)
    assert n2.node_id in graph.get_ancestors(n1.node_id)
    assert n1.node_id in graph.get_descendants(n2.node_id)
    assert n2.node_id in graph.get_descendants(n1.node_id)


def test_max_depth_traversal() -> None:
    graph = CausalGraph()
    nodes = [make_node(str(i)) for i in range(4)]
    for n in nodes:
        graph.add_node(n)
    # 0 -> 1 -> 2 -> 3
    for i in range(3):
        graph.add_edge(make_edge(nodes[i], nodes[i + 1]))
    # Ancestors of 3 with max_depth=1: only 2
    assert graph.get_ancestors(nodes[3].node_id, max_depth=1) == {nodes[2].node_id}
    # Descendants of 0 with max_depth=2: 1 and 2
    assert graph.get_descendants(nodes[0].node_id, max_depth=2) == {
        nodes[1].node_id,
        nodes[2].node_id,
    }
