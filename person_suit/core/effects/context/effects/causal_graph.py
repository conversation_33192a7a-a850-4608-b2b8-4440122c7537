"""
causal_graph.py

Purpose:
    Defines the CausalGraph class, which manages SpacetimeNode and CausalEdge instances for the CAW (Contextual Adaptive Wave Programming) paradigm. This module provides the core data structure for representing and traversing causal relationships, supporting context propagation, duality, and advanced information flow algorithms.

Related Files:
    - spacetime_node.py: Defines the SpacetimeNode class (nodes in the graph).
    - causal_edge.py: Defines the CausalEdge class (edges in the graph).
    - propagation.py: Implements wave/information propagation logic.

Dependencies:
    - typing (standard library)
    - collections (for efficient data structures)
    - spacetime_node.py, causal_edge.py (local)

"""

from __future__ import annotations

from collections import defaultdict
from typing import Dict, List, Optional, Set

from .causal_edge import (
    CausalEdge,
)
from .spacetime_node import SpacetimeNode


class CausalGraph:
    """
    Manages a directed graph of SpacetimeNode and CausalEdge instances for CAW models.

    Attributes:
        nodes (Dict[str, SpacetimeNode]): Mapping from node_id to SpacetimeNode.
        edges (Dict[str, List[CausalEdge]]): Mapping from source node_id to outgoing edges.
        reverse_edges (Dict[str, List[CausalEdge]]): Mapping from target node_id to incoming edges.

    Example:
        >>> graph = CausalGraph()
        >>> node1 = SpacetimeNode(label="A")
        >>> node2 = SpacetimeNode(label="B")
        >>> graph.add_node(node1)
        >>> graph.add_node(node2)
        >>> edge = CausalEdge(source_id=node1.node_id, target_id=node2.node_id, edge_type=CausalEdgeType.CAUSE)
        >>> graph.add_edge(edge)
    """

    def __init__(self) -> None:
        """
        Initializes an empty CausalGraph.
        """
        # TODO: Use efficient data structures for large graphs
        self.nodes: Dict[str, SpacetimeNode] = {}
        self.edges: Dict[str, List[CausalEdge]] = defaultdict(list)
        self.reverse_edges: Dict[str, List[CausalEdge]] = defaultdict(list)

    def add_node(self, node: SpacetimeNode) -> None:
        """
        Adds a SpacetimeNode to the graph.

        Args:
            node: The SpacetimeNode to add.

        Raises:
            ValueError: If a node with the same node_id already exists.
        """
        if node.node_id in self.nodes:
            raise ValueError(f"Node with id {node.node_id} already exists.")
        self.nodes[node.node_id] = node

    def add_edge(self, edge: CausalEdge) -> None:
        """
        Adds a CausalEdge to the graph.

        Args:
            edge: The CausalEdge to add.

        Raises:
            KeyError: If source or target node does not exist in the graph.
        """
        if edge.source_id not in self.nodes:
            raise KeyError(f"Source node {edge.source_id} does not exist.")
        if edge.target_id not in self.nodes:
            raise KeyError(f"Target node {edge.target_id} does not exist.")
        self.edges[edge.source_id].append(edge)
        self.reverse_edges[edge.target_id].append(edge)

    def get_node(self, node_id: str) -> Optional[SpacetimeNode]:
        """
        Retrieves a node by its ID.

        Args:
            node_id: The ID of the node to retrieve.
        Returns:
            The SpacetimeNode if found, else None.
        """
        return self.nodes.get(node_id)

    def get_outgoing_edges(self, node_id: str) -> List[CausalEdge]:
        """
        Returns all outgoing edges from a node.

        Args:
            node_id: The source node ID.
        Returns:
            List of outgoing CausalEdge instances. Empty if node has no outgoing edges.
        """
        return list(self.edges.get(node_id, []))

    def get_incoming_edges(self, node_id: str) -> List[CausalEdge]:
        """
        Returns all incoming edges to a node.

        Args:
            node_id: The target node ID.
        Returns:
            List of incoming CausalEdge instances. Empty if node has no incoming edges.
        """
        return list(self.reverse_edges.get(node_id, []))

    def get_ancestors(self, node_id: str, max_depth: Optional[int] = None) -> Set[str]:
        """
        Returns the set of ancestor node IDs for a given node.

        Args:
            node_id: The node ID to start from.
            max_depth: Optional maximum traversal depth.
        Returns:
            Set of ancestor node IDs.
        Raises:
            KeyError: If node_id does not exist in the graph.
        """
        if node_id not in self.nodes:
            raise KeyError(f"Node {node_id} does not exist.")
        visited: Set[str] = set()
        queue: List[tuple[str, int]] = [(node_id, 0)]
        while queue:
            current_id, depth = queue.pop(0)
            if max_depth is not None and depth >= max_depth:
                continue
            for edge in self.get_incoming_edges(current_id):
                src_id = edge.source_id
                if src_id not in visited:
                    visited.add(src_id)
                    queue.append((src_id, depth + 1))
        return visited

    def get_descendants(
        self, node_id: str, max_depth: Optional[int] = None
    ) -> Set[str]:
        """
        Returns the set of descendant node IDs for a given node.

        Args:
            node_id: The node ID to start from.
            max_depth: Optional maximum traversal depth.
        Returns:
            Set of descendant node IDs.
        Raises:
            KeyError: If node_id does not exist in the graph.
        """
        if node_id not in self.nodes:
            raise KeyError(f"Node {node_id} does not exist.")
        visited: Set[str] = set()
        queue: List[tuple[str, int]] = [(node_id, 0)]
        while queue:
            current_id, depth = queue.pop(0)
            if max_depth is not None and depth >= max_depth:
                continue
            for edge in self.get_outgoing_edges(current_id):
                tgt_id = edge.target_id
                if tgt_id not in visited:
                    visited.add(tgt_id)
                    queue.append((tgt_id, depth + 1))
        return visited

    # TODO: Add methods for context propagation, wave/information propagation, duality-aware traversal, etc.
