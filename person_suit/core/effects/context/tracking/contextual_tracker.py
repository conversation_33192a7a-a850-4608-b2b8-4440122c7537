"""
File: contextual_tracker.py
Purpose: Defines the ContextualEffectTracker for observing and analyzing effects.

This module provides the ContextualEffectTracker class, which is responsible for
observing ContextualEffect instances, maintaining their history, and performing
various analyses based on context, flow, dependencies, and interference patterns,
as per the CAW paradigm.

Related Files:
- person_suit/core/effects/contextual_effect.py: Defines ContextualEffect.
- person_suit/core/effects/core.py: Base EffectTracker.
- person_suit/core/infrastructure/wave/core.py: Context and wave-related classes.
"""

import json
import logging
import time
from dataclasses import dataclass, field
from enum import Enum, auto
from typing import Any, Dict, List, Optional, Tuple, Union

# Update imports: Remove try/except and import directly from caw.schemas
from ....caw.schemas import Context, WaveState
from ..effects.contextual_effect import ContextualEffect
from ...core import EffectType

logger = logging.getLogger(__name__)


class EffectAnalysisType(Enum):
    """Types of effect analysis."""

    FLOW = auto()  # Analyze effect flow between components
    DEPENDENCY = auto()  # Analyze dependencies between effects
    CONTEXT_TRANSITION = auto()  # Analyze context transitions
    INTERFERENCE = auto()  # Analyze effect interference
    COMPOSITION = auto()  # Analyze effect composition


@dataclass
class EffectAnalysisResult:
    """Result of effect analysis."""

    analysis_type: EffectAnalysisType
    context: Optional[Context]  # Use actual Context type
    data: Dict[str, Any]
    timestamp: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)


class ContextualEffectTracker:
    """
    Effect tracker with context-sensitive tracking and analysis capabilities.

    Extends the standard EffectTracker to handle ContextualEffect instances,
    track effects based on context, manage effect chains/transitions, and
    perform CAW-relevant analyses like interference.
    """

    def __init__(self):
        """Initialize the contextual effect tracker."""
        self._context_effects: Dict[str, List[ContextualEffect]] = {}
        self._wave_functions: Dict[str, WaveState] = {}
        self._effect_chains: Dict[str, List[List[ContextualEffect]]] = {}
        self._effect_transitions: List[
            Tuple[ContextualEffect, ContextualEffect, float]
        ] = []
        self._analysis_results: List[EffectAnalysisResult] = []

        # Initialize standard wave functions for analysis
        self._initialize_wave_functions()

    def _initialize_wave_functions(self):
        """Initialize default wave functions used for analysis aspects."""
        # Base tracking wave function (simple presence)
        self._wave_functions["effect_tracking"] = WaveState(
            amplitude_func=lambda *_: 1.0, phase_func=lambda *_: 0.0
        )
        # Flow analysis wave function (amplitude based on flow intensity)
        self._wave_functions["effect_flow"] = WaveState(
            amplitude_func=lambda effects_list, *_: min(1.0, len(effects_list) / 10.0),
            phase_func=lambda *_: 0.0,
        )
        # Context transition wave function (amplitude depends on context presence)
        self._wave_functions["context_transition"] = WaveState(
            amplitude_func=lambda transition_data, *_: (
                1.0
                if transition_data[0].context and transition_data[1].context
                else 0.5
            ),
            phase_func=lambda *_: 0.0,
        )

    def track_effect_in_context(
        self,
        effect_type: EffectType,
        source: str,
        context: Optional[Context] = None,
        metadata: Optional[Dict[str, Any]] = None,
        wave_amplitude: float = 1.0,
        wave_phase: float = 0.0,
        wave_function: Optional[WaveState] = None,
    ) -> ContextualEffect:
        """
        Track an effect in a specific context with wave properties.

        Args:
            effect_type: The type of the effect.
            source: The source of the effect.
            context: The context of the effect.
            metadata: Additional metadata for the effect.
            wave_amplitude: The amplitude of the effect wave.
            wave_phase: The phase of the effect wave.
            wave_function: Optional wave function for the effect.

        Returns:
            The tracked ContextualEffect instance.
        """
        effect = ContextualEffect(
            effect_type=effect_type,
            source=source,
            context=context,
            metadata=metadata,
            wave_amplitude=wave_amplitude,
            wave_phase=wave_phase,
            wave_function=wave_function,
        )

        # Track in the base tracker's list
        self._effects.append(effect)

        # Track specifically by context domain
        context_key = (
            context.domain if context and hasattr(context, "domain") else "default"
        )
        self._context_effects.setdefault(context_key, []).append(effect)

        # Log interpretation if context is available
        if context:
            try:
                effect_info = effect.as_information(context)
                effect_interp = effect_info.interpret(context)
                log_msg = (
                    f"Tracked effect {effect_type.name} in context {context_key} "
                    f"with probability {effect_interp.probability:.2f}, "
                    f"amplitude {wave_amplitude:.2f}, phase {wave_phase:.2f}"
                )
                logger.debug(log_msg)
                # Log additional context details if they exist
                if hasattr(context, "priority") and context.priority:
                    logger.debug(f"  Context priority: {context.priority}")
                if hasattr(context, "constraints") and context.constraints:
                    logger.debug(f"  Context constraints: {context.constraints}")
                if hasattr(context, "properties") and context.properties:
                    logger.debug(f"  Context properties: {context.properties}")
            except Exception as e:
                logger.error(
                    f"Error interpreting effect in context {context_key}: {e}",
                    exc_info=True,
                )
        else:
            logger.debug(f"Tracked effect {effect_type.name} without specific context.")

        return effect

    def get_effects_by_context(self, context: Context) -> List[ContextualEffect]:
        """
        Get all effects tracked in a specific context.

        Args:
            context: The context to filter by (uses context.domain).

        Returns:
            List of effects tracked in the specified context.
        """
        context_key = context.domain if hasattr(context, "domain") else "default"
        return self._context_effects.get(context_key, [])

    def get_effects_by_type_and_context(
        self, effect_type: EffectType, context: Context
    ) -> List[ContextualEffect]:
        """
        Get effects of a specific type tracked in a specific context.

        Args:
            effect_type: The type of effects to filter by.
            context: The context to filter by (uses context.domain).

        Returns:
            List of matching effects.
        """
        context_key = context.domain if hasattr(context, "domain") else "default"
        if context_key not in self._context_effects:
            return []

        return [
            effect
            for effect in self._context_effects[context_key]
            if effect.effect_type == effect_type
        ]

    def register_wave_function(self, name: str, wave_function: WaveState) -> None:
        """
        Register a named wave function for potential use in analysis or tracking.

        Args:
            name: The name to associate with the wave function.
            wave_function: The WaveState instance.
        """
        self._wave_functions[name] = wave_function
        logger.debug(f"Registered wave function '{name}' for effect tracking")

    def track_effect_chain(
        self, effects: List[ContextualEffect], context_key: str
    ) -> None:
        """
        Track an ordered sequence (chain) of effects within a context.

        Args:
            effects: The chain of effects to track.
            context_key: The context domain identifier for the chain.
        """
        if not effects:
            return
        self._effect_chains.setdefault(context_key, []).append(effects)
        logger.debug(
            f"Tracked effect chain with {len(effects)} effects in context {context_key}"
        )

    def track_effect_transition(
        self,
        from_effect: ContextualEffect,
        to_effect: ContextualEffect,
        weight: float = 1.0,
    ) -> None:
        """
        Track a transition between two effects, potentially weighted.

        Args:
            from_effect: The source effect.
            to_effect: The target effect.
            weight: The weight or strength of the transition.
        """
        self._effect_transitions.append((from_effect, to_effect, weight))
        from_type_name = (
            from_effect.effect_type.name
            if hasattr(from_effect.effect_type, "name")
            else str(from_effect.effect_type)
        )
        to_type_name = (
            to_effect.effect_type.name
            if hasattr(to_effect.effect_type, "name")
            else str(to_effect.effect_type)
        )
        logger.debug(
            f"Tracked effect transition from {from_type_name} to {to_type_name} with weight {weight:.2f}"
        )

    def get_effect_chains(
        self, context_key: Optional[str] = None
    ) -> Dict[str, List[List[ContextualEffect]]]:
        """
        Retrieve tracked effect chains, optionally filtered by context.

        Args:
            context_key: Optional context domain identifier to filter by.

        Returns:
            Dictionary mapping context keys to lists of effect chains.
        """
        if context_key:
            return {context_key: self._effect_chains.get(context_key, [])}
        return self._effect_chains.copy()

    def get_effect_transitions(
        self,
    ) -> List[Tuple[ContextualEffect, ContextualEffect, float]]:
        """
        Retrieve all tracked effect transitions.

        Returns:
            List of (from_effect, to_effect, weight) tuples.
        """
        return self._effect_transitions[:]

    def analyze_effects(
        self, analysis_type: EffectAnalysisType, context: Optional[Context] = None
    ) -> EffectAnalysisResult:
        """
        Perform a specific type of analysis on the tracked effects.

        Args:
            analysis_type: The type of analysis to perform.
            context: Optional context to scope the analysis.

        Returns:
            An EffectAnalysisResult object containing the analysis data.

        Raises:
            ValueError: If an unknown analysis type is provided.
        """
        if analysis_type == EffectAnalysisType.FLOW:
            result = self._analyze_effect_flow(context)
        elif analysis_type == EffectAnalysisType.DEPENDENCY:
            result = self._analyze_effect_dependencies(context)
        elif analysis_type == EffectAnalysisType.CONTEXT_TRANSITION:
            result = self._analyze_context_transitions(context)
        elif analysis_type == EffectAnalysisType.INTERFERENCE:
            result = self._analyze_effect_interference(context)
        elif analysis_type == EffectAnalysisType.COMPOSITION:
            result = self._analyze_effect_composition(context)
        else:
            raise ValueError(f"Unknown analysis type: {analysis_type}")

        self._analysis_results.append(result)
        logger.info(
            f"Performed analysis: {analysis_type.name} (Context: {context.domain if context else 'Global'}) -> {len(result.data)} items"
        )
        return result

    def _get_effects_for_analysis(
        self, context: Optional[Context] = None
    ) -> List[ContextualEffect]:
        """Helper to get effects relevant to an analysis context."""
        if context:
            return self.get_effects_by_context(context)
        # Return all contextual effects tracked
        all_effects = []
        for effects_list in self._context_effects.values():
            all_effects.extend(effects_list)
        return all_effects
        # Alternative: return [e for e in self._effects if isinstance(e, ContextualEffect)]

    def _analyze_effect_flow(
        self, context: Optional[Context] = None
    ) -> EffectAnalysisResult:
        """Analyze effect flow based on tracked transitions."""
        effects_in_scope = self._get_effects_for_analysis(context)
        effects_set = set(effects_in_scope)
        flow_graph: Dict[str, Dict[str, float]] = {}

        for from_effect, to_effect, weight in self._effect_transitions:
            if from_effect in effects_set and to_effect in effects_set:
                from_key = f"{from_effect.effect_type.name}:{from_effect.source}"
                to_key = f"{to_effect.effect_type.name}:{to_effect.source}"
                flow_graph.setdefault(from_key, {}).setdefault(to_key, 0.0)
                flow_graph[from_key][to_key] += weight

        return EffectAnalysisResult(
            analysis_type=EffectAnalysisType.FLOW,
            context=context,
            data={"flow_graph": flow_graph},
        )

    def _analyze_effect_dependencies(
        self, context: Optional[Context] = None
    ) -> EffectAnalysisResult:
        """Analyze effect dependencies based on tracked transitions."""
        effects_in_scope = self._get_effects_for_analysis(context)
        effects_set = set(effects_in_scope)
        dependency_graph: Dict[str, Dict[str, List[str]]] = {}

        for effect in effects_in_scope:
            effect_key = f"{effect.effect_type.name}:{effect.source}"
            dependency_graph[effect_key] = {"dependencies": [], "dependents": []}

        for from_effect, to_effect, _ in self._effect_transitions:
            if from_effect in effects_set and to_effect in effects_set:
                from_key = f"{from_effect.effect_type.name}:{from_effect.source}"
                to_key = f"{to_effect.effect_type.name}:{to_effect.source}"
                if (
                    from_key in dependency_graph
                    and to_key in dependency_graph[from_key]["dependents"]
                ):
                    dependency_graph[from_key]["dependents"].append(to_key)
                if (
                    to_key in dependency_graph
                    and from_key not in dependency_graph[to_key]["dependencies"]
                ):
                    dependency_graph[to_key]["dependencies"].append(from_key)

        return EffectAnalysisResult(
            analysis_type=EffectAnalysisType.DEPENDENCY,
            context=context,
            data={"dependency_graph": dependency_graph},
        )

    def _analyze_context_transitions(
        self, context: Optional[Context] = None
    ) -> EffectAnalysisResult:
        """Analyze transitions between different contexts."""
        # This analysis is inherently global unless filtered later
        transition_graph: Dict[str, Dict[str, float]] = {}
        for from_effect, to_effect, weight in self._effect_transitions:
            from_context_domain = (
                from_effect.context.domain
                if from_effect.context and hasattr(from_effect.context, "domain")
                else "default"
            )
            to_context_domain = (
                to_effect.context.domain
                if to_effect.context and hasattr(to_effect.context, "domain")
                else "default"
            )

            if from_context_domain != to_context_domain:
                transition_graph.setdefault(from_context_domain, {}).setdefault(
                    to_context_domain, 0.0
                )
                transition_graph[from_context_domain][to_context_domain] += weight

        return EffectAnalysisResult(
            analysis_type=EffectAnalysisType.CONTEXT_TRANSITION,
            context=context,  # Report context it was requested under, even if data is global
            data={"transition_graph": transition_graph},
        )

    def _analyze_effect_interference(
        self, context: Optional[Context] = None
    ) -> EffectAnalysisResult:
        """Analyze effect interference using wave properties within a context."""
        effects_in_scope = self._get_effects_for_analysis(context)
        interference_graph: Dict[str, Dict[str, Dict[str, Any]]] = {}
        effects_by_source: Dict[str, List[ContextualEffect]] = {}

        for effect in effects_in_scope:
            effects_by_source.setdefault(effect.source, []).append(effect)

        analysis_context = context  # The context used for calculations

        for source, source_effects in effects_by_source.items():
            source_interference: Dict[str, Dict[str, Any]] = {}
            for i, effect1 in enumerate(source_effects):
                for j, effect2 in enumerate(source_effects):
                    if i < j:  # Avoid self-interference and duplicates
                        effect1_key = f"{effect1.effect_type.name}"
                        effect2_key = f"{effect2.effect_type.name}"

                        # Determine the context for interference calculation
                        calc_ctx = (
                            analysis_context or effect1.context or effect2.context
                        )

                        interference_data: Dict[str, Any] = {
                            "amplitude": None,
                            "phase": None,
                            "constructive": None,
                            "destructive": None,
                        }

                        if calc_ctx:
                            try:
                                amp, phase = effect1.interfere_with(effect2, calc_ctx)
                                interference_data.update(
                                    {
                                        "amplitude": amp,
                                        "phase": phase,
                                        "constructive": amp
                                        > max(
                                            effect1.wave_amplitude,
                                            effect2.wave_amplitude,
                                        ),
                                        "destructive": amp
                                        < min(
                                            effect1.wave_amplitude,
                                            effect2.wave_amplitude,
                                        ),
                                    }
                                )
                                logger.debug(
                                    f"Effect interference ({source}): {effect1_key} + {effect2_key} = "
                                    f"amplitude {amp:.2f}, phase {phase:.2f}"
                                )
                            except Exception as e:
                                logger.warning(
                                    f"Could not calculate interference between {effect1_key} and {effect2_key} in context {calc_ctx.domain}: {e}"
                                )
                        else:
                            logger.warning(
                                f"No context available for interference calculation between {effect1_key} and {effect2_key}"
                            )

                        source_interference.setdefault(effect1_key, {})[effect2_key] = (
                            interference_data
                        )
            if source_interference:
                interference_graph[source] = source_interference

        # Gather context info for the result metadata
        context_info = {
            "context_domain": (
                context.domain if context and hasattr(context, "domain") else None
            ),
            "context_priority": (
                context.priority if context and hasattr(context, "priority") else None
            ),
            "context_constraints": (
                context.constraints
                if context and hasattr(context, "constraints")
                else None
            ),
        }

        return EffectAnalysisResult(
            analysis_type=EffectAnalysisType.INTERFERENCE,
            context=context,
            data={"interference_graph": interference_graph, **context_info},
        )

    def _analyze_effect_composition(
        self, context: Optional[Context] = None
    ) -> EffectAnalysisResult:
        """Analyze common sequences (patterns) of effects within chains."""
        chains_in_scope = []
        target_chains = self.get_effect_chains(
            context.domain if context and hasattr(context, "domain") else None
        )
        for chain_list in target_chains.values():
            chains_in_scope.extend(chain_list)

        composition_patterns: Dict[Tuple[str, ...], int] = {}
        for chain in chains_in_scope:
            if len(chain) >= 2:
                # Use effect type names for the pattern key
                pattern = tuple(
                    (
                        ef.effect_type.name
                        if hasattr(ef.effect_type, "name")
                        else str(ef.effect_type)
                    )
                    for ef in chain
                )
                composition_patterns[pattern] = composition_patterns.get(pattern, 0) + 1

        # Convert tuple keys to strings for JSON serialization if needed
        serializable_patterns = {
            ",".join(k): v for k, v in composition_patterns.items()
        }

        return EffectAnalysisResult(
            analysis_type=EffectAnalysisType.COMPOSITION,
            context=context,
            data={"composition_patterns": serializable_patterns},
        )

    # Visualization methods can remain here or be moved
    def visualize_effects(self, format: str = "json") -> str:
        """Generate a visualization string of the tracked effects and analyses."""
        if format == "json":
            return self._visualize_as_json()
        elif format == "dot":
            return self._visualize_as_dot()
        else:
            raise ValueError(f"Unknown visualization format: {format}")

    def _visualize_as_json(self) -> str:
        """Visualize tracked data as JSON."""
        # Simplified visualization - consider serializing ContextualEffect properly
        visualization = {
            "effects_by_context": {
                ctx: [str(e) for e in effs]
                for ctx, effs in self._context_effects.items()
            },
            "transitions": [
                {"from": str(f), "to": str(t), "weight": w}
                for f, t, w in self._effect_transitions
            ],
            "chains": {
                ctx: [[str(e) for e in chain] for chain in chains]
                for ctx, chains in self._effect_chains.items()
            },
            "analysis_results": [
                {
                    "type": r.analysis_type.name,
                    "context": r.context.domain if r.context else None,
                    "timestamp": r.timestamp,
                    "data": r.data,  # May need custom serialization for complex data
                }
                for r in self._analysis_results
            ],
        }
        try:
            return json.dumps(
                visualization, indent=2, default=lambda o: "<not serializable>"
            )
        except TypeError as e:
            logger.error(f"JSON serialization error during visualization: {e}")
            return json.dumps({"error": "Serialization failed"}, indent=2)

    def _visualize_as_dot(self) -> str:
        """Visualize effect transitions as a DOT graph string."""
        dot = [
            "digraph EffectGraph {",
            "  rankdir=LR;",
            "  node [shape=box, style=rounded];",
        ]
        nodes: Dict[str, str] = {}
        node_count = 0

        def get_node_id(effect: ContextualEffect) -> str:
            nonlocal node_count
            effect_str = str(effect)
            if effect_str not in nodes:
                nodes[effect_str] = f"E{node_count}"
                node_count += 1
                # Add node definition
                label = f"{effect.effect_type.name}\n{effect.source}\nctx: {effect.context.domain if effect.context else 'N/A'}"
                dot.append(f'  {nodes[effect_str]} [label="{label}"];')
            return nodes[effect_str]

        for from_effect, to_effect, weight in self._effect_transitions:
            from_id = get_node_id(from_effect)
            to_id = get_node_id(to_effect)
            dot.append(f'  {from_id} -> {to_id} [label="{weight:.2f}"];')

        dot.append("}")
        return "\n".join(dot)


# Global instance for convenience, matching pattern in original file
_contextual_effect_tracker: Optional[ContextualEffectTracker] = None


def get_contextual_effect_tracker() -> ContextualEffectTracker:
    """Get the global singleton contextual effect tracker instance."""
    global _contextual_effect_tracker
    if _contextual_effect_tracker is None:
        _contextual_effect_tracker = ContextualEffectTracker()
    return _contextual_effect_tracker
