"""
Person Suit - Core Effect Types

Centralized definitions for EffectType and Effect classes implementing the effect system's core abstractions.
This module is the single source of truth for effect type and effect instance representations, breaking circular dependencies between core and types submodules.

Related Files:
- core/types/types.py: Now imports from here
- core/types.py: Now imports from here
- core/__init__.py: Now imports from here

Dependencies:
- typing, dataclasses, logging
- person_suit.core.effects.interfaces (EffectTypeInterface, EffectInterface)
"""

import logging
from dataclasses import dataclass
from typing import Any, Dict, List

from .interfaces import EffectInterface, EffectTypeInterface

logger = logging.getLogger(__name__)


class EffectType(EffectTypeInterface):
    """
    Effect type implementation.
    Represents a type of effect (IO, Database, Network, etc.) and its supported operations.
    """

    def __init__(self, name: str, operations: List[str]):
        self._name = name
        self._operations = set(operations)

    @property
    def name(self) -> str:
        return self._name

    @property
    def operations(self) -> List[str]:
        return list(self._operations)

    def has_operation(self, operation: str) -> bool:
        return operation in self._operations

    def __eq__(self, other: object) -> bool:
        if not isinstance(other, EffectType):
            return NotImplemented
        return self._name == other._name

    def __hash__(self) -> int:
        return hash(self._name)

    def __str__(self) -> str:
        return f"EffectType({self._name})"

    def __repr__(self) -> str:
        return f"EffectType(name='{self._name}', operations={list(self._operations)})"


@dataclass
class Effect(EffectInterface):
    """
    Concrete implementation of an effect.
    """

    effect_type: EffectTypeInterface
    operation: str
    parameters: Dict[str, Any]

    def __init__(
        self,
        effect_type: EffectTypeInterface,
        operation: str,
        parameters: Dict[str, Any],
    ):
        if not effect_type.has_operation(operation):
            raise ValueError(
                f"Effect type {effect_type.name} doesn't support operation {operation}. "
                f"Supported operations: {', '.join(effect_type.operations)}"
            )
        self.effect_type = effect_type
        self.operation = operation
        self.parameters = parameters.copy()

    def __repr__(self) -> str:
        return (
            f"Effect(type={self.effect_type.name}, "
            f"operation={self.operation}, "
            f"parameters={self.parameters})"
        )


# Standard effect type singletons (canonical, for import elsewhere)
IO = EffectType("io", ["read", "write", "append", "delete", "exists"])
Database = EffectType("database", ["query", "insert", "update", "delete", "execute"])
Network = EffectType(
    "network", ["get", "post", "put", "delete", "patch", "head", "options"]
)
State = EffectType("state", ["get", "set", "update", "delete", "watch"])
Random = EffectType("random", ["generate", "seed"])
Pure = EffectType("pure", ["evaluate"])
Memory = EffectType("memory", ["allocate", "free", "read", "write"])
Computation = EffectType(
    "computation", ["execute", "cancel", "pause", "resume", "status"]
)
Telemetry = EffectType("telemetry", ["record", "measure", "track"])
Security = EffectType("security", ["verify", "encrypt", "decrypt"])
InformationFlow = EffectType("information_flow", ["transform", "route", "filter"])
ContextSwitch = EffectType("context_switch", ["switch", "save", "restore"])
Messaging = EffectType("messaging", ["send", "receive"])
StateRead = EffectType("state_read", ["read"])
StateChange = EffectType("state_change", ["change"])
ActorCreation = EffectType("actor_creation", ["create"])
ActorLifecycle = EffectType("actor_lifecycle", ["start", "stop", "restart"])
Conversion = EffectType("conversion", ["convert"])
Creation = EffectType("creation", ["create"])
ResourceManagement = EffectType("resource_management", ["allocate", "release"])
Custom = EffectType("custom", ["custom"])
Logging = EffectType("logging", ["debug", "info", "warning", "error", "critical"])
Differential = EffectType("differential", ["create_field", "compute_gradient", "transform_field", "analyze_derivatives", "update", "propagate", "convergence", "history"])
