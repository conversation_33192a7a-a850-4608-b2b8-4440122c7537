"""
File: examples.py
Purpose: Examples of using the effect system.

This module provides examples of how to use the effect system, including
defining effects, using effect decorators, and running effects with handlers.

Related Files:
- person_suit/core/infrastructure/effects/core.py: Core effect type definitions
- person_suit/core/infrastructure/effects/handlers.py: Effect handlers
- person_suit/core/infrastructure/effects/decorators.py: Effect decorators

Dependencies:
- typing>=4.0.0: For type annotations
"""

from typing import Any, Dict, List, Optional

from .core import Effect
from .decorators import effect_handler, effects
from .runtime import get_runtime
from ._internal_types import IO, Database, Network


# Example 1: Using the perform_effect function
def example_perform_effect():
    """
    Example of using the perform_effect function.
    """
    from . import perform_effect

    # Read a file
    try:
        content = perform_effect(IO, "read_file", "example.txt")
        print(f"File content: {content}")
    except FileNotFoundError:
        print("File not found")

    # Write to a file
    perform_effect(IO, "write_file", "example_output.txt", "Hello, world!")
    print("Wrote to file")

    # Read from stdin
    name = perform_effect(IO, "read_stdin", "Enter your name: ")
    print(f"Hello, {name}!")


# Example 2: Using the @effects decorator
@effects([IO, Database])
def process_data(file_path: str) -> List[Dict[str, Any]]:
    """
    Process data from a file and store it in a database.

    Args:
        file_path: Path to the file to process

    Returns:
        List of processed data items
    """
    from . import perform_effect

    # Read file (IO effect)
    content = perform_effect(IO, "read_file", file_path)

    # Parse data (pure computation)
    lines = content.strip().split("\n")
    parsed_data = []
    for line in lines:
        if line:
            parts = line.split(",")
            if len(parts) >= 2:
                item = {"name": parts[0].strip(), "value": parts[1].strip()}
                parsed_data.append(item)

    # Save to database (Database effect)
    for item in parsed_data:
        perform_effect(Database, "insert", "items", item)

    return parsed_data


# Example 3: Using the @effect_handler decorator
@effect_handler(IO)
def custom_read_file(path: str) -> str:
    """
    Custom handler for reading files.

    Args:
        path: Path to the file to read

    Returns:
        The contents of the file
    """
    print(f"Custom handler reading file: {path}")
    with open(path, "r") as f:
        return f.read()


# Example 4: Using the Effect class directly
def create_read_file_effect(path: str) -> Effect:
    """
    Create an effect for reading a file.

    Args:
        path: Path to the file to read

    Returns:
        An effect for reading the file
    """
    return Effect(IO, "read_file", None, path)


# Example 5: Using the with_handler context manager
def example_with_handler():
    """
    Example of using the with_handler context manager.
    """
    from . import perform_effect
    from .handlers import IOHandler

    # Create a custom IO handler
    class CustomIOHandler(IOHandler):
        def _handle_read_file(
            self, path: str, mode: str = "r", encoding: Optional[str] = "utf-8"
        ) -> str:
            print(f"Custom handler reading file: {path}")
            return f"Custom content for {path}"

    # Get the runtime
    runtime = get_runtime()

    # Use the custom handler temporarily
    with runtime.with_handler(CustomIOHandler()):
        # This will use the custom handler
        content = perform_effect(IO, "read_file", "example.txt")
        print(f"File content: {content}")

    # This will use the default handler
    try:
        content = perform_effect(IO, "read_file", "example.txt")
        print(f"File content: {content}")
    except FileNotFoundError:
        print("File not found")


# Example 6: Using the with_context context manager
def example_with_context():
    """
    Example of using the with_context context manager.
    """
    from . import perform_effect

    # Get the runtime
    runtime = get_runtime()

    # Use a context temporarily
    with runtime.with_context(user_id="123", request_id="456"):
        # This will have access to the context
        context = runtime.get_context()
        print(f"Context: {context}")

        # Perform an effect with the context
        perform_effect(IO, "write_stdout", f"Current context: {context}")


# Example 7: Combining multiple effects
@effects([IO, Database, Network])
def fetch_and_store_data(url: str, output_file: str) -> List[Dict[str, Any]]:
    """
    Fetch data from a URL, store it in a file, and save it to a database.

    Args:
        url: URL to fetch data from
        output_file: Path to the output file

    Returns:
        List of fetched data items
    """
    from . import perform_effect

    # Fetch data from URL (Network effect)
    response = perform_effect(Network, "http_get", url)

    # Extract data from response
    data = response.get("data", {})

    # Write data to file (IO effect)
    perform_effect(IO, "write_file", output_file, str(data))

    # Save data to database (Database effect)
    if isinstance(data, list):
        for item in data:
            perform_effect(Database, "insert", "items", item)
    elif isinstance(data, dict):
        perform_effect(Database, "insert", "items", data)

    return data if isinstance(data, list) else [data]


# Run examples if this module is executed directly
if __name__ == "__main__":
    print("Example 1: Using the perform_effect function")
    example_perform_effect()

    print("\nExample 2: Using the @effects decorator")
    try:
        result = process_data("example.txt")
        print(f"Processed data: {result}")
    except FileNotFoundError:
        print("File not found")

    print("\nExample 5: Using the with_handler context manager")
    example_with_handler()

    print("\nExample 6: Using the with_context context manager")
    example_with_context()
