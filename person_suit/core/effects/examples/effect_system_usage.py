"""
Person Suit - Effect System Usage Example

This script demonstrates how to use the effect system.

It shows how to:
1. Create custom effect types
2. Create effects
3. Create handlers for effect types
4. Handle effects
"""

from typing import Dict, Optional

from ..handlers import <PERSON>Handler
from ..registry import EffectRegistry
from ..types import <PERSON><PERSON>ABAS<PERSON>, IO, Effect, EffectType


def main() -> None:
    """Demonstrate the effect system usage."""
    # Create a registry
    registry = EffectRegistry()

    # Create custom effect types
    LOGGING = EffectType("LOGGING", {"info", "warning", "error", "debug"})

    # Create handlers
    io_handler = create_io_handler()
    db_handler = create_db_handler()
    log_handler = create_log_handler()

    # Register handlers
    registry.register_handler(io_handler)
    registry.register_handler(db_handler)
    registry.register_handler(log_handler)

    # Create and handle effects
    file_read_effect = Effect(IO, "read", {"path": "example.txt"})
    query_effect = Effect(DATABASE, "query", {"query": "SELECT * FROM users"})
    log_effect = Effect(LOGGING, "info", {"message": "This is an information message"})

    # Handle effects
    print("Handling file read effect:")
    file_content = registry.handle_effect(file_read_effect)
    print(f"  Result: {file_content}")

    print("\nHandling database query effect:")
    query_result = registry.handle_effect(query_effect)
    print(f"  Result: {query_result}")

    print("\nHandling log effect:")
    log_result = registry.handle_effect(log_effect)
    print(f"  Result: {log_result}")


def create_io_handler() -> EffectHandler:
    """
    Create a handler for IO effects.

    Returns:
        An IO effect handler
    """
    handler = EffectHandler("IOHandler")

    # Register handlers for IO operations
    handler.register_handler(IO, "read", handle_io_read)
    handler.register_handler(IO, "write", handle_io_write)
    handler.register_handler(IO, "delete", handle_io_delete)

    return handler


def create_db_handler() -> EffectHandler:
    """
    Create a handler for DATABASE effects.

    Returns:
        A DATABASE effect handler
    """
    handler = EffectHandler("DatabaseHandler")

    # Register handlers for DATABASE operations
    handler.register_handler(DATABASE, "query", handle_db_query)
    handler.register_handler(DATABASE, "insert", handle_db_insert)
    handler.register_handler(DATABASE, "update", handle_db_update)
    handler.register_handler(DATABASE, "delete", handle_db_delete)

    return handler


def create_log_handler() -> EffectHandler:
    """
    Create a handler for LOGGING effects.

    Returns:
        A LOGGING effect handler
    """
    # Create custom effect type for logging
    LOGGING = EffectType("LOGGING", {"info", "warning", "error", "debug"})

    handler = EffectHandler("LoggingHandler")

    # Register handlers for LOGGING operations
    handler.register_handler(LOGGING, "info", handle_log_info)
    handler.register_handler(LOGGING, "warning", handle_log_warning)
    handler.register_handler(LOGGING, "error", handle_log_error)
    handler.register_handler(LOGGING, "debug", handle_log_debug)

    return handler


# IO operation handlers
def handle_io_read(path: str, **kwargs) -> str:
    """Simulate reading from a file."""
    print(f"  [IO] Reading from {path}")
    return f"Content of {path}"


def handle_io_write(path: str, content: str, **kwargs) -> int:
    """Simulate writing to a file."""
    print(f"  [IO] Writing to {path}: {content}")
    return len(content)  # Return number of bytes written


def handle_io_delete(path: str, **kwargs) -> bool:
    """Simulate deleting a file."""
    print(f"  [IO] Deleting {path}")
    return True  # Return success


# DATABASE operation handlers
def handle_db_query(query: str, **kwargs) -> Dict:
    """Simulate a database query."""
    print(f"  [DB] Executing query: {query}")
    return {"rows": 10, "data": ["row1", "row2", "row3"]}


def handle_db_insert(table: str, data: Dict, **kwargs) -> int:
    """Simulate a database insert."""
    print(f"  [DB] Inserting into {table}: {data}")
    return 1  # Return number of rows inserted


def handle_db_update(
    table: str, data: Dict, condition: Optional[str] = None, **kwargs
) -> int:
    """Simulate a database update."""
    print(f"  [DB] Updating {table}: {data}")
    if condition:
        print(f"  [DB] With condition: {condition}")
    return 2  # Return number of rows updated


def handle_db_delete(table: str, condition: Optional[str] = None, **kwargs) -> int:
    """Simulate a database delete."""
    print(f"  [DB] Deleting from {table}")
    if condition:
        print(f"  [DB] With condition: {condition}")
    return 3  # Return number of rows deleted


# LOGGING operation handlers
def handle_log_info(message: str, **kwargs) -> None:
    """Handle info log."""
    print(f"  [LOG] INFO: {message}")


def handle_log_warning(message: str, **kwargs) -> None:
    """Handle warning log."""
    print(f"  [LOG] WARNING: {message}")


def handle_log_error(message: str, error: Optional[Exception] = None, **kwargs) -> None:
    """Handle error log."""
    print(f"  [LOG] ERROR: {message}")
    if error:
        print(f"  [LOG] Exception: {error}")


def handle_log_debug(message: str, **kwargs) -> None:
    """Handle debug log."""
    print(f"  [LOG] DEBUG: {message}")


if __name__ == "__main__":
    main()
