"""
Person Suit - Effect System Interfaces

Defines the core interfaces (contracts) for effect types and effect instances, decoupled from implementation. This supports CAW's separation of potential (interface) and actual (implementation).

Related Files:
- effect_types.py: Implements these interfaces
- core/types/types.py: Uses these interfaces
- core/base/interfaces.py: No longer defines these interfaces

"""

from abc import ABC, abstractmethod
from typing import (
    Any,
    Callable,
    Dict,
    List,
    Optional,
    Protocol,
    TypeVar,
    runtime_checkable,
    Generic,
)

T = TypeVar("T")  # Result type
E = TypeVar("E")  # Effect type


class EffectTypeInterface(ABC):
    """Interface for effect types."""

    @property
    @abstractmethod
    def name(self) -> str:
        """Get the name of the effect type."""
        pass

    @property
    @abstractmethod
    def operations(self) -> List[str]:
        """Get the operations supported by this effect type."""
        pass

    @abstractmethod
    def has_operation(self, operation: str) -> bool:
        """Check if this effect type supports an operation."""
        pass


class EffectInterface(Generic[T], ABC):
    """Interface for effects."""

    @property
    @abstractmethod
    def effect_type(self) -> EffectTypeInterface:
        """Get the type of the effect."""
        pass

    @property
    @abstractmethod
    def operation(self) -> str:
        """Get the operation of the effect."""
        pass

    @property
    @abstractmethod
    def parameters(self) -> Dict[str, Any]:
        """Get the parameters of the effect."""
        pass


# Optionally, add Protocols for runtime duck-typing if needed
@runtime_checkable
class EffectTypeProtocol(Protocol):
    @property
    def name(self) -> str: ...
    @property
    def operations(self) -> List[str]: ...
    def has_operation(self, operation: str) -> bool: ...


@runtime_checkable
class EffectProtocol(Protocol):
    @property
    def effect_type(self) -> EffectTypeInterface: ...
    @property
    def operation(self) -> str: ...
    @property
    def parameters(self) -> Dict[str, Any]: ...


class EffectHandlerInterface(ABC):
    """Interface for effect handlers."""

    @abstractmethod
    def can_handle(self, effect_type: Any) -> bool:
        """Check if this handler can handle the given effect type."""
        pass

    @abstractmethod
    def handle(
        self,
        effect_type: Any,
        operation: str,
        computation: Optional[Callable[..., Any]],
        *args: Any,
        **kwargs: Any,
    ) -> Any:
        """Handle an effect."""
        pass


class EffectRegistryInterface(ABC):
    """Interface for effect handler registries."""

    @abstractmethod
    def register_handler(self, handler: "EffectHandlerInterface") -> None:
        """Register an effect handler."""
        pass

    @abstractmethod
    def get_handler(self, effect_type: Any) -> Optional["EffectHandlerInterface"]:
        """Get a handler for a given effect type."""
        pass


class EffectRuntimeInterface(ABC):
    """Interface for the effect system runtime."""

    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the runtime."""
        pass

    @abstractmethod
    async def shutdown(self) -> None:
        """Shutdown the runtime."""
        pass

    @abstractmethod
    async def register_handler(self, handler: "EffectHandlerInterface") -> None:
        """Register a handler with the runtime."""
        pass

    @abstractmethod
    async def execute_effect(
        self, effect: "EffectInterface", context: Optional[Any] = None
    ) -> Any:
        """Execute an effect in the runtime."""
        pass
