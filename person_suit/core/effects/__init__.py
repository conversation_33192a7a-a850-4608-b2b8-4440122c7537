"""Effect System Package.

This package provides the core functionality for the Person Suit Effect System.
It implements a wave-particle duality based effect system that supports both
potential (wave) and concrete (particle) state operations.

The system is organized into the following components:
- Core: Base interfaces, types, and runtime
- Handlers: Effect handlers for various operations
- Context: Context management and tracking
- Monitoring: System monitoring and metrics
"""

# Context management
from .context import <PERSON>text<PERSON>ana<PERSON>, ContextualEffect, ContextualEffectTracker
from .core._internal_types import (
    ContextID,
    EffectContext,
    EffectID,
    EffectMetrics,
    EffectState,
    HandlerContext,
    HandlerID,
    HandlerMetrics,
    HandlerState,
    ResourceMetrics,
    RuntimeContext,
    RuntimeMetrics,
    RuntimeState,
)

# Core components
from .core.base import (
    ContextInterface,
    Effect,
    EffectHandler,
    HandlerInterface,
    MetricsInterface,
    RuntimeInterface,
)

# Convenience functions
from .core.base.decorators import context_effects, effect, wave_effects, with_effects
from .core.runtime import EffectRuntime

# Advanced handlers
from .handlers.advanced import DifferentialHandler, ProbabilisticHandler

# Core handlers
from .handlers.core import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
)
from .handlers.core import ContextManager as <PERSON>lerContextManager

# Monitoring handlers
from .monitoring import LoggingHandler, TelemetryEffectHandler

__all__ = [
    # Core interfaces
    "Effect",
    "RuntimeInterface",
    "HandlerInterface",
    "ContextInterface",
    "MetricsInterface",
    "EffectHandler",
    # Types
    "EffectID",
    "HandlerID",
    "ContextID",
    "RuntimeState",
    "EffectState",
    "HandlerState",
    "RuntimeMetrics",
    "EffectMetrics",
    "HandlerMetrics",
    "ResourceMetrics",
    "EffectContext",
    "HandlerContext",
    "RuntimeContext",
    # Runtime
    "EffectRuntime",
    # Core handlers
    "IOHandler",
    "DatabaseHandler",
    "NetworkHandler",
    "StateHandler",
    "ComputationHandler",
    "HandlerContextManager",
    # Advanced handlers
    "ProbabilisticHandler",
    "DifferentialHandler",
    # Monitoring handlers
    "LoggingHandler",
    "TelemetryEffectHandler",
    # Context management
    "ContextualEffectTracker",
    "ContextualEffect",
    "ContextManager",
    # Convenience functions
    "effect",
    "with_effects",
    "context_effects",
    "wave_effects",
]
