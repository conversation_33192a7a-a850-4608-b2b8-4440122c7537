"""
Differential Effect Handler
=======================

Handles differential operations with wave-particle duality. In wave state,
operations exist as continuous fields and gradients, collapsing to discrete
changes and updates in particle state.
"""

import logging
from dataclasses import dataclass, field
from typing import Any, Callable, Dict, Generic, List, Optional, Tuple, TypeVar, Union

import numpy as np
import asyncio

# Optional JAX import - use numpy fallback if not available
try:
    import jax
    import jax.numpy as jnp
    JAX_AVAILABLE = True
except ImportError:
    JAX_AVAILABLE = False
    jax = None
    jnp = None

from ...interfaces import EffectInterface
from ...core.types import Differential as DifferentialEffectType
from person_suit.core.effects.handlers import AsyncEffectHandler
from ..monitoring.decorators import particle_monitored, wave_monitored

logger = logging.getLogger(__name__)

T = TypeVar("T")  # Value type for differentiation


@dataclass
class DifferentialField:
    """Represents a differential field with values and gradients."""

    values: np.ndarray
    gradients: np.ndarray
    parameters: Dict[str, Any]
    metadata: Dict[str, Any]


class DifferentialHandler(AsyncEffectHandler, Generic[T]):
    """
    Handles differential operations with wave-particle duality.

    Features:
    - Wave state gradients
    - Particle state updates
    - Field transformations
    - Automatic differentiation
    - Change propagation
    """

    def __init__(self):
        """Initialize the differential handler."""
        super().__init__(
            supported_types=[DifferentialEffectType],
            required_capabilities=[],  # Add capabilities if needed
        )
        self._wave_fields: Dict[str, DifferentialField] = {}
        self._particle_updates: Dict[str, List[Dict[str, Any]]] = {}
        self._gradient_history: Dict[str, List[np.ndarray]] = {}
        self._computation_graph: Dict[str, Dict[str, Any]] = {}

    async def _async_wave_operation(
        self, effect: EffectInterface, context: Dict[str, Any]
    ) -> Any:
        """
        Handle wave state operations.

        In wave state, we:
        - Maintain continuous fields
        - Compute gradients
        - Transform fields
        - Analyze derivatives
        """
        operation = effect.operation
        params = effect.parameters

        if operation == "create_field":
            return await self._create_field(
                params["name"],
                params["values"],
                params.get("parameters", {}),
                params.get("metadata", {}),
            )
        elif operation == "compute_gradient":
            return await self._compute_gradient(params["name"], params["function"])
        elif operation == "transform_field":
            return await self._transform_field(
                params["name"], params["transformation"], params.get("parameters", {})
            )
        elif operation == "analyze_derivatives":
            return await self._analyze_derivatives(
                params["name"], params.get("order", 1)
            )
        else:
            raise ValueError(f"Unknown wave operation: {operation}")

    async def _async_particle_operation(
        self, effect: EffectInterface, context: Dict[str, Any]
    ) -> Any:
        """
        Handle particle state operations.

        In particle state, we:
        - Apply discrete updates
        - Track changes
        - Propagate updates
        - Analyze convergence
        """
        operation = effect.operation
        params = effect.parameters

        if operation == "update":
            return await self._apply_update(params["name"], params["updates"])
        elif operation == "propagate":
            return await self._propagate_changes(params["name"], params.get("depth", 1))
        elif operation == "convergence":
            return await self._check_convergence(
                params["name"], params.get("tolerance", 1e-6)
            )
        elif operation == "history":
            return await self._get_update_history(params["name"])
        else:
            raise ValueError(f"Unknown particle operation: {operation}")

    async def _create_field(
        self,
        name: str,
        values: np.ndarray,
        parameters: Dict[str, Any],
        metadata: Dict[str, Any],
    ) -> DifferentialField:
        """
        Create a new differential field.

        Initializes field with values and computes initial gradients.
        """
        values_array = np.array(values)
        gradients = np.zeros_like(values_array)

        field = DifferentialField(
            values=values_array,
            gradients=gradients,
            parameters=parameters,
            metadata=metadata,
        )

        self._wave_fields[name] = field
        self._gradient_history[name] = []
        self._particle_updates[name] = []

        return field

    async def _compute_gradient(
        self, name: str, function: Union[str, Callable]
    ) -> Dict[str, Any]:
        """
        Compute gradient of a function over a field.

        Uses automatic differentiation for gradient computation.
        """
        if name not in self._wave_fields:
            raise ValueError(f"Field {name} not found")

        field = self._wave_fields[name]

        if isinstance(function, str):
            # Parse string function (simplified)
            grad = np.gradient(field.values)
        else:
            # Use provided function for gradient
            try:
                grad = function(field.values)
            except Exception as e:
                logger.error(f"Gradient computation failed: {e}")
                raise

        field.gradients = grad
        self._gradient_history[name].append(grad)

        return {
            "field": name,
            "gradient": grad,
            "magnitude": np.linalg.norm(grad),
            "direction": grad / (np.linalg.norm(grad) + 1e-10),
        }

    async def _transform_field(
        self, name: str, transformation: str, parameters: Dict[str, Any]
    ) -> DifferentialField:
        """
        Transform a differential field.

        Applies transformations while preserving differential structure.
        """
        if name not in self._wave_fields:
            raise ValueError(f"Field {name} not found")

        field = self._wave_fields[name]

        if transformation == "scale":
            scale = parameters.get("factor", 1.0)
            new_values = field.values * scale
            new_gradients = field.gradients * scale
        elif transformation == "shift":
            shift = parameters.get("offset", 0.0)
            new_values = field.values + shift
            new_gradients = field.gradients  # Gradients unchanged by shift
        elif transformation == "compose":
            other_name = parameters.get("other")
            if other_name not in self._wave_fields:
                raise ValueError(f"Field {other_name} not found")
            other = self._wave_fields[other_name]
            new_values = field.values + other.values
            new_gradients = field.gradients + other.gradients
        else:
            raise ValueError(f"Unknown transformation: {transformation}")

        return DifferentialField(
            values=new_values,
            gradients=new_gradients,
            parameters={**field.parameters, **parameters},
            metadata=field.metadata,
        )

    async def _analyze_derivatives(self, name: str, order: int = 1) -> Dict[str, Any]:
        """
        Analyze derivatives of a field.

        Computes higher-order derivatives and their properties.
        """
        if name not in self._wave_fields:
            raise ValueError(f"Field {name} not found")

        field = self._wave_fields[name]
        derivatives = []
        current = field.values

        for i in range(order):
            derivative = np.gradient(current)
            derivatives.append(
                {
                    "order": i + 1,
                    "values": derivative,
                    "magnitude": np.linalg.norm(derivative),
                    "smoothness": self._calculate_smoothness(derivative),
                }
            )
            current = derivative

        return {
            "field": name,
            "derivatives": derivatives,
            "total_variation": sum(d["magnitude"] for d in derivatives),
        }

    async def _apply_update(
        self, name: str, updates: Union[np.ndarray, List[Dict[str, Any]]]
    ) -> Dict[str, Any]:
        """
        Apply updates to a field.

        Converts continuous gradients to discrete updates.
        """
        if name not in self._wave_fields:
            raise ValueError(f"Field {name} not found")

        field = self._wave_fields[name]

        if isinstance(updates, np.ndarray):
            field.values += updates
            update_info = {"type": "direct", "magnitude": np.linalg.norm(updates)}
        else:
            total_update = np.zeros_like(field.values)
            for update in updates:
                if update["type"] == "gradient":
                    total_update -= update.get("learning_rate", 0.01) * field.gradients
                elif update["type"] == "momentum":
                    if self._gradient_history[name]:
                        momentum = np.mean(self._gradient_history[name][-5:], axis=0)
                        total_update -= update.get("momentum_rate", 0.9) * momentum

            field.values += total_update
            update_info = {
                "type": "composite",
                "magnitude": np.linalg.norm(total_update),
            }

        self._particle_updates[name].append(update_info)
        return update_info

    async def _propagate_changes(self, name: str, depth: int = 1) -> Dict[str, Any]:
        """
        Propagate changes through connected fields.

        Handles update propagation in the computation graph.
        """
        if name not in self._wave_fields:
            raise ValueError(f"Field {name} not found")

        propagation_info = {"source": name, "depth": depth, "affected_fields": []}

        # Simplified propagation through computation graph
        visited = set()
        queue = [(name, 0)]

        while queue and queue[0][1] < depth:
            current, level = queue.pop(0)
            if current in visited:
                continue

            visited.add(current)
            if current in self._computation_graph:
                for dependent in self._computation_graph[current].get("dependents", []):
                    if dependent not in visited:
                        queue.append((dependent, level + 1))
                        if dependent in self._wave_fields:
                            # Propagate update (simplified)
                            self._wave_fields[dependent].values += (
                                self._wave_fields[current].gradients * 0.1
                            )
                            propagation_info["affected_fields"].append(
                                {"field": dependent, "level": level + 1}
                            )

        return propagation_info

    async def _check_convergence(
        self, name: str, tolerance: float = 1e-6
    ) -> Dict[str, Any]:
        """
        Check convergence of updates.

        Analyzes gradient magnitudes and update history.
        """
        if name not in self._wave_fields:
            raise ValueError(f"Field {name} not found")

        field = self._wave_fields[name]
        updates = self._particle_updates[name]

        if not updates:
            return {"field": name, "converged": True, "reason": "No updates applied"}

        recent_magnitudes = [update["magnitude"] for update in updates[-10:]]

        gradient_magnitude = np.linalg.norm(field.gradients)

        return {
            "field": name,
            "converged": gradient_magnitude < tolerance,
            "gradient_magnitude": gradient_magnitude,
            "update_trend": np.mean(recent_magnitudes),
            "stability": np.std(recent_magnitudes),
        }

    async def _get_update_history(self, name: str) -> Dict[str, Any]:
        """
        Get history of updates for a field.

        Returns update statistics and convergence information.
        """
        if name not in self._particle_updates:
            raise ValueError(f"No update history for field {name}")

        updates = self._particle_updates[name]
        magnitudes = [update["magnitude"] for update in updates]

        return {
            "field": name,
            "total_updates": len(updates),
            "magnitude_mean": np.mean(magnitudes),
            "magnitude_std": np.std(magnitudes),
            "convergence_trend": self._analyze_convergence_trend(magnitudes),
        }

    def _calculate_smoothness(self, values: np.ndarray) -> float:
        """Calculate smoothness of a field's values."""
        # Simplified smoothness metric using total variation
        return 1.0 / (1.0 + np.sum(np.abs(np.gradient(values))))

    def _analyze_convergence_trend(self, magnitudes: List[float]) -> Dict[str, Any]:
        """Analyze convergence trend from update magnitudes."""
        if len(magnitudes) < 2:
            return {"trend": "insufficient_data", "rate": 0.0}

        # Simplified trend analysis
        recent = magnitudes[-10:] if len(magnitudes) > 10 else magnitudes
        diffs = np.diff(recent)

        return {
            "trend": "decreasing" if np.mean(diffs) < 0 else "increasing",
            "rate": abs(np.mean(diffs)),
            "stability": np.std(diffs),
        }

    async def _cleanup_resources(self) -> None:
        """Clean up differential resources."""
        self._wave_fields.clear()
        self._particle_updates.clear()
        self._gradient_history.clear()
        self._computation_graph.clear()
