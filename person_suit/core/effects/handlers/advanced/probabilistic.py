"""
Probabilistic Effect Handler
========================

Handles probabilistic operations with wave-particle duality. In wave state,
operations exist as probability distributions, collapsing to concrete
particle states upon measurement.
"""

import logging
from dataclasses import dataclass, field
from typing import Any, Dict, Generic, List, Optional, TypeVar, Tuple, Union
from uuid import uuid4

import numpy as np

from ...core.types import Probabilistic as ProbabilisticEffectType
from ...interfaces import EffectInterface
from person_suit.core.effects.handlers import AsyncEffectHandler
from ..monitoring.decorators import particle_monitored, wave_monitored

logger = logging.getLogger(__name__)

T = TypeVar("T")  # Distribution value type


@dataclass
class Distribution:
    """Represents a probability distribution."""

    dist_id: str
    values: np.ndarray  # Distribution values
    probabilities: np.ndarray  # Probability weights
    parameters: Dict[str, Any]  # Distribution parameters
    metadata: Dict[str, Any]  # Additional metadata
    measurement_history: List[Dict[str, Any]] = field(default_factory=list)


class ProbabilisticHandler(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>[T]):
    """
    Handles probabilistic operations with wave-particle duality.

    Features:
    - Distribution creation
    - Probability transformation
    - Quantum superposition
    - Measurement effects
    """

    def __init__(self):
        """Initialize the probabilistic handler."""
        super().__init__(
            supported_types=[ProbabilisticEffectType],
            required_capabilities=[],  # Add capabilities if needed
        )
        self._distributions: Dict[str, Distribution] = {}
        self._quantum_states: Dict[str, np.ndarray] = {}
        self._measurement_history: Dict[str, List[Dict[str, Any]]] = {}

    @wave_monitored()
    async def _async_wave_operation(
        self, effect: EffectInterface, context: Dict[str, Any]
    ) -> Any:
        """
        Handle wave state operations.

        In wave state, we:
        - Create distributions
        - Transform probabilities
        - Create superpositions
        - Analyze interference
        """
        operation = effect.operation
        params = effect.parameters

        if operation == "create_distribution":
            return await self._create_distribution(
                params["values"],
                params["probabilities"],
                params.get("parameters", {}),
                params.get("metadata", {}),
            )
        elif operation == "transform_distribution":
            return await self._transform_distribution(
                params["dist_id"], params["transformation"]
            )
        elif operation == "create_superposition":
            return await self._create_quantum_superposition(
                params["states"], params.get("weights", None)
            )
        elif operation == "analyze_interference":
            return await self._analyze_interference_patterns(params["states"])
        else:
            raise ValueError(f"Unknown wave operation: {operation}")

    @particle_monitored()
    async def _async_particle_operation(
        self, effect: EffectInterface, context: Dict[str, Any]
    ) -> Any:
        """
        Handle particle state operations.

        In particle state, we:
        - Sample distributions
        - Measure quantum states
        - Record observations
        - Calculate statistics
        """
        operation = effect.operation
        params = effect.parameters

        if operation == "sample":
            return await self._sample_distribution(
                params["dist_id"], params.get("num_samples", 1)
            )
        elif operation == "measure":
            return await self._measure_quantum_state(
                params["state_id"], params.get("basis", "computational")
            )
        elif operation == "get_statistics":
            return await self._calculate_statistics(params["dist_id"])
        elif operation == "expectation":
            return await self._calculate_expectation_value(
                params["dist_id"], params.get("operator", None)
            )
        else:
            raise ValueError(f"Unknown particle operation: {operation}")

    async def _create_distribution(
        self,
        values: List[Any],
        probabilities: List[float],
        parameters: Dict[str, Any],
        metadata: Dict[str, Any],
    ) -> Dict[str, Any]:
        """
        Create a probability distribution.

        Initializes a new distribution with given values and probabilities.
        """
        dist_id = str(uuid4())

        # Normalize probabilities
        prob_array = np.array(probabilities)
        prob_array = prob_array / np.sum(prob_array)

        distribution = Distribution(
            dist_id=dist_id,
            values=np.array(values),
            probabilities=prob_array,
            parameters=parameters,
            metadata=metadata,
        )

        self._distributions[dist_id] = distribution

        return {
            "dist_id": dist_id,
            "entropy": self._calculate_entropy(prob_array),
            "num_states": len(values),
        }

    async def _transform_distribution(
        self, dist_id: str, transformation: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Transform a probability distribution.

        Applies transformation to probabilities while preserving normalization.
        """
        if dist_id not in self._distributions:
            raise ValueError(f"Distribution {dist_id} not found")

        dist = self._distributions[dist_id]

        if transformation["type"] == "scale":
            new_probs = dist.probabilities * transformation["factor"]
        elif transformation["type"] == "power":
            new_probs = dist.probabilities ** transformation["exponent"]
        elif transformation["type"] == "shift":
            new_probs = dist.probabilities + transformation["amount"]
        else:
            raise ValueError(f"Unknown transformation: {transformation['type']}")

        # Renormalize
        new_probs = new_probs / np.sum(new_probs)

        # Update distribution
        dist.probabilities = new_probs

        return {
            "dist_id": dist_id,
            "transformation": transformation["type"],
            "new_entropy": self._calculate_entropy(new_probs),
        }

    async def _create_quantum_superposition(
        self, states: List[str], weights: Optional[List[float]] = None
    ) -> Dict[str, Any]:
        """
        Create quantum superposition state.

        Combines multiple states into a quantum superposition.
        """
        if not weights:
            weights = [1.0 / len(states)] * len(states)

        # Create superposition state
        state_id = str(uuid4())
        superposition = np.zeros(len(states), dtype=complex)

        for i, (state, weight) in enumerate(zip(states, weights)):
            if state in self._quantum_states:
                superposition[i] = weight * self._quantum_states[state]

        # Normalize
        superposition /= np.linalg.norm(superposition)

        self._quantum_states[state_id] = superposition

        return {
            "state_id": state_id,
            "num_components": len(states),
            "coherence": self._calculate_coherence(superposition),
        }

    async def _analyze_interference_patterns(self, states: List[str]) -> Dict[str, Any]:
        """
        Analyze interference between quantum states.

        Calculates interference patterns and phase relationships.
        """
        patterns = []
        for i, state1 in enumerate(states):
            for j, state2 in enumerate(states[i + 1 :], i + 1):
                if state1 in self._quantum_states and state2 in self._quantum_states:
                    # Calculate interference
                    s1 = self._quantum_states[state1]
                    s2 = self._quantum_states[state2]

                    interference = np.abs(np.vdot(s1, s2)) ** 2
                    phase = np.angle(np.vdot(s1, s2))

                    patterns.append(
                        {
                            "states": (state1, state2),
                            "interference": interference,
                            "phase": phase,
                        }
                    )

        return {"patterns": patterns, "total_patterns": len(patterns)}

    async def _sample_distribution(
        self, dist_id: str, num_samples: int = 1
    ) -> Dict[str, Any]:
        """
        Sample from probability distribution.

        Generates samples according to probability weights.
        """
        if dist_id not in self._distributions:
            raise ValueError(f"Distribution {dist_id} not found")

        dist = self._distributions[dist_id]

        # Generate samples
        indices = np.random.choice(
            len(dist.values), size=num_samples, p=dist.probabilities
        )
        samples = dist.values[indices]

        # Record sampling
        dist.measurement_history.append(
            {"type": "sample", "num_samples": num_samples, "values": samples.tolist()}
        )

        return {
            "dist_id": dist_id,
            "samples": samples.tolist(),
            "probabilities": dist.probabilities[indices].tolist(),
        }

    async def _measure_quantum_state(
        self, state_id: str, basis: str = "computational"
    ) -> Dict[str, Any]:
        """
        Measure quantum state.

        Performs measurement in specified basis.
        """
        if state_id not in self._quantum_states:
            raise ValueError(f"State {state_id} not found")

        state = self._quantum_states[state_id]

        # Perform measurement
        probabilities = np.abs(state) ** 2
        outcome = np.random.choice(len(state), p=probabilities)

        # Record measurement
        if state_id not in self._measurement_history:
            self._measurement_history[state_id] = []

        self._measurement_history[state_id].append(
            {"basis": basis, "outcome": outcome, "probability": probabilities[outcome]}
        )

        return {
            "state_id": state_id,
            "outcome": outcome,
            "probability": probabilities[outcome],
            "basis": basis,
        }

    async def _calculate_statistics(self, dist_id: str) -> Dict[str, Any]:
        """
        Calculate distribution statistics.

        Computes various statistical measures.
        """
        if dist_id not in self._distributions:
            raise ValueError(f"Distribution {dist_id} not found")

        dist = self._distributions[dist_id]

        # Calculate statistics
        mean = np.average(dist.values, weights=dist.probabilities)
        variance = np.average((dist.values - mean) ** 2, weights=dist.probabilities)
        entropy = self._calculate_entropy(dist.probabilities)

        return {
            "dist_id": dist_id,
            "mean": mean,
            "variance": variance,
            "entropy": entropy,
            "num_measurements": len(dist.measurement_history),
        }

    async def _calculate_expectation_value(
        self, dist_id: str, operator: Optional[np.ndarray] = None
    ) -> Dict[str, Any]:
        """
        Calculate expectation value.

        Computes expectation value of operator or values.
        """
        if dist_id not in self._distributions:
            raise ValueError(f"Distribution {dist_id} not found")

        dist = self._distributions[dist_id]

        if operator is not None:
            # Calculate with operator
            expectation = np.sum(operator @ dist.values * dist.probabilities)
        else:
            # Calculate direct expectation
            expectation = np.sum(dist.values * dist.probabilities)

        return {
            "dist_id": dist_id,
            "expectation": expectation,
            "operator_applied": operator is not None,
        }

    def _calculate_entropy(self, probabilities: np.ndarray) -> float:
        """Calculate Shannon entropy of probability distribution."""
        # Remove zero probabilities to avoid log(0)
        probs = probabilities[probabilities > 0]
        return -np.sum(probs * np.log2(probs))

    def _calculate_coherence(self, state: np.ndarray) -> float:
        """Calculate quantum state coherence."""
        return np.abs(np.sum(state)) / len(state)

    async def _cleanup_resources(self) -> None:
        """Clean up probabilistic resources."""
        self._distributions.clear()
        self._quantum_states.clear()
        self._measurement_history.clear()

        self._distributions.clear()
        self._quantum_states.clear()
        self._measurement_history.clear()
