"""
Memory Effect Handler
=================

This module provides a handler for memory operations in the effect system.
"""

import logging
from typing import Any, Dict, List, Optional

from ..base import BaseEffectHandler
from ...interfaces import EffectTypeInterface
from ...types import Memory

logger = logging.getLogger(__name__)


class MemoryEffectHandler(BaseEffectHandler):
    """
    Handler for memory operations.

    This handler integrates with the CAW paradigm by:
    1. Supporting wave-particle duality in memory operations
    2. Being context-aware in all operations
    3. Using wave functions for memory state representation
    4. Enabling adaptive computation based on context
    """

    def __init__(self):
        """Initialize the memory effect handler."""
        super().__init__("memory_handler")
        self._memory: Dict[str, Dict[str, Any]] = {}

    def can_handle(self, effect_type: EffectTypeInterface) -> bool:
        """Check if this handler can handle the given effect type."""
        return effect_type == Memory

    def can_handle_operation(
        self, effect_type: EffectTypeInterface, operation: str
    ) -> bool:
        """Check if this handler can handle the given operation."""
        return self.can_handle(
            effect_type
        ) and operation in self.get_supported_operations(effect_type)

    def get_supported_operations(self, effect_type: EffectTypeInterface) -> List[str]:
        """Get the operations supported by this handler."""
        if self.can_handle(effect_type):
            return ["store", "retrieve", "delete", "exists", "clear"]
        return []

    def handle(
        self,
        effect_type: EffectTypeInterface,
        operation: str,
        context: Optional[Dict[str, Any]] = None,
        **kwargs: Any,
    ) -> Any:
        """Handle a memory operation."""
        if not self.can_handle_operation(effect_type, operation):
            raise ValueError(
                f"Cannot handle operation {operation} for effect type {effect_type}"
            )

        # Get operation parameters
        namespace = kwargs.pop("namespace", "default")

        if operation == "store":
            return self._handle_store(namespace=namespace, **kwargs)
        elif operation == "retrieve":
            return self._handle_retrieve(namespace=namespace, **kwargs)
        elif operation == "delete":
            return self._handle_delete(namespace=namespace, **kwargs)
        elif operation == "exists":
            return self._handle_exists(namespace=namespace, **kwargs)
        elif operation == "clear":
            return self._handle_clear(namespace=namespace, **kwargs)
        else:
            raise ValueError(f"Unsupported operation: {operation}")

    def _get_namespace(self, namespace: str) -> Dict[str, Any]:
        """Get or create a namespace."""
        if namespace not in self._memory:
            self._memory[namespace] = {}
        return self._memory[namespace]

    def _handle_store(
        self, key: str, value: Any, namespace: str = "default", **kwargs: Any
    ) -> Any:
        """Handle storing a value in memory."""
        logger.debug(f"Storing value for key: {key} in namespace: {namespace}")
        ns = self._get_namespace(namespace)
        ns[key] = value
        return {
            "status": "success",
            "operation": "store",
            "namespace": namespace,
            "key": key,
        }

    def _handle_retrieve(
        self,
        key: str,
        default: Optional[Any] = None,
        namespace: str = "default",
        **kwargs: Any,
    ) -> Any:
        """Handle retrieving a value from memory."""
        logger.debug(f"Retrieving value for key: {key} from namespace: {namespace}")
        ns = self._get_namespace(namespace)
        value = ns.get(key, default)
        return {
            "status": "success",
            "operation": "retrieve",
            "namespace": namespace,
            "key": key,
            "value": value,
        }

    def _handle_delete(
        self, key: str, namespace: str = "default", **kwargs: Any
    ) -> Any:
        """Handle deleting a value from memory."""
        logger.debug(f"Deleting value for key: {key} from namespace: {namespace}")
        ns = self._get_namespace(namespace)
        if key in ns:
            del ns[key]
            return {
                "status": "success",
                "operation": "delete",
                "namespace": namespace,
                "key": key,
            }
        return {
            "status": "not_found",
            "operation": "delete",
            "namespace": namespace,
            "key": key,
        }

    def _handle_exists(
        self, key: str, namespace: str = "default", **kwargs: Any
    ) -> Any:
        """Handle checking if a key exists in memory."""
        logger.debug(f"Checking existence of key: {key} in namespace: {namespace}")
        ns = self._get_namespace(namespace)
        exists = key in ns
        return {
            "status": "success",
            "operation": "exists",
            "namespace": namespace,
            "key": key,
            "exists": exists,
        }

    def _handle_clear(self, namespace: str = "default", **kwargs: Any) -> Any:
        """Handle clearing all values from a namespace."""
        logger.debug(f"Clearing all values from namespace: {namespace}")
        if namespace in self._memory:
            self._memory[namespace].clear()
        return {"status": "success", "operation": "clear", "namespace": namespace}
