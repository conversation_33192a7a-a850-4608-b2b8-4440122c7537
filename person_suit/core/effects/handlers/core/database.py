"""
Database Effect Handler
====================

This module defines the handler for Database effects in the Person Suit effect system.
Database effects include operations like querying, inserting, updating, or deleting
data from a database.

Related Files:
- person_suit/core/infrastructure/effects/core.py: Core effect system components
- person_suit.core.effects.interfaces.py: Effect system interfaces
- person_suit/core/infrastructure/effects/registry.py: Effect handler registry

Dependencies:
- typing>=4.0.0: For type annotations
"""

import asyncio
import logging
from contextlib import asynccontextmanager
from typing import Any, Dict, List, Optional

import asyncpg
from sqlalchemy.engine import URL
from sqlalchemy.ext.asyncio import AsyncEngine, create_async_engine
from sqlalchemy.pool import AsyncAdapted<PERSON><PERSON>uePool

from ...core._internal_types.types import RuntimeContext
from ...core.types import Database as DatabaseEffectType
from ...core.types import Effect
from . import BaseEffectHandler

from ..monitoring.decorators import particle_monitored, wave_monitored

# Configure logger
logger = logging.getLogger(__name__)


class DatabaseHandler(BaseEffectHandler):
    """Handler for database operations with transaction management.

    Supports:
    - Connection pooling
    - Transaction management
    - Query optimization
    - Access pattern analysis
    - Resource monitoring
    """

    def __init__(self):
        """Initialize the handler."""
        super().__init__()
        self._engines: Dict[str, AsyncEngine] = {}
        self._pools: Dict[str, asyncpg.Pool] = {}
        self._active_transactions: Dict[str, asyncpg.Transaction] = {}
        self._access_patterns: Dict[str, List[Dict[str, Any]]] = {}

    def can_handle(self, effect: Effect) -> bool:
        """Check if handler can handle an effect.

        Args:
            effect: The effect to check

        Returns:
            True if effect type is 'database'
        """
        return effect.effect_type == "database"

    async def analyze_wave_state(
        self, effect: Effect, context: RuntimeContext
    ) -> Dict[str, Any]:
        """Analyze potential database operations in wave state.

        Args:
            effect: The effect to analyze
            context: Runtime context

        Returns:
            Analysis results including:
            - access_pattern: Detected access pattern
            - resource_usage: Resource usage estimates
            - optimization_hints: Query optimization suggestions
        """
        operation = effect.operation
        parameters = effect.parameters

        # Analyze access pattern
        access_pattern = self._analyze_access_pattern(parameters)

        # Estimate resource usage
        resource_usage = self._estimate_resource_usage(operation, parameters)

        # Generate optimization hints
        optimization_hints = self._generate_optimization_hints(
            operation, parameters, access_pattern
        )

        # Determine if operation should be executed
        should_execute = (
            resource_usage["connection_available"]
            and not resource_usage["exceeds_limits"]
            and not optimization_hints["requires_optimization"]
        )

        return {
            "access_pattern": access_pattern,
            "resource_usage": resource_usage,
            "optimization_hints": optimization_hints,
            "should_execute": should_execute,
        }

    async def execute_particle_state(
        self, effect: Effect, context: RuntimeContext
    ) -> Any:
        """Execute database operations in particle state.

        Args:
            effect: The effect to execute
            context: Runtime context

        Returns:
            Operation result
        """
        operation = effect.operation
        parameters = effect.parameters

        try:
            if operation == "query":
                return await self._execute_query(parameters)
            elif operation == "execute":
                return await self._execute_statement(parameters)
            elif operation == "transaction":
                return await self._execute_transaction(parameters)
            elif operation == "batch":
                return await self._execute_batch(parameters)
            else:
                raise ValueError(f"Unknown operation: {operation}")
        except Exception as e:
            logger.error(f"Database operation failed: {e}")
            raise

    async def _initialize_resources(self) -> None:
        """Initialize handler resources."""
        # Initialize connection pools
        for db_config in self._get_database_configs():
            await self._initialize_pool(db_config)

    async def _cleanup_resources(self) -> None:
        """Clean up handler resources."""
        # Close all active transactions
        for tx in self._active_transactions.values():
            try:
                await tx.rollback()
            except Exception as e:
                logger.error(f"Failed to rollback transaction: {e}")

        # Close all connection pools
        for pool in self._pools.values():
            try:
                await pool.close()
            except Exception as e:
                logger.error(f"Failed to close pool: {e}")

        self._pools.clear()
        self._active_transactions.clear()
        self._access_patterns.clear()

    async def _initialize_pool(self, config: Dict[str, Any]) -> None:
        """Initialize database connection pool.

        Args:
            config: Database configuration
        """
        db_name = config["name"]
        if db_name in self._pools:
            return

        if config["type"] == "postgresql":
            dsn = URL.create(
                "postgresql+asyncpg",
                username=config["username"],
                password=config["password"],
                host=config["host"],
                port=config["port"],
                database=config["database"],
            )

            # Create SQLAlchemy engine
            self._engines[db_name] = create_async_engine(
                dsn,
                poolclass=AsyncAdaptedQueuePool,
                pool_size=config.get("pool_size", 10),
                max_overflow=config.get("max_overflow", 20),
            )

            # Create asyncpg pool
            self._pools[db_name] = await asyncpg.create_pool(
                str(dsn),
                min_size=config.get("min_size", 5),
                max_size=config.get("pool_size", 10),
            )

    @asynccontextmanager
    async def _get_connection(self, db_name: str):
        """Get database connection from pool.

        Args:
            db_name: Database name

        Yields:
            Database connection
        """
        pool = self._pools.get(db_name)
        if not pool:
            raise ValueError(f"No pool found for database: {db_name}")

        async with pool.acquire() as conn:
            try:
                yield conn
            finally:
                pass  # Connection automatically returned to pool

    async def _execute_query(self, parameters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Execute database query.

        Args:
            parameters: Query parameters

        Returns:
            Query results
        """
        db_name = parameters["database"]
        query = parameters["query"]
        args = parameters.get("args", [])

        async with self._get_connection(db_name) as conn:
            result = await conn.fetch(query, *args)
            return [dict(r) for r in result]

    async def _execute_statement(self, parameters: Dict[str, Any]) -> Optional[int]:
        """Execute database statement.

        Args:
            parameters: Statement parameters

        Returns:
            Number of affected rows if applicable
        """
        db_name = parameters["database"]
        statement = parameters["statement"]
        args = parameters.get("args", [])

        async with self._get_connection(db_name) as conn:
            result = await conn.execute(statement, *args)
            if result and "UPDATE" in statement.upper():
                return int(result.split()[-1])
            return None

    async def _execute_transaction(self, parameters: Dict[str, Any]) -> Any:
        """Execute operations in transaction.

        Args:
            parameters: Transaction parameters

        Returns:
            Transaction result
        """
        db_name = parameters["database"]
        operations = parameters["operations"]

        async with self._get_connection(db_name) as conn:
            async with conn.transaction():
                results = []
                for op in operations:
                    if op["type"] == "query":
                        result = await conn.fetch(op["query"], *op.get("args", []))
                        results.append([dict(r) for r in result])
                    elif op["type"] == "execute":
                        result = await conn.execute(
                            op["statement"], *op.get("args", [])
                        )
                        results.append(result)
                return results

    async def _execute_batch(self, parameters: Dict[str, Any]) -> List[Any]:
        """Execute batch operations.

        Args:
            parameters: Batch parameters

        Returns:
            Operation results
        """
        db_name = parameters["database"]
        statements = parameters["statements"]

        async with self._get_connection(db_name) as conn:
            return await conn.executemany(statements["query"], statements["args"])

    def _analyze_access_pattern(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze database access pattern.

        Args:
            parameters: Operation parameters

        Returns:
            Access pattern analysis
        """
        db_name = parameters["database"]
        patterns = self._access_patterns.get(db_name, [])

        if not patterns:
            return {"type": "first_access", "frequency": 0}

        # Analyze temporal patterns
        current_time = asyncio.get_event_loop().time()
        recent_patterns = [
            p
            for p in patterns
            if current_time - p["timestamp"] < 60  # Last minute
        ]

        return {
            "type": "read" if "query" in parameters else "write",
            "frequency": len(recent_patterns) / 60,  # Access per second
            "temporal_locality": len(recent_patterns) / len(patterns),
        }

    def _estimate_resource_usage(
        self, operation: str, parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Estimate resource usage for operation.

        Args:
            operation: Operation type
            parameters: Operation parameters

        Returns:
            Resource usage estimates
        """
        db_name = parameters["database"]
        pool = self._pools.get(db_name)

        if not pool:
            return {
                "connection_available": False,
                "exceeds_limits": True,
                "error": "No connection pool",
            }

        # Check connection availability
        available_connections = pool.get_size()
        required_connections = 1

        if operation == "transaction":
            required_connections = len(parameters["operations"])

        return {
            "connection_available": available_connections >= required_connections,
            "exceeds_limits": False,
            "current_connections": pool.get_size(),
            "max_connections": pool.get_max_size(),
        }

    def _generate_optimization_hints(
        self, operation: str, parameters: Dict[str, Any], access_pattern: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate query optimization hints.

        Args:
            operation: Operation type
            parameters: Operation parameters
            access_pattern: Access pattern analysis

        Returns:
            Optimization hints
        """
        hints = {"requires_optimization": False, "suggestions": []}

        if operation == "query":
            query = parameters["query"].upper()

            # Check for potential full table scans
            if "SELECT *" in query and "WHERE" not in query:
                hints["requires_optimization"] = True
                hints["suggestions"].append("Consider specifying required columns")

            # Check for missing indices
            if "WHERE" in query and "INDEX" not in query:
                hints["suggestions"].append("Consider adding appropriate indices")

            # Check for high frequency access
            if access_pattern["frequency"] > 10:  # More than 10 per second
                hints["suggestions"].append("Consider caching frequently accessed data")

        elif operation == "transaction":
            # Check transaction size
            if len(parameters["operations"]) > 10:
                hints["requires_optimization"] = True
                hints["suggestions"].append("Consider breaking down large transactions")

        return hints

    def _get_database_configs(self) -> List[Dict[str, Any]]:
        """Get database configurations.

        Returns:
            List of database configurations
        """
        # This should be loaded from configuration
        return [
            {
                "name": "main",
                "type": "postgresql",
                "host": "localhost",
                "port": 5432,
                "database": "personsuit",
                "username": "personsuit",
                "password": "personsuit",
                "pool_size": 10,
                "min_size": 5,
                "max_overflow": 20,
            }
        ]

    @particle_monitored()
    async def _async_particle_operation(
        self, effect: Effect, context: RuntimeContext
    ) -> Any:
        # Implementation of the method
        pass
