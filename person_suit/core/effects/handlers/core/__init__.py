"""
Core Effect Handlers
==================

This package provides implementations of fundamental effect handlers for
basic system operations. These handlers manage effects in both potential
and concrete states, implementing core system functionality.

Available Handlers:
-----------------
- IOHandler: File and stream operations
- DatabaseHandler: Database operations
- NetworkHandler: Network communication
- StateHandler: State management
- ComputationHandler: Basic computation
"""

from .base import BaseEffectHandler
from .computation import ComputationHandler
from .context import ContextManager
from .database import DatabaseHandler
from .io import IOHandler
from .network import NetworkHandler
from .state import StateHandler

__all__ = [
    "BaseEffectHandler",
    "IOHandler",
    "DatabaseHandler",
    "NetworkHandler",
    "StateHandler",
    "ComputationHandler",
    "ContextManager",
]
