"""
Person Suit - Base Effect Handler

Provides a base implementation for effect handlers.
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, Generic, List, Optional, Set, Type, TypeVar

from ...interfaces import (
    EffectHandlerInterface,
    EffectTypeInterface,
)

from ...interfaces import (
    EffectHandlerInterface,
    EffectTypeInterface,
)

from ...core._internal_types.types import (
    EffectContext,
    HandlerID,
    HandlerMetrics,
    HandlerState,
    ResourceMetrics,
)

from ...core.base.interfaces import Effect, RuntimeContext

logger = logging.getLogger(__name__)

T = TypeVar("T")


class BaseEffectHandler(EffectHandlerInterface, ABC, Generic[T]):
    """
    Base implementation of an effect handler.

    Provides common functionality for effect handlers.
    """

    def __init__(self):
        """Initialize a new base effect handler."""
        self._supported_effect_types: Set[Type[EffectTypeInterface]] = set()
        self._register_supported_effect_types()
        self._id: Optional[HandlerID] = None
        self._state: HandlerState = HandlerState.UNINITIALIZED
        self._metrics: HandlerMetrics = HandlerMetrics()
        self._resource_metrics: ResourceMetrics = ResourceMetrics()
        self._lock = asyncio.Lock()

    @abstractmethod
    def _register_supported_effect_types(self) -> None:
        """
        Register the effect types supported by this handler.

        This method should be implemented by subclasses to register the effect types
        they support using the _register_effect_type method.
        """
        pass

    def _register_effect_type(self, effect_type: Type[EffectTypeInterface]) -> None:
        """
        Register an effect type as supported by this handler.

        Args:
            effect_type: The effect type class to register
        """
        self._supported_effect_types.add(effect_type)

    def can_handle(self, effect_type: EffectTypeInterface) -> bool:
        """
        Check if this handler can handle the given effect type.

        Args:
            effect_type: The effect type to check

        Returns:
            True if this handler can handle the effect type, False otherwise
        """
        return any(
            isinstance(effect_type, supported_type)
            for supported_type in self._supported_effect_types
        )

    def can_handle_operation(
        self, effect_type: EffectTypeInterface, operation: str
    ) -> bool:
        """
        Check if this handler can handle the given operation for the effect type.

        Args:
            effect_type: The effect type
            operation: The operation to check

        Returns:
            True if this handler can handle the operation, False otherwise
        """
        if not self.can_handle(effect_type):
            return False

        return operation in self.get_supported_operations(effect_type)

    @abstractmethod
    def get_supported_operations(self, effect_type: EffectTypeInterface) -> List[str]:
        """
        Get the operations supported by this handler for the given effect type.

        Args:
            effect_type: The effect type

        Returns:
            A list of supported operations
        """
        pass

    @abstractmethod
    def handle(
        self,
        effect_type: EffectTypeInterface,
        operation: str,
        context: Optional[Dict[str, Any]] = None,
        **kwargs: Any,
    ) -> Any:
        """
        Handle an effect.

        Args:
            effect_type: The type of the effect
            operation: The operation to perform
            context: Optional context information
            **kwargs: The parameters for the operation

        Returns:
            The result of handling the effect

        Raises:
            ValueError: If the handler doesn't support the effect type or operation
        """
        pass

    @property
    def id(self) -> Optional[HandlerID]:
        """Get the handler's ID."""
        return self._id

    @property
    def state(self) -> HandlerState:
        """Get the handler's state."""
        return self._state

    async def initialize(self, handler_id: HandlerID) -> None:
        """Initialize the handler.

        Args:
            handler_id: The ID to assign to this handler
        """
        async with self._lock:
            if self._state != HandlerState.UNINITIALIZED:
                raise RuntimeError("Handler already initialized")

            try:
                self._state = HandlerState.INITIALIZING
                self._id = handler_id
                await self._initialize_resources()
                self._state = HandlerState.READY
                logger.info(f"Handler {self._id} initialized")
            except Exception as e:
                self._state = HandlerState.ERROR
                logger.error(f"Handler initialization failed: {e}")
                raise

    async def cleanup(self) -> None:
        """Clean up handler resources."""
        async with self._lock:
            if self._state == HandlerState.UNINITIALIZED:
                return

            try:
                self._state = HandlerState.CLEANING_UP
                await self._cleanup_resources()
                self._state = HandlerState.STOPPED
                logger.info(f"Handler {self._id} cleaned up")
            except Exception as e:
                self._state = HandlerState.ERROR
                logger.error(f"Handler cleanup failed: {e}")
                raise

    async def handle_effect(self, effect: Effect, context: RuntimeContext) -> T:
        """Handle an effect.

        Args:
            effect: The effect to handle
            context: Runtime context

        Returns:
            Effect handling result
        """
        async with self._lock:
            if self._state != HandlerState.READY:
                raise RuntimeError(f"Handler not ready (state: {self._state})")

            self._state = HandlerState.BUSY
            effect_context = EffectContext(
                effect_id=effect.effect_id, handler_id=self._id
            )

            try:
                # Analyze wave state
                wave_analysis = await self.analyze_wave_state(effect, context)
                effect_context.metrics.wave_analysis_time = (
                    asyncio.get_event_loop().time()
                )

                # Execute particle state if wave analysis allows
                if self._should_execute_particle_state(wave_analysis):
                    result = await self.execute_particle_state(effect, context)
                    effect_context.metrics.particle_execution_time = (
                        asyncio.get_event_loop().time()
                    )
                else:
                    result = wave_analysis.get("default_result")

                # Update metrics
                await self._update_metrics(effect_context, True)

                return result
            except Exception:
                await self._update_metrics(effect_context, False)
                raise
            finally:
                self._state = HandlerState.READY

    @abstractmethod
    async def analyze_wave_state(
        self, effect: Effect, context: RuntimeContext
    ) -> Dict[str, Any]:
        """Analyze effect in wave state.

        Args:
            effect: The effect to analyze
            context: Runtime context

        Returns:
            Analysis results
        """
        pass

    @abstractmethod
    async def execute_particle_state(
        self, effect: Effect, context: RuntimeContext
    ) -> T:
        """Execute effect in particle state.

        Args:
            effect: The effect to execute
            context: Runtime context

        Returns:
            Execution result
        """
        pass

    async def get_metrics(self) -> HandlerMetrics:
        """Get handler metrics."""
        return self._metrics

    async def get_resource_metrics(self) -> ResourceMetrics:
        """Get resource metrics."""
        return self._resource_metrics

    @abstractmethod
    async def _initialize_resources(self) -> None:
        """Initialize handler resources."""
        pass

    @abstractmethod
    async def _cleanup_resources(self) -> None:
        """Clean up handler resources."""
        pass

    def _should_execute_particle_state(self, wave_analysis: Dict[str, Any]) -> bool:
        """Determine if particle state should be executed based on wave analysis."""
        return wave_analysis.get("should_execute", True)

    async def _update_metrics(
        self, effect_context: EffectContext, success: bool
    ) -> None:
        """Update handler metrics.

        Args:
            effect_context: The effect execution context
            success: Whether handling was successful
        """
        self._metrics.total_operations += 1
        if success:
            self._metrics.successful_operations += 1
            self._metrics.average_wave_time = (
                self._metrics.average_wave_time
                * (self._metrics.successful_operations - 1)
                + effect_context.metrics.wave_analysis_time
            ) / self._metrics.successful_operations
            if effect_context.metrics.particle_execution_time:
                self._metrics.average_particle_time = (
                    self._metrics.average_particle_time
                    * (self._metrics.successful_operations - 1)
                    + effect_context.metrics.particle_execution_time
                ) / self._metrics.successful_operations
        else:
            self._metrics.failed_operations += 1
            self._metrics.error_counts[str(type(effect_context).__name__)] = (
                self._metrics.error_counts.get(str(type(effect_context).__name__), 0)
                + 1
            )
