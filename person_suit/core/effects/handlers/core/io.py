"""IO Effect Handler.

This module implements the IOHandler for handling file and stream operations
in the effect system. It supports both synchronous and asynchronous I/O
operations with proper resource management.

The handler maintains both wave and particle states:
- Wave state: Represents potential I/O operations and access patterns
- Particle state: Represents concrete I/O operations and their execution
"""

import asyncio
import logging
import os
from pathlib import Path
from typing import Any, BinaryIO, Dict, List, Optional

from ...core._internal_types.types import RuntimeContext
from ...core.types import IO as IOEffectType
from ...core.types import Effect
from . import BaseEffectHandler

from ..monitoring.decorators import particle_monitored, wave_monitored

logger = logging.getLogger(__name__)


class IOHandler(BaseEffectHandler):
    """Handler for file and stream operations.

    Supports:
    - File read/write operations
    - Stream handling
    - Resource management
    - Access pattern optimization
    """

    def __init__(self):
        """Initialize the handler."""
        super().__init__()
        self._open_files: Dict[str, BinaryIO] = {}
        self._access_patterns: Dict[str, List[Dict[str, Any]]] = {}

    def can_handle(self, effect: Effect) -> bool:
        """Check if handler can handle an effect.

        Args:
            effect: The effect to check

        Returns:
            True if effect type is 'io'
        """
        return effect.effect_type == "io"

    async def analyze_wave_state(
        self, effect: Effect, context: RuntimeContext
    ) -> Dict[str, Any]:
        """Analyze potential I/O operations in wave state.

        Args:
            effect: The effect to analyze
            context: Runtime context

        Returns:
            Analysis results including:
            - access_pattern: Detected access pattern
            - locality: Data locality score
            - resource_availability: Resource availability check
        """
        operation = effect.operation
        path = effect.parameters.get("path")

        if not path:
            raise ValueError("Path parameter is required")

        # Analyze access pattern
        access_pattern = self._analyze_access_pattern(path)

        # Check data locality
        locality = self._check_data_locality(path)

        # Check resource availability
        resources = self._check_resource_availability(path, operation)

        return {
            "access_pattern": access_pattern,
            "locality": locality,
            "resource_availability": resources,
            "should_execute": resources["available"],
        }

    async def execute_particle_state(
        self, effect: Effect, context: RuntimeContext
    ) -> Any:
        """Execute I/O operations in particle state.

        Args:
            effect: The effect to execute
            context: Runtime context

        Returns:
            Operation result
        """
        operation = effect.operation
        path = effect.parameters.get("path")
        mode = effect.parameters.get("mode", "rb")
        data = effect.parameters.get("data")

        try:
            if operation == "read":
                return await self._read_file(path, mode)
            elif operation == "write":
                return await self._write_file(path, data, mode)
            elif operation == "append":
                return await self._append_file(path, data)
            elif operation == "delete":
                return await self._delete_file(path)
            else:
                raise ValueError(f"Unknown operation: {operation}")
        except Exception as e:
            logger.error(f"IO operation failed: {e}")
            raise

    async def _initialize_resources(self) -> None:
        """Initialize handler resources."""
        self._open_files.clear()
        self._access_patterns.clear()

    async def _cleanup_resources(self) -> None:
        """Clean up handler resources."""
        for file in self._open_files.values():
            try:
                file.close()
            except Exception as e:
                logger.error(f"Failed to close file: {e}")
        self._open_files.clear()
        self._access_patterns.clear()

    def _analyze_access_pattern(self, path: str) -> Dict[str, Any]:
        """Analyze file access pattern.

        Args:
            path: File path

        Returns:
            Access pattern analysis
        """
        patterns = self._access_patterns.get(path, [])
        if not patterns:
            return {"type": "first_access", "count": 0}

        return {
            "type": "sequential" if self._is_sequential(patterns) else "random",
            "count": len(patterns),
        }

    def _is_sequential(self, patterns: List[Dict[str, Any]]) -> bool:
        """Check if access pattern is sequential.

        Args:
            patterns: List of access patterns

        Returns:
            True if pattern is sequential
        """
        if len(patterns) < 2:
            return True

        positions = [p.get("position", 0) for p in patterns]
        diffs = [positions[i + 1] - positions[i] for i in range(len(positions) - 1)]
        return all(d == diffs[0] for d in diffs)

    def _check_data_locality(self, path: str) -> float:
        """Check data locality for a file.

        Args:
            path: File path

        Returns:
            Locality score (0-1)
        """
        if path in self._open_files:
            return 1.0

        # Check if file is in cache (simplified)
        return 0.5

    def _check_resource_availability(self, path: str, operation: str) -> Dict[str, Any]:
        """Check resource availability for operation.

        Args:
            path: File path
            operation: Operation type

        Returns:
            Resource availability info
        """
        path_obj = Path(path)

        if operation in ("read", "append"):
            exists = path_obj.exists()
            readable = exists and os.access(path, os.R_OK)
            return {"available": readable, "exists": exists, "readable": readable}
        elif operation == "write":
            parent = path_obj.parent
            writable = parent.exists() and os.access(parent, os.W_OK)
            return {
                "available": writable,
                "exists": path_obj.exists(),
                "writable": writable,
            }
        elif operation == "delete":
            exists = path_obj.exists()
            deletable = exists and os.access(path, os.W_OK)
            return {"available": deletable, "exists": exists, "deletable": deletable}
        else:
            return {"available": False, "error": "Unknown operation"}

    async def _read_file(self, path: str, mode: str) -> bytes:
        """Read file contents.

        Args:
            path: File path
            mode: Open mode

        Returns:
            File contents
        """
        if path in self._open_files:
            file = self._open_files[path]
        else:
            file = open(path, mode)
            self._open_files[path] = file

        try:
            return file.read()
        finally:
            self._update_access_pattern(path, "read", file.tell())

    async def _write_file(self, path: str, data: bytes, mode: str) -> int:
        """Write data to file.

        Args:
            path: File path
            data: Data to write
            mode: Open mode

        Returns:
            Number of bytes written
        """
        if path in self._open_files:
            file = self._open_files[path]
        else:
            file = open(path, mode)
            self._open_files[path] = file

        try:
            return file.write(data)
        finally:
            self._update_access_pattern(path, "write", file.tell())

    async def _append_file(self, path: str, data: bytes) -> int:
        """Append data to file.

        Args:
            path: File path
            data: Data to append

        Returns:
            Number of bytes written
        """
        if path in self._open_files:
            file = self._open_files[path]
        else:
            file = open(path, "ab")
            self._open_files[path] = file

        try:
            return file.write(data)
        finally:
            self._update_access_pattern(path, "append", file.tell())

    async def _delete_file(self, path: str) -> bool:
        """Delete a file.

        Args:
            path: File path

        Returns:
            True if file was deleted
        """
        if path in self._open_files:
            self._open_files[path].close()
            del self._open_files[path]

        try:
            os.remove(path)
            return True
        except Exception as e:
            logger.error(f"Failed to delete file {path}: {e}")
            return False

    def _update_access_pattern(self, path: str, operation: str, position: int) -> None:
        """Update file access pattern tracking.

        Args:
            path: File path
            operation: Operation type
            position: File position
        """
        patterns = self._access_patterns.setdefault(path, [])
        patterns.append(
            {
                "operation": operation,
                "position": position,
                "timestamp": asyncio.get_event_loop().time(),
            }
        )
