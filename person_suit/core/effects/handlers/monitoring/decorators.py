"""
Monitoring Decorators
===================

Decorators for adding monitoring capabilities to effect handlers.
"""

import functools
import logging
import time
from dataclasses import dataclass
from typing import Any, Callable, Dict, Optional, TypeVar, cast

import anyio

# Corrected absolute import path
from ...interfaces import EffectInterface
from ...core.types import Telemetry as MonitoringEffectType

logger = logging.getLogger(__name__)

F = TypeVar("F", bound=Callable[..., Any])


@dataclass
class MonitoringContext:
    """Context for monitoring decorator."""

    handler_id: str
    method_name: str
    start_time: float
    metrics: Dict[str, Any]


def monitored(
    handler_id: Optional[str] = None,
    track_args: bool = False,
    track_result: bool = False,
) -> Callable[[F], F]:
    """
    Decorator for monitoring handler methods.

    Args:
        handler_id: Optional identifier for the handler. If not provided,
            will use the class name.
        track_args: Whether to track method arguments.
        track_result: Whether to track method return value.

    Returns:
        Decorated function that includes monitoring.
    """

    def decorator(func: F) -> F:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            # Get handler instance (self) if method
            instance = args[0] if args else None

            # Determine handler ID
            actual_handler_id = handler_id
            if not actual_handler_id and instance:
                actual_handler_id = instance.__class__.__name__

            # Create monitoring context
            context = MonitoringContext(
                handler_id=actual_handler_id,
                method_name=func.__name__,
                start_time=time.time(),
                metrics={},
            )

            # Track arguments if enabled
            if track_args:
                context.metrics["args"] = {
                    "positional": args[1:] if instance else args,
                    "keyword": kwargs,
                }

            try:
                # Execute original function
                result = await func(*args, **kwargs)

                # Calculate duration
                duration = time.time() - context.start_time
                context.metrics.update({"duration": duration, "success": True})

                # Track result if enabled
                if track_result:
                    try:
                        # Attempt to get a string representation
                        result_repr = str(result)
                        if len(result_repr) > 1000:
                            result_repr = result_repr[:1000] + "..."
                        context.metrics["result"] = result_repr
                    except Exception as e:
                        logger.warning(f"Failed to get result representation: {e}")

                return result

            except Exception as e:
                # Record error information
                context.metrics.update(
                    {
                        "success": False,
                        "error": {"type": type(e).__name__, "message": str(e)},
                    }
                )
                raise

            finally:
                # Always record metrics
                if instance and hasattr(instance, "_record_monitoring"):
                    try:
                        await instance._record_monitoring(context)
                    except Exception as e:
                        logger.error(f"Failed to record monitoring data: {e}")

        return cast(F, wrapper)

    return decorator


def wave_monitored(
    handler_id: Optional[str] = None, track_args: bool = False
) -> Callable[[F], F]:
    """
    Decorator specifically for monitoring wave state operations.

    Args:
        handler_id: Optional identifier for the handler.
        track_args: Whether to track method arguments.

    Returns:
        Decorated function that includes wave state monitoring.
    """
    return monitored(
        handler_id=handler_id,
        track_args=track_args,
        track_result=False,  # Wave operations track potentials
    )


def particle_monitored(
    handler_id: Optional[str] = None,
    track_args: bool = False,
    track_result: bool = True,
) -> Callable[[F], F]:
    """
    Decorator specifically for monitoring particle state operations.

    Args:
        handler_id: Optional identifier for the handler.
        track_args: Whether to track method arguments.
        track_result: Whether to track concrete results.

    Returns:
        Decorated function that includes particle state monitoring.
    """
    return monitored(
        handler_id=handler_id, track_args=track_args, track_result=track_result
    )


async def _record_monitoring(instance: Any, context: MonitoringContext) -> None:
    """
    Record monitoring data using the metrics handler.

    This method should be added to handler classes that use monitoring.

    Args:
        instance: Handler instance.
        context: Monitoring context with metrics.
    """
    # Create monitoring effect
    effect = EffectInterface(
        type=MonitoringEffectType,
        operation="track_handler",
        parameters={
            "handler_id": context.handler_id,
            "method": context.method_name,
            "metrics": context.metrics,
        },
    )

    # Get metrics handler
    if hasattr(instance, "_metrics_handler"):
        metrics_handler = instance._metrics_handler
        try:
            await metrics_handler._async_wave_operation(effect=effect, context={})
        except Exception as e:
            logger.error(f"Failed to record metrics via handler: {e}")
