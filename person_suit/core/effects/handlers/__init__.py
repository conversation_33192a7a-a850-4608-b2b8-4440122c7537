"""
Core Effect Handler Implementations
===================================

This module provides the core effect handler implementations and base classes
for the Person Suit effect system. Effect handlers are responsible for
interpreting and executing effects based on their type and operation.

Key Components:
- BaseEffectHandler: Canonical base class for all effect handlers
- EffectHandlerRegistry: Registry for managing handler instances
- Specialized Handlers: Core handlers for common effect types (IO, State, etc.)

Dependencies:
- Python 3.8+
- Effect system interfaces
"""

import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, Generic, List, Optional, TypeVar

# Import interfaces directly
from ..interfaces import (
    EffectHandlerInterface,
    EffectInterface,
    EffectTypeInterface,
)
# Do NOT import MonitoringContext here to avoid circular dependency
# from person_suit.core.effects.monitoring.decorators import MonitoringContext
# Import Capability placeholder or actual class if needed
# from person_suit.core.infrastructure.security.capabilities import Capability
Capability = Any # Placeholder

logger = logging.getLogger(__name__)

T = TypeVar("T")  # Effect result type

# --- Base Handler Classes (Moved from base.py) ---

class BaseEffectHandler(EffectHandlerInterface, ABC):
    """
    Canonical base effect handler implementation.

    All effect handler implementations should inherit from this class.
    """

    def __init__(
        self,
        supported_types: List[EffectTypeInterface],
        required_capabilities: Optional[List[Capability]] = None,
    ):
        """Initialize the handler with supported types and capabilities."""
        self._supported_types = supported_types
        self._capabilities = required_capabilities or []

    @property
    def supported_types(self) -> List[EffectTypeInterface]:
        """Get supported effect types."""
        return self._supported_types

    @property
    def capabilities(self) -> List[Capability]:
        """Get required capabilities."""
        return self._capabilities

    def can_handle(self, effect: EffectInterface[Any]) -> bool:
        """Check if this handler can handle the effect."""
        # Assuming effect.type exists and is comparable
        # Also assuming effect.type has a 'name' attribute for comparison if needed
        # This simplistic check might need refinement based on actual EffectInterface implementation
        if hasattr(effect, 'type') and effect.type:
            # Check if the effect's type object is in the list of supported types
            if effect.type in self._supported_types:
                return True
            # Fallback: Check if the effect type's name matches any supported type's name
            # This handles cases where effect types might be different instances but same logical type
            if hasattr(effect.type, 'name'):
                 effect_type_name = effect.type.name
                 for supported_type in self._supported_types:
                     if hasattr(supported_type, 'name') and supported_type.name == effect_type_name:
                         return True
        return False


    async def handle(self, effect: EffectInterface[T]) -> T:
        """
        Handle an effect, managing state transitions and errors.

        Args:
            effect: The effect to handle

        Returns:
            The effect result

        Raises:
            ValueError: If effect type not supported
            RuntimeError: If handling fails
        """
        if not self.can_handle(effect):
             effect_type_name = getattr(getattr(effect, 'type', None), 'name', 'UnknownType')
             raise ValueError(
                f"Handler {self.__class__.__name__} cannot handle "
                f"effect type {effect_type_name}"
            )

        try:
            logger.debug(f"Handling effect: {effect}")
            return await self._handle_effect(effect)
        except Exception as e:
            logger.error(f"Error handling effect: {e}", exc_info=True)
            raise RuntimeError(f"Failed to handle effect: {e}") from e

    @abstractmethod
    async def _handle_effect(self, effect: EffectInterface[T]) -> T:
        """
        Internal method to handle the effect.

        Must be implemented by subclasses.
        """
        pass


class ContextualEffectHandler(BaseEffectHandler, Generic[T]):
    """
    Handler that provides advanced context management and state tracking.

    Adds:
    - Context propagation
    - State transition management
    - Interference pattern tracking
    """

    async def _handle_effect(self, effect: EffectInterface[T]) -> T:
        """Handle effect with context awareness."""
        context = self._prepare_context(effect)

        # Assuming effect has an 'is_wave' attribute/property
        if getattr(effect, 'is_wave', False):
            result = await self._handle_wave_state(effect, context)
        else:
            result = await self._handle_particle_state(effect, context)

        return self._process_result(result, context)

    def _prepare_context(self, effect: EffectInterface[T]) -> Dict[str, Any]:
        """Prepare execution context."""
        # Ensure effect.type and effect.operation exist before accessing .name
        effect_type_name = getattr(getattr(effect, 'type', None), 'name', 'UnknownType')
        operation_name = getattr(effect, 'operation', 'UnknownOperation')
        is_wave = getattr(effect, 'is_wave', False)
        return {
            "handler": self.__class__.__name__,
            "effect_type": effect_type_name,
            "operation": operation_name,
            "wave_state": is_wave,
        }

    @abstractmethod
    async def _handle_wave_state(
        self, effect: EffectInterface[T], context: Dict[str, Any]
    ) -> T:
        """Handle effect in wave state."""
        pass

    @abstractmethod
    async def _handle_particle_state(
        self, effect: EffectInterface[T], context: Dict[str, Any]
    ) -> T:
        """Handle effect in particle state."""
        pass

    def _process_result(self, result: T, context: Dict[str, Any]) -> T:
        """Process and validate the result."""
        # Placeholder: Add validation or processing logic here
        return result


class AsyncEffectHandler(ContextualEffectHandler[T]):
    """
    Handler optimized for async operations with proper cancellation.

    Adds:
    - Proper async resource management
    - Cancellation support
    - Timeout handling
    """

    async def _handle_wave_state(
        self, effect: EffectInterface[T], context: Dict[str, Any]
    ) -> T:
        """Handle wave state asynchronously."""
        try:
            return await self._async_wave_operation(effect, context)
        finally:
            await self._cleanup_resources()

    async def _handle_particle_state(
        self, effect: EffectInterface[T], context: Dict[str, Any]
    ) -> T:
        """Handle particle state asynchronously."""
        try:
            return await self._async_particle_operation(effect, context)
        finally:
            await self._cleanup_resources()

    @abstractmethod
    async def _async_wave_operation(
        self, effect: EffectInterface[T], context: Dict[str, Any]
    ) -> T:
        """Perform async wave state operation."""
        pass

    @abstractmethod
    async def _async_particle_operation(
        self, effect: EffectInterface[T], context: Dict[str, Any]
    ) -> T:
        """Perform async particle state operation."""
        pass

    async def _cleanup_resources(self) -> None:
        """Clean up any async resources."""
        # Placeholder: Subclasses should implement resource cleanup
        pass

    # Removed _record_monitoring method to break circular dependency

# --- End Base Handler Classes ---

# Import other handlers (adjust paths as needed)
# This section should import concrete handlers if they are defined elsewhere
# within this handlers package or its submodules, like core/ or advanced/.
# Example (assuming they exist and are needed for the public API):
# from .core import StateHandler, IOHandler
# from .advanced import DifferentialHandler, ProbabilisticHandler

# Define public API for the handlers package
__all__ = [
    "BaseEffectHandler",
    "ContextualEffectHandler",
    "AsyncEffectHandler",
    # Add other handlers as they are defined/imported for re-export
    # "StateHandler",
    # "IOHandler",
    # "DifferentialHandler",
    # "ProbabilisticHandler",
]
