"""
Information Flow Effect Handler
===========================

This module defines the handler for Information Flow effects in the Person Suit effect system.
Information Flow effects include operations like tracking and logging information flow
between components in the system, providing detailed visibility into component interactions.

Related Files:
- person_suit/core/effects/core.py: Core effect system components
- person_suit/core/effects/interfaces.py: Effect system interfaces
- person_suit/core/effects/registry.py: Effect handler registry

Dependencies:
- typing>=4.0.0: For type annotations
- uuid: For generating unique IDs
- time: For timestamps
- logging: For logging information flows
"""

import logging
from typing import Any, Dict, List, Optional, Set

from .base import BaseEffectHandler
from ..interfaces import (
    EffectTypeInterface,
)
from ..types import InformationFlow

# Configure logger
logger = logging.getLogger(__name__)


class InformationFlowEffectHandler(BaseEffectHandler):
    """
    Handler for information flow operations.

    This handler integrates with the CAW paradigm by:
    1. Supporting wave-particle duality in information flow
    2. Being context-aware in all operations
    3. Using wave functions for flow state representation
    4. Enabling adaptive computation based on context
    """

    def __init__(self):
        """Initialize the information flow effect handler."""
        super().__init__("information_flow_handler")
        self._flows: Dict[str, Dict[str, Any]] = {}
        self._sources: Set[str] = set()
        self._sinks: Set[str] = set()
        self._transformations: Dict[str, Dict[str, Any]] = {}

    def can_handle(self, effect_type: EffectTypeInterface) -> bool:
        """Check if this handler can handle the given effect type."""
        return effect_type == InformationFlow

    def can_handle_operation(
        self, effect_type: EffectTypeInterface, operation: str
    ) -> bool:
        """Check if this handler can handle the given operation."""
        return self.can_handle(
            effect_type
        ) and operation in self.get_supported_operations(effect_type)

    def get_supported_operations(self, effect_type: EffectTypeInterface) -> List[str]:
        """Get the operations supported by this handler."""
        if self.can_handle(effect_type):
            return [
                "register_source",
                "register_sink",
                "create_flow",
                "transform",
                "analyze",
            ]
        return []

    def handle(
        self,
        effect_type: EffectTypeInterface,
        operation: str,
        context: Optional[Dict[str, Any]] = None,
        **kwargs: Any,
    ) -> Any:
        """Handle an information flow operation."""
        if not self.can_handle_operation(effect_type, operation):
            raise ValueError(
                f"Cannot handle operation {operation} for effect type {effect_type}"
            )

        # Get operation parameters
        if operation == "register_source":
            return self._handle_register_source(**kwargs)
        elif operation == "register_sink":
            return self._handle_register_sink(**kwargs)
        elif operation == "create_flow":
            return self._handle_create_flow(**kwargs)
        elif operation == "transform":
            return self._handle_transform(**kwargs)
        elif operation == "analyze":
            return self._handle_analyze(**kwargs)
        else:
            raise ValueError(f"Unsupported operation: {operation}")

    def _handle_register_source(
        self,
        source_id: str,
        source_type: str,
        attributes: Optional[Dict[str, Any]] = None,
        **kwargs: Any,
    ) -> Any:
        """Handle registering an information source."""
        logger.debug(f"Registering source: {source_id} of type: {source_type}")

        self._sources.add(source_id)

        return {
            "status": "success",
            "operation": "register_source",
            "source_id": source_id,
            "source_type": source_type,
        }

    def _handle_register_sink(
        self,
        sink_id: str,
        sink_type: str,
        attributes: Optional[Dict[str, Any]] = None,
        **kwargs: Any,
    ) -> Any:
        """Handle registering an information sink."""
        logger.debug(f"Registering sink: {sink_id} of type: {sink_type}")

        self._sinks.add(sink_id)

        return {
            "status": "success",
            "operation": "register_sink",
            "sink_id": sink_id,
            "sink_type": sink_type,
        }

    def _handle_create_flow(
        self,
        flow_id: str,
        source_id: str,
        sink_id: str,
        flow_type: str,
        attributes: Optional[Dict[str, Any]] = None,
        **kwargs: Any,
    ) -> Any:
        """Handle creating an information flow."""
        logger.debug(f"Creating flow: {flow_id} from {source_id} to {sink_id}")

        if source_id not in self._sources:
            return {
                "status": "error",
                "operation": "create_flow",
                "error": "Source not found",
                "source_id": source_id,
            }

        if sink_id not in self._sinks:
            return {
                "status": "error",
                "operation": "create_flow",
                "error": "Sink not found",
                "sink_id": sink_id,
            }

        self._flows[flow_id] = {
            "source_id": source_id,
            "sink_id": sink_id,
            "flow_type": flow_type,
            "attributes": attributes or {},
            "status": "active",
        }

        return {"status": "success", "operation": "create_flow", "flow_id": flow_id}

    def _handle_transform(
        self,
        flow_id: str,
        transformation_id: str,
        transformation_type: str,
        parameters: Optional[Dict[str, Any]] = None,
        **kwargs: Any,
    ) -> Any:
        """Handle adding a transformation to an information flow."""
        logger.debug(f"Adding transformation: {transformation_id} to flow: {flow_id}")

        if flow_id not in self._flows:
            return {
                "status": "error",
                "operation": "transform",
                "error": "Flow not found",
                "flow_id": flow_id,
            }

        self._transformations[transformation_id] = {
            "flow_id": flow_id,
            "transformation_type": transformation_type,
            "parameters": parameters or {},
            "status": "active",
        }

        return {
            "status": "success",
            "operation": "transform",
            "flow_id": flow_id,
            "transformation_id": transformation_id,
        }

    def _handle_analyze(
        self,
        flow_id: Optional[str] = None,
        analysis_type: str = "flow_graph",
        parameters: Optional[Dict[str, Any]] = None,
        **kwargs: Any,
    ) -> Any:
        """Handle analyzing information flows."""
        logger.debug(f"Analyzing flows with type: {analysis_type}")

        if flow_id and flow_id not in self._flows:
            return {
                "status": "error",
                "operation": "analyze",
                "error": "Flow not found",
                "flow_id": flow_id,
            }

        analysis = {
            "sources": len(self._sources),
            "sinks": len(self._sinks),
            "flows": len(self._flows),
            "transformations": len(self._transformations),
        }

        if flow_id:
            analysis["flow_details"] = self._flows[flow_id]

        return {
            "status": "success",
            "operation": "analyze",
            "analysis_type": analysis_type,
            "results": analysis,
        }
