"""
Person Suit - Canonical Base Effect Handler

This is the canonical base class for all effect handlers in the Person Suit effect system.
All effect handler implementations should inherit from this class.

Related Files:
- person_suit/core/effects/interfaces/handler.py: Effect handler interface
- person_suit/core/effects/registry.py: Effect handler registry

Dependencies:
- typing>=4.0.0: For type annotations
- abc: For abstract base classes
"""

import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, Generic, List, Optional, TypeVar

# Removed imports for interfaces, BaseEffectHandler is defined in __init__.py now
# from person_suit.core.effects.interfaces import (
#     EffectHandlerInterface,
#     EffectInterface,
#     EffectTypeInterface,
# )
# Removed import causing circular dependency
# from person_suit.core.effects.monitoring.decorators import MonitoringContext
# from person_suit.core.infrastructure.security.capabilities import Capability

logger = logging.getLogger(__name__)

T = TypeVar("T")  # Effect result type


# The BaseEffectHandler, ContextualEffectHandler, and AsyncEffectHandler class definitions 
# have been moved to person_suit/core/effects/handlers/__init__.py 
# to resolve the circular import issue.

# This file might now be empty or contain other base utilities if needed.
# For now, leaving it mostly empty after moving the classes.
