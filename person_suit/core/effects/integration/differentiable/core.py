"""
Differential Effect Handler
========================

This module provides a handler for differentiable effects that integrates with
the CAW paradigm's wave-particle duality and context-aware processing.
"""

from ...core import EffectResult
from ...handlers.base import BaseEffectHandler
from ....infrastructure.wave.core import Context


class DifferentialEffectHandler(BaseEffectHandler):
    """
    Handler for differentiable effects.

    This handler integrates with the CAW paradigm by:
    1. Supporting wave-particle duality through differentiable computations
    2. Being context-aware in all operations
    3. Using wave functions for gradient propagation
    4. Enabling adaptive computation based on context
    """

    def __init__(self):
        """Initialize the differential effect handler."""
        super().__init__("differential_handler")
        self.caw_processor = CAWProcessor()

    def can_handle(self, effect_type: EffectTypeInterface) -> bool:
        """Check if this handler can handle the given effect type."""
        return isinstance(effect_type, DifferentialEffectType)

    def can_handle_operation(
        self, effect_type: EffectTypeInterface, operation: str
    ) -> bool:
        """Check if this handler can handle the given operation."""
        return operation in ["forward", "backward", "gradient"]

    def get_supported_operations(self, effect_type: EffectTypeInterface) -> List[str]:
        """Get the operations supported by this handler."""
        return ["forward", "backward", "gradient"]

    def handle(
        self,
        effect_type: EffectTypeInterface,
        operation: str,
        context: Optional[Dict[str, Any]] = None,
        **kwargs: Any,
    ) -> Any:
        """Handle a differential effect."""
        if not isinstance(effect_type, DifferentialEffectType):
            raise ValueError(
                f"Expected DifferentialEffectType, got {type(effect_type)}"
            )

        effect = kwargs.get("effect")
        if not isinstance(effect, DifferentialEffect):
            raise ValueError(f"Expected DifferentialEffect, got {type(effect)}")

        if operation == "forward":
            return self._handle_forward(effect, context)
        elif operation == "backward":
            return self._handle_backward(effect, context)
        elif operation == "gradient":
            return self._handle_gradient(effect, context)
        else:
            raise ValueError(f"Unsupported operation: {operation}")

    def _handle_forward(
        self, effect: DifferentialEffect, context: Optional[Context] = None
    ) -> EffectResult:
        """Handle a forward pass effect."""
        # Implementation unchanged...

    def _handle_backward(
        self, effect: DifferentialEffect, context: Optional[Context] = None
    ) -> EffectResult:
        """Handle a backward pass effect."""
        # Implementation unchanged...

    def _handle_gradient(
        self, effect: DifferentialEffect, context: Optional[Context] = None
    ) -> EffectResult:
        """Handle a gradient computation effect."""
        # Implementation unchanged...
