"""
CAW Effect Tracker
===============

This module implements a CAW-aware effect tracker that uses wave functions
for tracking and analyzing effects in different contexts.

Related Files:
- ../../core.py: Core effect types and classes
- ../../context_effects.py: Context-sensitive effect tracking
- ./wave_effect.py: Wave-based effects
- ../../../wave/core.py: Wave-based information representation
- ../../../caw/core.py: CAW components

Dependencies:
- Python 3.8+
- NumPy
"""

import logging
from typing import (
    Any,
    Dict,
    List,
    Optional,
    TypeVar,
    Union,
)

import numpy as np

from ...context_effects import (
    EffectAnalysisResult,
    EffectAnalysisType,
)
from ...core import EffectType
from ...handlers.base import BaseEffectHandler
from .wave_effect import (
    WaveEffect,
    get_effect_wave_function,
)
from ....infrastructure.caw.core import CAWProcessor
from ....infrastructure.wave.core import (
    Context,
    Information,
    WaveFunction,
)

# Configure logging
logger = logging.getLogger(__name__)

# Type variables
T = TypeVar("T")
E = TypeVar("E")


class CAWEffectTracker(BaseEffectHandler):
    """
    CAW-aware effect tracker.

    This class extends BaseEffectHandler with CAW-specific functionality,
    enabling context-sensitive effect tracking and analysis using wave
    functions and interference patterns.

    The handler follows CAW principles by:
    1. Maintaining wave-particle duality through WaveEffect tracking
    2. Being context-aware in all operations
    3. Supporting interference pattern analysis
    4. Enabling adaptive computation based on context
    """

    def __init__(self):
        """Initialize the CAW effect tracker."""
        super().__init__("caw_effect_tracker")
        self.caw_processor = CAWProcessor()
        self._effects: List[WaveEffect] = []
        self._context_effects: Dict[str, List[WaveEffect]] = {}
        self._effect_interference_patterns: Dict[str, Dict[str, float]] = {}

    def can_handle(self, effect_type: EffectTypeInterface) -> bool:
        """Check if this handler can handle the given effect type."""
        # CAW tracker can handle any effect type that can be represented as a wave
        return True

    def can_handle_operation(
        self, effect_type: EffectTypeInterface, operation: str
    ) -> bool:
        """Check if this handler can handle the given operation."""
        return operation in [
            "track_wave_effect",
            "analyze_wave_effects",
            "get_interference_pattern",
            "clear_effects",
        ]

    def get_supported_operations(self, effect_type: EffectTypeInterface) -> List[str]:
        """Get the operations supported by this handler."""
        return [
            "track_wave_effect",
            "analyze_wave_effects",
            "get_interference_pattern",
            "clear_effects",
        ]

    def handle(
        self,
        effect_type: EffectTypeInterface,
        operation: str,
        context: Optional[Dict[str, Any]] = None,
        **kwargs: Any,
    ) -> Any:
        """Handle a CAW effect tracking operation."""
        if operation == "track_wave_effect":
            return self.track_wave_effect(**kwargs)
        elif operation == "analyze_wave_effects":
            return self.analyze_wave_effects(**kwargs)
        elif operation == "get_interference_pattern":
            return self._get_interference_pattern(**kwargs)
        elif operation == "clear_effects":
            return self.clear_effects()
        else:
            raise ValueError(f"Unsupported operation: {operation}")

    def track_wave_effect(
        self,
        effect_type: EffectType,
        source: str,
        context: Context,
        wave_function: Optional[Union[str, WaveFunction]] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> WaveEffect:
        """
        Track a wave effect in a specific context.

        Args:
            effect_type: The type of effect
            source: The source of the effect
            context: The context of the effect
            wave_function: Optional wave function name or instance
            metadata: Additional metadata for the effect

        Returns:
            The tracked wave effect
        """
        # Resolve wave function
        resolved_wave_function = None
        if wave_function is not None:
            if isinstance(wave_function, str):
                resolved_wave_function = get_effect_wave_function(wave_function)
            else:
                resolved_wave_function = wave_function

        # Create wave effect
        effect = WaveEffect(
            effect_type=effect_type,
            operation=f"track_effect_{source}",
            wave_function=resolved_wave_function,
            metadata=metadata,
        )

        # Track the effect
        self._effects.append(effect)

        # Track the effect by context
        context_key = context.domain
        if context_key not in self._context_effects:
            self._context_effects[context_key] = []

        self._context_effects[context_key].append(effect)

        # Create information object for effect tracking
        effect_info = effect.as_information(context)

        # Interpret effect in the context
        effect_interp = effect_info.interpret(context)
        logger.debug(
            f"Tracked wave effect {effect_type.name} in context {context.domain} with probability {effect_interp.probability:.2f}"
        )

        return effect

    def analyze_wave_effects(
        self,
        analysis_type: EffectAnalysisType,
        context: Context,
        effect_types: Optional[List[EffectType]] = None,
    ) -> EffectAnalysisResult:
        """
        Analyze wave effects in a specific context.

        Args:
            analysis_type: The type of analysis to perform
            context: The context in which to analyze effects
            effect_types: Optional list of effect types to analyze

        Returns:
            The analysis result
        """
        # Get effects in the context
        effects = self.get_effects_by_context(context)

        # Filter by effect types if specified
        if effect_types:
            effects = [e for e in effects if e.effect_type in effect_types]

        # Convert to wave effects if needed
        wave_effects = []
        for effect in effects:
            if isinstance(effect, WaveEffect):
                wave_effects.append(effect)
            else:
                # Create a wave effect from a regular effect
                wave_effect = WaveEffect(
                    effect_type=effect.effect_type,
                    operation=effect.operation,
                    wave_function=get_effect_wave_function("default"),
                    metadata=effect.metadata,
                )
                wave_effects.append(wave_effect)

        # Perform the analysis
        if analysis_type == EffectAnalysisType.FLOW:
            return self._analyze_effect_flow(wave_effects, context)
        elif analysis_type == EffectAnalysisType.DEPENDENCY:
            return self._analyze_effect_dependencies(wave_effects, context)
        elif analysis_type == EffectAnalysisType.INTERFERENCE:
            return self._analyze_effect_interference(wave_effects, context)
        elif analysis_type == EffectAnalysisType.COMPOSITION:
            return self._analyze_effect_composition(wave_effects, context)
        else:
            raise ValueError(f"Unsupported analysis type: {analysis_type}")

    def _analyze_effect_interference(
        self, effects: List[WaveEffect], context: Context
    ) -> EffectAnalysisResult:
        """
        Analyze interference patterns between effects.

        Args:
            effects: The effects to analyze
            context: The context in which to analyze effects

        Returns:
            The analysis result
        """
        # Initialize interference matrix
        n = len(effects)
        interference_matrix = np.zeros((n, n), dtype=np.complex128)

        # Compute interference between all pairs of effects
        for i in range(n):
            for j in range(n):
                if i == j:
                    # Self-interference is always 1
                    interference_matrix[i, j] = 1.0
                else:
                    # Compute interference between effects
                    effect1 = effects[i]
                    effect2 = effects[j]

                    # Convert to information objects
                    info1 = effect1.as_information(context)
                    info2 = effect2.as_information(context)

                    # Compute interference
                    interference = self._compute_interference(info1, info2, context)
                    interference_matrix[i, j] = interference

        # Store interference patterns
        context_key = context.domain
        if context_key not in self._effect_interference_patterns:
            self._effect_interference_patterns[context_key] = {}

        for i in range(n):
            for j in range(n):
                if i != j:
                    effect1 = effects[i]
                    effect2 = effects[j]
                    key = f"{effect1.effect_type.name}_{effect1.operation}_{effect2.effect_type.name}_{effect2.operation}"
                    self._effect_interference_patterns[context_key][key] = (
                        interference_matrix[i, j]
                    )

        # Create analysis result
        return EffectAnalysisResult(
            analysis_type=EffectAnalysisType.INTERFERENCE,
            context=context,
            data={
                "interference_matrix": interference_matrix,
                "effect_types": [e.effect_type.name for e in effects],
                "operations": [e.operation for e in effects],
            },
        )

    def _compute_interference(
        self, info1: Information, info2: Information, context: Context
    ) -> complex:
        """
        Compute interference between two information objects.

        Args:
            info1: The first information object
            info2: The second information object
            context: The context in which to compute interference

        Returns:
            The interference value
        """
        # Interpret both information objects in the context
        interp1 = info1.interpret(context)
        interp2 = info2.interpret(context)

        # Compute interference
        interference = interp1.wave_value * np.conjugate(interp2.wave_value)

        return interference

    def _get_interference_pattern(
        self, effect1: WaveEffect, effect2: WaveEffect, context: Context
    ) -> float:
        """Get the interference pattern between two effects."""
        # Implementation unchanged...

    def clear_effects(self) -> None:
        """Clear all tracked effects."""
        self._effects.clear()
        self._context_effects.clear()
        self._effect_interference_patterns.clear()


# Global CAW effect tracker
_caw_effect_tracker = CAWEffectTracker()


def get_caw_effect_tracker() -> CAWEffectTracker:
    """
    Get the global CAW effect tracker.

    Returns:
        The global CAW effect tracker
    """
    return _caw_effect_tracker
