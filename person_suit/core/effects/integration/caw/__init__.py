"""
Integration with CAW
=================

This package implements integration between the Effect Systems framework
and the Contextual Adaptive Wave (CAW) paradigm. It enables context-sensitive
effect tracking and handling using wave functions and interference patterns.

Related Files:
- ../../core.py: Core effect types and classes
- ../../context_effects.py: Context-sensitive effect tracking
- ../../../wave/core.py: Wave-based information representation
- ../../../caw/core.py: CAW components

Dependencies:
- Python 3.8+
- NumPy
"""

from ....infrastructure.effects.integration.caw.caw_effect_tracker import (
    CAWEffectTracker,
    get_caw_effect_tracker,
)
from ....infrastructure.effects.integration.caw.decorators import (
    wave_effects,
    with_wave_effects,
)
from ....infrastructure.effects.integration.caw.operations import (
    analyze_effects_in_context,
    track_effect_in_context,
    visualize_effects_in_context,
)
from ....infrastructure.effects.integration.caw.wave_effect import (
    WaveEffect,
    create_wave_effect,
    get_effect_wave_function,
    register_effect_wave_function,
)

__all__ = [
    "WaveEffect",
    "create_wave_effect",
    "register_effect_wave_function",
    "get_effect_wave_function",
    "CAWEffectTracker",
    "get_caw_effect_tracker",
    "wave_effects",
    "with_wave_effects",
    "track_effect_in_context",
    "analyze_effects_in_context",
    "visualize_effects_in_context",
]
