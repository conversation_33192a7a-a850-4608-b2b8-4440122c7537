"""
Wave-Based Effects
===============

This module implements wave-based effects for the CAW paradigm, enabling
context-sensitive effect tracking and handling using wave functions.

Related Files:
- ../../core.py: Core effect types and classes
- ../../context_effects.py: Context-sensitive effect tracking
- ../../../wave/core.py: Wave-based information representation
- ../../../caw/core.py: CAW components

Dependencies:
- Python 3.8+
- NumPy
"""

import logging
import math
from typing import (
    Any,
    Callable,
    Dict,
    Optional,
    TypeVar,
    Union,
)

import numpy as np

from ....infrastructure.dual_wave import (
    DualContext,
    DualInformation,
    DualInterpretation,
    DualWaveFunction,
)

# Import adapters for backward compatibility
from ....infrastructure.effects.core import (
    Effect,
)

# Configure logging
logger = logging.getLogger(__name__)

# Type variables
T = TypeVar("T")
E = TypeVar("E")

# Registry of effect wave functions
_effect_wave_functions: Dict[str, DualWaveFunction] = {}


class WaveEffect(Effect[E, T]):
    """
    Effect with wave function support.

    This class extends the standard Effect class with wave function support,
    enabling context-sensitive effect tracking and handling.
    """

    def __init__(
        self,
        effect_type: E,
        operation: str,
        wave_function: Optional[DualWaveFunction] = None,
        computation: Optional[Callable[..., T]] = None,
        *args: Any,
        **kwargs: Any,
    ):
        """
        Initialize a wave effect.

        Args:
            effect_type: The type of effect
            operation: The operation to perform
            wave_function: Optional wave function for the effect
            computation: Optional function to compute the result
            *args: Positional arguments for the computation
            **kwargs: Keyword arguments for the computation
        """
        super().__init__(
            effect_type=effect_type,
            operation=operation,
            computation=computation,
            *args,
            **kwargs,
        )
        self.wave_function = wave_function

    def as_information(self, context: DualContext) -> DualInformation:
        """
        Convert the effect to an information object.

        Args:
            context: The context in which to interpret the effect

        Returns:
            An information object representing the effect
        """
        if self.wave_function is None:
            # Use a default wave function if none is provided
            self.wave_function = get_effect_wave_function("default")

        return DualInformation(content=self, wave_function=self.wave_function)

    def interpret_in_context(self, context: DualContext) -> DualInterpretation:
        """
        Interpret the effect in a specific context.

        Args:
            context: The context in which to interpret the effect

        Returns:
            The interpreted effect
        """
        # Convert to information
        info = self.as_information(context)

        # Interpret in the context
        return info.interpret(context)

    def as_dual_information(self) -> DualInformation:
        """
        Convert the effect to a dual information object.

        Returns:
            A dual information object representing the effect
        """
        if self.wave_function is None:
            # Use a default wave function if none is provided
            self.wave_function = get_effect_wave_function("default")

        return DualInformation(content=self, wave_function=self.wave_function)


def create_wave_effect(
    effect_type: E,
    operation: str,
    wave_function: Optional[Union[str, DualWaveFunction]] = None,
    computation: Optional[Callable[..., T]] = None,
    *args: Any,
    **kwargs: Any,
) -> WaveEffect[E, T]:
    """
    Create a wave effect.

    Args:
        effect_type: The type of effect
        operation: The operation to perform
        wave_function: Optional wave function name or instance
        computation: Optional function to compute the result
        *args: Positional arguments for the computation
        **kwargs: Keyword arguments for the computation

    Returns:
        A wave effect
    """
    # Resolve wave function
    resolved_wave_function = None
    if wave_function is not None:
        if isinstance(wave_function, str):
            resolved_wave_function = get_effect_wave_function(wave_function)
        else:
            resolved_wave_function = wave_function

    # Create wave effect
    return WaveEffect(
        effect_type=effect_type,
        operation=operation,
        wave_function=resolved_wave_function,
        computation=computation,
        *args,
        **kwargs,
    )


def register_effect_wave_function(name: str, wave_function: DualWaveFunction) -> None:
    """
    Register an effect wave function.

    Args:
        name: The name of the wave function
        wave_function: The wave function to register
    """
    _effect_wave_functions[name] = wave_function
    logger.debug(f"Registered effect wave function: {name}")


def get_effect_wave_function(name: str = "default") -> DualWaveFunction:
    """
    Get an effect wave function by name.

    Args:
        name: The name of the wave function

    Returns:
        The wave function

    Raises:
        ValueError: If the wave function is not found
    """
    # Check if the wave function exists
    if name not in _effect_wave_functions:
        if name == "default":
            # Create a default wave function
            default_wave = DualWaveFunction(
                vector=np.array([1.0, 0.0, 0.0]),  # Default vector
                amplitude=1.0,  # Default amplitude
                phase=0.0,  # Default phase
                position=np.array([1.0, 0.0, 0.0]),  # Default position
                wave_particle_ratio=0.5,  # Balanced wave-particle ratio
            )

            # Register the default wave function
            register_effect_wave_function("default", default_wave)

            return default_wave
        else:
            raise ValueError(f"Effect wave function not found: {name}")

    return _effect_wave_functions[name]


# Initialize standard wave functions
def _initialize_standard_wave_functions():
    """Initialize standard effect wave functions."""
    # IO effect wave function
    io_wave = DualWaveFunction(
        vector=np.array([0.8, 0.1, 0.1]),  # IO-focused vector
        amplitude=0.9,  # High amplitude for IO operations
        phase=0.0,  # No phase shift
        position=np.array([0.8, 0.1, 0.1]),  # Position matches vector
        momentum=np.array([0.1, 0.0, 0.0]),  # Small momentum in x direction
        wave_particle_ratio=0.6,  # Slightly more wave-like
    )

    # Database effect wave function
    db_wave = DualWaveFunction(
        vector=np.array([0.1, 0.8, 0.1]),  # Database-focused vector
        amplitude=0.9,  # High amplitude for database operations
        phase=math.pi / 4,  # 45-degree phase shift
        position=np.array([0.1, 0.8, 0.1]),  # Position matches vector
        momentum=np.array([0.0, 0.1, 0.0]),  # Small momentum in y direction
        wave_particle_ratio=0.7,  # More wave-like for data
    )

    # Computation effect wave function
    comp_wave = DualWaveFunction(
        vector=np.array([0.1, 0.1, 0.8]),  # Computation-focused vector
        amplitude=0.9,  # High amplitude for computation operations
        phase=math.pi / 2,  # 90-degree phase shift
        position=np.array([0.1, 0.1, 0.8]),  # Position matches vector
        momentum=np.array([0.0, 0.0, 0.1]),  # Small momentum in z direction
        wave_particle_ratio=0.4,  # More particle-like for computation
    )

    # Security effect wave function
    sec_wave = DualWaveFunction(
        vector=np.array([0.6, 0.2, 0.2]),  # Security-focused vector
        amplitude=0.9,  # High amplitude for security operations
        phase=3 * math.pi / 4,  # 135-degree phase shift
        position=np.array([0.6, 0.2, 0.2]),  # Position matches vector
        momentum=np.array([0.05, 0.05, 0.05]),  # Small momentum in all directions
        wave_particle_ratio=0.3,  # More particle-like for security
    )

    # Register standard wave functions
    register_effect_wave_function("io", io_wave)
    register_effect_wave_function("database", db_wave)
    register_effect_wave_function("computation", comp_wave)
    register_effect_wave_function("security", sec_wave)


# Initialize standard wave functions
_initialize_standard_wave_functions()
