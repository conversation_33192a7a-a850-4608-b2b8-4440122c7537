"""
Operations for CAW-Effect Integration
=================================

This module implements operations for integrating CAW with the Effect Systems
framework, enabling context-sensitive effect tracking and handling.

Related Files:
- ../../core.py: Core effect types and classes
- ../../context_effects.py: Context-sensitive effect tracking
- ./wave_effect.py: Wave-based effects
- ./caw_effect_tracker.py: CAW effect tracker
- ../../../wave/core.py: Wave-based information representation
- ../../../caw/core.py: CAW components

Dependencies:
- Python 3.8+
- NumPy
"""

import logging
import os
import time
from typing import (
    Any,
    Dict,
    List,
    Optional,
    TypeVar,
    Union,
)

import numpy as np

from ....infrastructure.effects.context_effects import (
    ContextualEffect,
    EffectAnalysisResult,
    EffectAnalysisType,
)
from ....infrastructure.effects.core import (
    Effect,
    EffectType,
)
from ....infrastructure.effects.integration.caw.caw_effect_tracker import (
    get_caw_effect_tracker,
)
from ....infrastructure.effects.integration.caw.wave_effect import (
    WaveEffect,
)
from ....infrastructure.wave.core import Context, WaveFunction

# Configure logging
logger = logging.getLogger(__name__)

# Type variables
T = TypeVar("T")


def track_effect_in_context(
    effect_type: EffectType,
    source: str,
    context: Context,
    wave_function: Optional[Union[str, WaveFunction]] = None,
    metadata: Optional[Dict[str, Any]] = None,
) -> WaveEffect:
    """
    Track an effect in a specific context.

    Args:
        effect_type: The type of effect
        source: The source of the effect
        context: The context in which to track the effect
        wave_function: Optional wave function name or instance
        metadata: Additional metadata for the effect

    Returns:
        The tracked effect
    """
    # Get the CAW effect tracker
    tracker = get_caw_effect_tracker()

    # Track the effect
    return tracker.track_wave_effect(
        effect_type=effect_type,
        source=source,
        context=context,
        wave_function=wave_function,
        metadata=metadata,
    )


def analyze_effects_in_context(
    analysis_type: EffectAnalysisType,
    context: Context,
    effect_types: Optional[List[EffectType]] = None,
) -> EffectAnalysisResult:
    """
    Analyze effects in a specific context.

    Args:
        analysis_type: The type of analysis to perform
        context: The context in which to analyze effects
        effect_types: Optional list of effect types to analyze

    Returns:
        The analysis result
    """
    # Get the CAW effect tracker
    tracker = get_caw_effect_tracker()

    # Analyze the effects
    return tracker.analyze_wave_effects(
        analysis_type=analysis_type, context=context, effect_types=effect_types
    )


def visualize_effects_in_context(
    context: Context,
    effect_types: Optional[List[EffectType]] = None,
    output_dir: str = "effect_visualizations",
    format: str = "html",
) -> str:
    """
    Visualize effects in a specific context.

    Args:
        context: The context in which to visualize effects
        effect_types: Optional list of effect types to visualize
        output_dir: The directory in which to save the visualization
        format: The format of the visualization (html, png, svg)

    Returns:
        The path to the visualization file
    """
    # Get the CAW effect tracker
    tracker = get_caw_effect_tracker()

    # Get effects in the context
    effects = tracker.get_effects_by_context(context)

    # Filter by effect types if specified
    if effect_types:
        effects = [e for e in effects if e.effect_type in effect_types]

    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Generate visualization
    timestamp = int(time.time())
    output_path = os.path.join(
        output_dir, f"effects_{context.domain}_{timestamp}.{format}"
    )

    # Generate HTML visualization
    if format == "html":
        html = _generate_html_visualization(effects, context)
        with open(output_path, "w") as f:
            f.write(html)
    else:
        # For other formats, generate a simple text file
        with open(output_path, "w") as f:
            f.write(f"Effects in context {context.domain}:\n\n")
            for effect in effects:
                f.write(f"- {effect.effect_type.name}: {effect.operation}\n")

    logger.info(f"Generated effect visualization: {output_path}")
    return output_path


def _generate_html_visualization(
    effects: List[Union[Effect, WaveEffect, ContextualEffect]], context: Context
) -> str:
    """
    Generate HTML visualization of effects.

    Args:
        effects: The effects to visualize
        context: The context in which to visualize effects

    Returns:
        The HTML visualization
    """
    # Generate HTML
    html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Effects in Context {context.domain}</title>
        <style>
            body {{
                font-family: Arial, sans-serif;
                margin: 20px;
            }}
            h1 {{
                color: #333;
            }}
            .effect {{
                margin: 10px 0;
                padding: 10px;
                border: 1px solid #ddd;
                border-radius: 5px;
            }}
            .effect-type {{
                font-weight: bold;
                color: #fff;
                padding: 3px 6px;
                border-radius: 3px;
                margin-right: 10px;
            }}
            .effect-IO {{
                background-color: #4CAF50;
            }}
            .effect-DATABASE {{
                background-color: #2196F3;
            }}
            .effect-NETWORK {{
                background-color: #9C27B0;
            }}
            .effect-COMPUTATION {{
                background-color: #FF9800;
            }}
            .effect-SECURITY {{
                background-color: #F44336;
            }}
            .effect-MEMORY {{
                background-color: #607D8B;
            }}
            .effect-TELEMETRY {{
                background-color: #795548;
            }}
            .effect-INFORMATION_FLOW {{
                background-color: #009688;
            }}
            .effect-CONTEXT_SWITCH {{
                background-color: #673AB7;
            }}
            .effect-MESSAGING {{
                background-color: #3F51B5;
            }}
            .effect-STATE_READ {{
                background-color: #8BC34A;
            }}
            .effect-STATE_CHANGE {{
                background-color: #CDDC39;
            }}
            .effect-ACTOR_CREATION {{
                background-color: #FFC107;
            }}
            .effect-CUSTOM {{
                background-color: #9E9E9E;
            }}
            .effect-details {{
                margin-top: 5px;
                color: #666;
            }}
            .wave-info {{
                margin-top: 10px;
                padding: 5px;
                background-color: #f5f5f5;
                border-radius: 3px;
            }}
        </style>
    </head>
    <body>
        <h1>Effects in Context {context.domain}</h1>
        <div class="context-info">
            <p><strong>Domain:</strong> {context.domain}</p>
            <p><strong>Priority:</strong> {context.priority}</p>
            <p><strong>Constraints:</strong> {", ".join(context.constraints) if context.constraints else "None"}</p>
        </div>
        <h2>Effects ({len(effects)})</h2>
        <div class="effects">
    """

    # Add effects
    for effect in effects:
        effect_type_name = (
            effect.effect_type.name
            if hasattr(effect.effect_type, "name")
            else str(effect.effect_type)
        )

        html += f"""
        <div class="effect">
            <span class="effect-type effect-{effect_type_name}">{effect_type_name}</span>
            <span class="effect-operation">{effect.operation}</span>
            <div class="effect-details">
                <p><strong>Source:</strong> {effect.metadata.get("source", "Unknown")}</p>
                <p><strong>Timestamp:</strong> {time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(effect.created_at))}</p>
            </div>
        """

        # Add wave information if available
        if isinstance(effect, WaveEffect) and effect.wave_function is not None:
            # Create information object
            info = effect.as_information(context)

            # Interpret in the context
            interp = info.interpret(context)

            html += f"""
            <div class="wave-info">
                <p><strong>Probability:</strong> {interp.probability:.2f}</p>
                <p><strong>Phase:</strong> {np.angle(interp.wave_value):.2f} rad</p>
            </div>
            """

        html += "</div>"

    html += """
        </div>
    </body>
    </html>
    """

    return html
