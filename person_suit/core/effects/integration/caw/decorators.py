"""
Decorators for CAW-Effect Integration
=================================

This module implements decorators for integrating CAW with the Effect Systems
framework, enabling context-sensitive effect tracking and handling.

Related Files:
- ../../core.py: Core effect types and classes
- ../../context_effects.py: Context-sensitive effect tracking
- ./wave_effect.py: Wave-based effects
- ./caw_effect_tracker.py: CAW effect tracker
- ../../../wave/core.py: Wave-based information representation
- ../../../caw/core.py: CAW components

Dependencies:
- Python 3.8+
- NumPy
"""

import functools
import logging
from typing import (
    Any,
    Callable,
    List,
    Optional,
    TypeVar,
    Union,
    cast,
)

from ....infrastructure.effects.core import Effect, EffectType
from ....infrastructure.effects.integration.caw.caw_effect_tracker import (
    get_caw_effect_tracker,
)
from ....infrastructure.effects.integration.caw.wave_effect import (
    WaveEffect,
    get_effect_wave_function,
)
from ....infrastructure.wave.core import Context, WaveFunction

# Configure logging
logger = logging.getLogger(__name__)

# Type variables
T = TypeVar("T")
F = TypeVar("F", bound=Callable[..., Any])


def wave_effects(
    effect_types: List[EffectType],
    wave_function: Optional[Union[str, WaveFunction]] = None,
    context_param: str = "context",
) -> Callable[[F], F]:
    """
    Decorator for tracking wave effects.

    This decorator tracks the specified effect types using wave functions
    in the context provided by the context_param parameter.

    Args:
        effect_types: The effect types to track
        wave_function: Optional wave function name or instance
        context_param: The name of the context parameter

    Returns:
        Decorator function
    """

    def decorator(func: F) -> F:
        @functools.wraps(func)
        async def async_wrapper(*args: Any, **kwargs: Any) -> Any:
            # Get the context
            context = kwargs.get(context_param)
            if context is None:
                # Try to find the context in positional arguments
                for arg in args:
                    if isinstance(arg, Context):
                        context = arg
                        break

            if context is None:
                logger.warning(f"No context found for wave effects in {func.__name__}")
                return await func(*args, **kwargs)

            # Get the source
            source = f"{func.__module__}.{func.__qualname__}"

            # Track the effects
            tracker = get_caw_effect_tracker()
            for effect_type in effect_types:
                tracker.track_wave_effect(
                    effect_type=effect_type,
                    source=source,
                    context=context,
                    wave_function=wave_function,
                )

            # Call the function
            return await func(*args, **kwargs)

        @functools.wraps(func)
        def sync_wrapper(*args: Any, **kwargs: Any) -> Any:
            # Get the context
            context = kwargs.get(context_param)
            if context is None:
                # Try to find the context in positional arguments
                for arg in args:
                    if isinstance(arg, Context):
                        context = arg
                        break

            if context is None:
                logger.warning(f"No context found for wave effects in {func.__name__}")
                return func(*args, **kwargs)

            # Get the source
            source = f"{func.__module__}.{func.__qualname__}"

            # Track the effects
            tracker = get_caw_effect_tracker()
            for effect_type in effect_types:
                tracker.track_wave_effect(
                    effect_type=effect_type,
                    source=source,
                    context=context,
                    wave_function=wave_function,
                )

            # Call the function
            return func(*args, **kwargs)

        # Use the appropriate wrapper based on whether the function is async
        if asyncio.iscoroutinefunction(func):
            return cast(F, async_wrapper)
        else:
            return cast(F, sync_wrapper)

    return decorator


def with_wave_effects(
    effect_type: EffectType, wave_function: Optional[Union[str, WaveFunction]] = None
) -> Callable[[F], F]:
    """
    Decorator for functions with wave effects.

    This decorator combines the @effects decorator with wave effect
    tracking, enabling tracking of side effects using wave functions.

    Args:
        effect_type: The type of effect
        wave_function: Optional wave function name or instance

    Returns:
        Decorator function

    Example:
    ```python
    @with_wave_effects(EffectType.IO, wave_function="io")
    def read_file(filename, context):
        # Implementation
        return content
    ```
    """

    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            # Call the function
            result = func(*args, **kwargs)

            # If the result is already a WaveEffect, return it
            if isinstance(result, WaveEffect):
                return result

            # If the result is an Effect, convert it to a WaveEffect
            if isinstance(result, Effect):
                # Resolve wave function
                resolved_wave_function = None
                if wave_function is not None:
                    if isinstance(wave_function, str):
                        resolved_wave_function = get_effect_wave_function(wave_function)
                    else:
                        resolved_wave_function = wave_function

                # Create wave effect
                return WaveEffect(
                    effect_type=result.effect_type,
                    operation=result.operation,
                    wave_function=resolved_wave_function,
                    computation=result.computation,
                    *result.args,
                    **result.kwargs,
                )

            # Otherwise, wrap it in a WaveEffect
            return WaveEffect(
                effect_type=effect_type,
                operation=f"{func.__name__}",
                wave_function=wave_function,
                computation=lambda: result,
            )

        return cast(F, wrapper)

    return decorator
