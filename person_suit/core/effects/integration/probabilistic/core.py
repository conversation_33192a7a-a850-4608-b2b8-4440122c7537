"""
Core Classes for Effect Systems and Probabilistic Programming Integration
=======================================================================

This module implements core classes for integrating Effect Systems with
Differentiable Probabilistic Programming, enabling tracking and management
of side effects in probabilistic computations.

Related Files:
- ../../core.py: Effect systems core types
- ../../../differentiable/integration/probabilistic/core.py: Differentiable probabilistic core types
- ../../../probabilistic/distributions.py: Probability distributions

Dependencies:
- Python 3.8+
- NumPy
"""

import logging
import math
from enum import Enum, auto
from typing import (
    Any,
    Callable,
    Dict,
    List,
    Optional,
    TypeVar,
    Union,
)

from ...handlers.base import BaseEffectHandler
from ....infrastructure.caw.core import CAWProcessor
from ....infrastructure.differentiable.core import (
    Variable,
)
from ....infrastructure.differentiable.integration.probabilistic.core import (
    DifferentiableDistribution,
    DifferentiableProbabilistic,
)
from ....infrastructure.effects.core import (
    Effect,
    EffectResult,
    EffectType,
)
from ....infrastructure.probabilistic.distributions import (
    Distribution,
)
from ....infrastructure.wave.core import Context, Information, WaveFunction

# Configure logging
logger = logging.getLogger(__name__)

# Type variables for generic types
T = TypeVar("T")
V = TypeVar("V")


class ProbabilisticEffectType(Enum):
    """Types of probabilistic effects."""

    SAMPLING = auto()  # Sampling from distributions
    INFERENCE = auto()  # Probabilistic inference
    UNCERTAINTY = auto()  # Uncertainty quantification
    OPTIMIZATION = auto()  # Optimization of probabilistic models
    CUSTOM = auto()  # Custom probabilistic effects


class ProbabilisticEffect(Effect):
    """
    An effect that represents a probabilistic computation.

    This class extends the Effect class to support probabilistic computations,
    enabling tracking and management of side effects in probabilistic models.
    """

    def __init__(
        self,
        effect_type: Union[EffectType, ProbabilisticEffectType],
        operation: str,
        computation: Optional[Callable[..., Any]] = None,
        uncertainty: Optional[float] = None,
        distribution: Optional[Distribution] = None,
        wave_function: Optional[WaveFunction] = None,
        *args: Any,
        **kwargs: Any,
    ):
        """
        Initialize a probabilistic effect.

        Args:
            effect_type: The type of effect
            operation: The operation to perform
            computation: Optional function to compute the result
            uncertainty: Optional uncertainty level
            distribution: Optional probability distribution
            wave_function: Optional wave function for context-sensitive behavior
            *args: Positional arguments for the computation
            **kwargs: Keyword arguments for the computation
        """
        super().__init__(effect_type, operation, computation, *args, **kwargs)
        self.uncertainty = uncertainty
        self.distribution = distribution
        self.wave_function = wave_function or self._create_default_wave_function()

    def with_uncertainty(self, uncertainty: float) -> "ProbabilisticEffect":
        """
        Set the uncertainty level of the effect.

        Args:
            uncertainty: Uncertainty level

        Returns:
            Self with uncertainty set
        """
        self.uncertainty = uncertainty
        return self

    def with_distribution(self, distribution: Distribution) -> "ProbabilisticEffect":
        """
        Set the probability distribution of the effect.

        Args:
            distribution: Probability distribution

        Returns:
            Self with distribution set
        """
        self.distribution = distribution
        return self

    def to_differentiable(self) -> DifferentiableProbabilistic:
        """
        Convert the effect to a differentiable probabilistic object.

        Returns:
            A differentiable probabilistic object
        """

        # If we have a distribution, convert it to a differentiable distribution
        if self.distribution is not None:
            return DifferentiableDistribution(self.distribution)

        # Otherwise, create a variable with uncertainty
        if self.computation is not None:
            result = self.computation(*self.args, **self.kwargs)
            var = Variable(result)

            if self.uncertainty is not None:
                var.uncertainty = self.uncertainty

            return var

        # If we don't have a computation or distribution, create a default variable
        return Variable(0.0)

    def _create_default_wave_function(self) -> WaveFunction:
        """
        Create a default wave function for the effect.

        Returns:
            A default wave function
        """

        # Create amplitude function based on uncertainty
        def amplitude_func(effect, context):
            if not isinstance(effect, ProbabilisticEffect):
                return 0.0

            # Higher uncertainty means lower amplitude
            if effect.uncertainty is not None:
                return max(0.0, 1.0 - effect.uncertainty)

            # If we have a distribution, use its entropy
            if effect.distribution is not None:
                try:
                    # Normalize entropy to [0, 1] range
                    entropy = effect.distribution.entropy
                    return max(0.0, 1.0 - min(1.0, entropy / 10.0))
                except (AttributeError, ValueError):
                    pass

            return 0.8  # Default amplitude

        # Create phase function based on effect type
        def phase_func(effect, context):
            if not isinstance(effect, ProbabilisticEffect):
                return 0.0

            # Different phase for different effect types
            if isinstance(effect.effect_type, ProbabilisticEffectType):
                if effect.effect_type == ProbabilisticEffectType.SAMPLING:
                    return 0.0
                elif effect.effect_type == ProbabilisticEffectType.INFERENCE:
                    return math.pi / 4
                elif effect.effect_type == ProbabilisticEffectType.UNCERTAINTY:
                    return math.pi / 2
                elif effect.effect_type == ProbabilisticEffectType.OPTIMIZATION:
                    return 3 * math.pi / 4

            return 0.0

        return WaveFunction(amplitude_func, phase_func)

    def as_information(self, context: Context) -> Information:
        """
        Convert the effect to an information object.

        Args:
            context: The context in which to interpret the effect

        Returns:
            An information object representing the effect
        """
        return Information(self, self.wave_function)

    def interpret_in_context(self, context: Context) -> Information:
        """
        Interpret the effect in a specific context.

        Args:
            context: The context in which to interpret the effect

        Returns:
            The interpreted effect
        """
        # Convert to information
        info = self.as_information(context)

        # Interpret in the context
        return info.interpret(context)

    def __repr__(self) -> str:
        """String representation of the probabilistic effect."""
        return (
            f"ProbabilisticEffect(effect_type={self.effect_type}, "
            f"operation={self.operation}, "
            f"uncertainty={self.uncertainty}, "
            f"distribution={self.distribution}, "
            f"effect_id={self.effect_id})"
        )


class ProbabilisticEffectHandler(BaseEffectHandler):
    """
    Handler for probabilistic effects.

    This handler integrates with the CAW paradigm by:
    1. Supporting wave-particle duality through probabilistic computations
    2. Being context-aware in all operations
    3. Using wave functions for uncertainty quantification
    4. Enabling adaptive computation based on context
    """

    def __init__(self):
        """Initialize the probabilistic effect handler."""
        super().__init__("probabilistic_handler")
        self.caw_processor = CAWProcessor()

    def can_handle(self, effect_type: EffectTypeInterface) -> bool:
        """Check if this handler can handle the given effect type."""
        return isinstance(effect_type, ProbabilisticEffectType)

    def can_handle_operation(
        self, effect_type: EffectTypeInterface, operation: str
    ) -> bool:
        """Check if this handler can handle the given operation."""
        return operation in ["inference", "uncertainty", "optimization"]

    def get_supported_operations(self, effect_type: EffectTypeInterface) -> List[str]:
        """Get the operations supported by this handler."""
        return ["inference", "uncertainty", "optimization"]

    def handle(
        self,
        effect_type: EffectTypeInterface,
        operation: str,
        context: Optional[Dict[str, Any]] = None,
        **kwargs: Any,
    ) -> Any:
        """Handle a probabilistic effect."""
        if not isinstance(effect_type, ProbabilisticEffectType):
            raise ValueError(
                f"Expected ProbabilisticEffectType, got {type(effect_type)}"
            )

        effect = kwargs.get("effect")
        if not isinstance(effect, ProbabilisticEffect):
            raise ValueError(f"Expected ProbabilisticEffect, got {type(effect)}")

        if operation == "inference":
            return self._handle_inference(effect, context)
        elif operation == "uncertainty":
            return self._handle_uncertainty(effect, context)
        elif operation == "optimization":
            return self._handle_optimization(effect, context)
        else:
            raise ValueError(f"Unsupported operation: {operation}")

    def _handle_inference(
        self, effect: ProbabilisticEffect, context: Optional[Context] = None
    ) -> EffectResult:
        """Handle an inference effect."""
        # Implementation unchanged...

    def _handle_uncertainty(
        self, effect: ProbabilisticEffect, context: Optional[Context] = None
    ) -> EffectResult:
        """Handle an uncertainty quantification effect."""
        # Implementation unchanged...

    def _handle_optimization(
        self, effect: ProbabilisticEffect, context: Optional[Context] = None
    ) -> EffectResult:
        """Handle an optimization effect."""
        # Implementation unchanged...
