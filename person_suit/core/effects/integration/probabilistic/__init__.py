"""
Integration with Differentiable Probabilistic Programming
=======================================================

This module implements integration between the Effect Systems framework and
the Differentiable Probabilistic Programming framework. It allows for tracking
and managing side effects in differentiable probabilistic computations.

Related Files:
- ../../core.py: Effect systems core types
- ../../../differentiable/integration/probabilistic/core.py: Differentiable probabilistic core types
- ../../../probabilistic/distributions.py: Probability distributions

Dependencies:
- Python 3.8+
- NumPy
"""

from ....infrastructure.effects.integration.probabilistic.conversions import (
    effect_to_probabilistic,
    probabilistic_to_effect,
)
from ....infrastructure.effects.integration.probabilistic.core import (
    ProbabilisticEffect,
    ProbabilisticEffectHandler,
    ProbabilisticEffectType,
)
from ....infrastructure.effects.integration.probabilistic.decorators import (
    probabilistic_effect_handler,
    with_probabilistic_effects,
)

# Define public API
__all__ = [
    # Core classes
    "ProbabilisticEffect",
    "ProbabilisticEffectHandler",
    "ProbabilisticEffectType",
    # Decorators
    "with_probabilistic_effects",
    "probabilistic_effect_handler",
    # Conversion functions
    "effect_to_probabilistic",
    "probabilistic_to_effect",
]
