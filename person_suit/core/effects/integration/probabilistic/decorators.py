"""
Decorators for Effect Systems and Probabilistic Programming Integration
=====================================================================

This module implements decorators for integrating Effect Systems with
Differentiable Probabilistic Programming, enabling tracking and management
of side effects in probabilistic computations.

Related Files:
- ./core.py: Core classes for effect systems and probabilistic programming integration
- ../../decorators.py: Effect system decorators
- ../../../differentiable/integration/probabilistic/decorators.py: Differentiable probabilistic decorators

Dependencies:
- Python 3.8+
- NumPy
"""

import functools
import logging
from typing import (
    Any,
    Callable,
    Optional,
    TypeVar,
    Union,
    cast,
)

from ....infrastructure.effects.core import (
    Effect,
    EffectType,
)
from ....infrastructure.effects.integration.probabilistic.core import (
    ProbabilisticEffect,
    ProbabilisticEffectType,
)

# Configure logging
logger = logging.getLogger(__name__)

# Type variables for generic types
F = TypeVar("F", bound=Callable[..., Any])
T = TypeVar("T")


def with_probabilistic_effects(
    effect_type: Union[EffectType, ProbabilisticEffectType],
    uncertainty_level: Optional[float] = None,
) -> Callable[[F], F]:
    """
    Decorator for functions with probabilistic effects.

    This decorator combines the @with_effects decorator with probabilistic
    effect handling, enabling tracking of side effects in probabilistic
    computations.

    Args:
        effect_type: The type of effect
        uncertainty_level: Optional uncertainty level

    Returns:
        Decorator function

    Example:
    ```python
    @with_probabilistic_effects(ProbabilisticEffectType.SAMPLING, uncertainty_level=0.1)
    def sample_from_model(model, n_samples=10):
        # Implementation
        return samples
    ```
    """

    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            # Call the function
            result = func(*args, **kwargs)

            # If the result is already a ProbabilisticEffect, return it
            if isinstance(result, ProbabilisticEffect):
                return result

            # If the result is an Effect, convert it to a ProbabilisticEffect
            if isinstance(result, Effect):
                prob_effect = ProbabilisticEffect(
                    effect_type=result.effect_type,
                    operation=result.operation,
                    computation=result.computation,
                    uncertainty=uncertainty_level,
                    *result.args,
                    **result.kwargs,
                )
                prob_effect.metadata = result.metadata.copy()
                return prob_effect

            # Otherwise, create a new ProbabilisticEffect
            return ProbabilisticEffect(
                effect_type=effect_type,
                operation=func.__name__,
                computation=lambda: result,
                uncertainty=uncertainty_level,
            )

        # Mark the function as having probabilistic effects
        wrapper.__has_probabilistic_effects__ = True

        return cast(F, wrapper)

    return decorator


def probabilistic_effect_handler(
    effect_type: Union[EffectType, ProbabilisticEffectType],
) -> Callable[[F], F]:
    """
    Decorator for probabilistic effect handlers.

    This decorator marks a function as a handler for a specific type of
    probabilistic effect.

    Args:
        effect_type: The type of effect to handle

    Returns:
        Decorator function

    Example:
    ```python
    @probabilistic_effect_handler(ProbabilisticEffectType.SAMPLING)
    def handle_sampling(effect):
        # Implementation
        return result
    ```
    """

    def decorator(func: F) -> F:
        func.__handled_probabilistic_effect__ = effect_type
        return func

    return decorator
