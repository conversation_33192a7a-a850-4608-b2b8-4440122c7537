"""
Conversion Functions for Effect Systems and Probabilistic Programming Integration
==============================================================================

This module implements conversion functions between effects and probabilistic
objects, enabling seamless integration between the two paradigms.

Related Files:
- ./core.py: Core classes for effect systems and probabilistic programming integration
- ../../core.py: Effect systems core types
- ../../../differentiable/integration/probabilistic/conversions.py: Differentiable probabilistic conversions

Dependencies:
- Python 3.8+
- NumPy
"""

import logging
from typing import (
    Optional,
    TypeVar,
    Union,
)

import numpy as np

from ....infrastructure.differentiable.core import (
    Differentiable,
)
from ....infrastructure.differentiable.integration.probabilistic.conversions import (
    differentiable_to_probabilistic,
)
from ....infrastructure.differentiable.integration.probabilistic.core import (
    DifferentiableProbabilistic,
)
from ....infrastructure.effects.core import (
    Effect,
    EffectType,
)
from ....infrastructure.effects.integration.probabilistic.core import (
    ProbabilisticEffect,
    ProbabilisticEffectType,
)
from ....infrastructure.probabilistic.distributions import (
    Distribution,
    Gaussian,
)

# Configure logging
logger = logging.getLogger(__name__)

# Type variables for generic types
T = TypeVar("T")
V = TypeVar("V")


def effect_to_probabilistic(
    effect: Effect, uncertainty_level: Optional[float] = None
) -> Union[Distribution, DifferentiableProbabilistic]:
    """
    Convert an effect to a probabilistic object.

    Args:
        effect: The effect to convert
        uncertainty_level: Optional uncertainty level for the conversion

    Returns:
        A probabilistic object

    Raises:
        TypeError: If the effect cannot be converted
    """
    # If the effect is already a ProbabilisticEffect, use its distribution or uncertainty
    if isinstance(effect, ProbabilisticEffect):
        if effect.distribution is not None:
            return effect.distribution
        elif effect.uncertainty is not None:
            uncertainty_level = effect.uncertainty

    # If the effect has a computation, execute it to get the result
    if effect.computation is not None:
        result = effect.computation(*effect.args, **effect.kwargs)

        # If the result is already a probabilistic object, return it
        if isinstance(result, (Distribution, DifferentiableProbabilistic)):
            return result

        # If the result is a differentiable object, convert it to a probabilistic object
        if isinstance(result, Differentiable):
            return differentiable_to_probabilistic(result, uncertainty_level)

        # Otherwise, create a Gaussian distribution based on the result
        if isinstance(result, (int, float)) or (
            isinstance(result, np.ndarray) and result.size == 1
        ):
            # Use the provided uncertainty level or a default
            if uncertainty_level is None:
                uncertainty_level = 0.1

            return Gaussian(mean=result, std=uncertainty_level * abs(result))

        elif isinstance(result, np.ndarray):
            # Use the provided uncertainty level or a default
            if uncertainty_level is None:
                uncertainty_level = 0.1

            return Gaussian(mean=result, std=uncertainty_level * np.abs(result))

    # If we can't convert the effect, raise an error
    raise TypeError(
        f"Cannot convert effect of type {type(effect)} to probabilistic object"
    )


def probabilistic_to_effect(
    probabilistic_obj: Union[Distribution, DifferentiableProbabilistic],
    effect_type: Optional[Union[EffectType, ProbabilisticEffectType]] = None,
) -> Effect:
    """
    Convert a probabilistic object to an effect.

    Args:
        probabilistic_obj: The probabilistic object to convert
        effect_type: Optional effect type for the conversion

    Returns:
        An effect

    Raises:
        TypeError: If the probabilistic object cannot be converted
    """
    # If no effect type is provided, use a default
    if effect_type is None:
        effect_type = ProbabilisticEffectType.SAMPLING

    # If the object is a Distribution, create a ProbabilisticEffect with the distribution
    if isinstance(probabilistic_obj, Distribution):
        return ProbabilisticEffect(
            effect_type=effect_type, operation="sample", distribution=probabilistic_obj
        )

    # If the object is a DifferentiableProbabilistic, extract its distribution or value
    if isinstance(probabilistic_obj, DifferentiableProbabilistic):
        if (
            hasattr(probabilistic_obj, "distribution")
            and probabilistic_obj.distribution is not None
        ):
            return ProbabilisticEffect(
                effect_type=effect_type,
                operation="sample",
                distribution=probabilistic_obj.distribution,
            )
        elif (
            hasattr(probabilistic_obj, "value") and probabilistic_obj.value is not None
        ):
            # Create a computation that returns the value
            def computation():
                return probabilistic_obj.value

            # Create a ProbabilisticEffect with the computation
            effect = ProbabilisticEffect(
                effect_type=effect_type, operation="compute", computation=computation
            )

            # Add uncertainty if available
            if (
                hasattr(probabilistic_obj, "uncertainty")
                and probabilistic_obj.uncertainty is not None
            ):
                effect.uncertainty = probabilistic_obj.uncertainty

            return effect

    # If we can't convert the probabilistic object, raise an error
    raise TypeError(
        f"Cannot convert probabilistic object of type {type(probabilistic_obj)} to effect"
    )
