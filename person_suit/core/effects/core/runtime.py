"""Effect Runtime.

This module implements the effect runtime system that manages effect execution
and lifecycle. It integrates with the CAW paradigm by managing wave-particle
duality in effect execution and context propagation.
"""

import asyncio
import logging
from contextlib import asynccontextmanager
from typing import TYPE_CHECKING, Any, Dict, List, Optional

from .registry import EffectRegistry
from ._internal_types import Effect, HandlerMetrics, ResourceMetrics

if TYPE_CHECKING:
    from .base import EffectHandler

logger = logging.getLogger(__name__)


class EffectRuntime:
    """Runtime system for effect handling.

    Manages:
    - Effect lifecycle and execution
    - Handler registration and discovery
    - Resource management and monitoring
    - Context propagation and wave-particle state transitions
    """

    def __init__(self):
        """Initialize the runtime."""
        self._registry = EffectRegistry()
        self._metrics: Dict[str, HandlerMetrics] = {}
        self._resources: Dict[str, ResourceMetrics] = {}
        self._context_stack: List[Dict[str, Any]] = []

    async def initialize(self) -> None:
        """Initialize the runtime system."""
        # Initialize core handlers
        await self._initialize_core_handlers()

        # Initialize metrics
        self._initialize_metrics()

        logger.info("Effect runtime initialized")

    async def shutdown(self) -> None:
        """Shutdown the runtime system."""
        # Unregister all handlers
        handler_types = list(self._registry._handlers.keys())
        for handler_type in handler_types:
            await self._registry.unregister_handler(handler_type)

        # Clear metrics and context
        self._metrics.clear()
        self._resources.clear()
        self._context_stack.clear()

        logger.info("Effect runtime shut down")

    async def register_handler(
        self, handler_type: str, handler: "EffectHandler"
    ) -> None:
        """Register a new effect handler.

        Args:
            handler_type: Type of handler to register
            handler: Handler instance
        """
        await self._registry.register_handler(handler_type, handler)
        self._metrics[handler_type] = HandlerMetrics(
            operation_count=0,
            success_rate=1.0,
            latency={"avg": 0.0, "min": 0.0, "max": 0.0},
            resource_usage={"memory": 0.0, "cpu": 0.0},
        )

    async def handle_effect(
        self, effect: Effect, context: Optional[Dict[str, Any]] = None
    ) -> Any:
        """Handle an effect.

        Args:
            effect: Effect to handle
            context: Runtime context

        Returns:
            Handler result
        """
        # Merge context with current context stack
        merged_context = self._merge_context(context)

        start_time = asyncio.get_event_loop().time()
        try:
            result = await self._registry.handle_effect(effect, merged_context)
            self._update_metrics(effect.effect_type, True, start_time)
            return result
        except Exception:
            self._update_metrics(effect.effect_type, False, start_time)
            raise

    @asynccontextmanager
    async def context(self, **context):
        """Context manager for effect handling.

        Args:
            **context: Context key-value pairs
        """
        self._context_stack.append(context)
        try:
            yield
        finally:
            self._context_stack.pop()

    async def _initialize_core_handlers(self) -> None:
        """Initialize core effect handlers."""
        # This should be loaded from configuration
        from ..handlers.advanced.tensor import TensorHandler
        from ..handlers.core.database import DatabaseHandler
        from ..handlers.core.io import IOHandler

        await self.register_handler("database", DatabaseHandler())
        await self.register_handler("io", IOHandler())
        await self.register_handler("tensor", TensorHandler())

    def _initialize_metrics(self) -> None:
        """Initialize metrics tracking."""
        self._resources = {
            "system": ResourceMetrics(
                memory_usage={"used": 0.0, "total": 0.0},
                cpu_usage={"user": 0.0, "system": 0.0},
                io_operations={"read": 0, "write": 0},
                network_usage={"sent": 0.0, "received": 0.0},
            )
        }

    def _merge_context(self, context: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Merge context with context stack.

        Args:
            context: Context to merge

        Returns:
            Merged context
        """
        merged = {}
        for ctx in self._context_stack:
            merged.update(ctx)
        if context:
            merged.update(context)
        return merged

    def _update_metrics(
        self, handler_type: str, success: bool, start_time: float
    ) -> None:
        """Update handler metrics.

        Args:
            handler_type: Handler type
            success: Whether operation succeeded
            start_time: Operation start time
        """
        metrics = self._metrics.get(handler_type)
        if not metrics:
            return

        # Update operation count and success rate
        metrics.operation_count += 1
        if not success:
            metrics.success_rate = (
                (metrics.operation_count - 1) * metrics.success_rate + 0
            ) / metrics.operation_count

        # Update latency
        latency = asyncio.get_event_loop().time() - start_time
        metrics.latency["avg"] = (
            (metrics.operation_count - 1) * metrics.latency["avg"] + latency
        ) / metrics.operation_count
        metrics.latency["min"] = min(metrics.latency["min"], latency)
        metrics.latency["max"] = max(metrics.latency["max"], latency)

    async def _run_effect(
        self,
        effect: Effect,
        handler: "EffectHandler",
        context: Optional[Dict[str, Any]] = None,
    ) -> Any:
        """Run the given effect using the specified handler and context.

        Args:
            effect: The effect to execute.
            handler: The effect handler instance.
            context: Optional context for effect execution.

        Returns:
            The result of the effect execution.

        Raises:
            NotImplementedError: If not implemented in subclass.
        """
        raise NotImplementedError("_run_effect must be implemented by subclasses.")
