"""Base Effect Handler.

This module defines the base interface for effect handlers in the system.
It implements the core CAW paradigm principles of wave-particle duality
and context-aware computation.
"""

import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, Generic, Optional, TypeVar

from ._internal_types import Effect, EffectState

logger = logging.getLogger(__name__)

T = TypeVar("T")


class EffectHandler(Generic[T], ABC):
    """Base class for effect handlers.

    Implements:
    - Wave-particle duality in effect handling
    - Context-aware computation
    - Resource management
    - Metrics tracking

    Type Parameters:
        T: Type of handler result
    """

    def __init__(self):
        """Initialize the handler."""
        self._resources: Dict[str, Any] = {}
        self._metrics: Dict[str, float] = {}
        self._wave_states: Dict[str, EffectState] = {}
        self._particle_states: Dict[str, EffectState] = {}

    @abstractmethod
    def can_handle(self, effect: Effect) -> bool:
        """Check if handler can handle an effect.

        Args:
            effect: The effect to check

        Returns:
            True if handler can handle the effect
        """
        pass

    @abstractmethod
    async def analyze_wave_state(
        self, effect: Effect, context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Analyze potential effect operations in wave state.

        This method implements the wave aspect of the CAW paradigm,
        analyzing potential operations before they manifest.

        Args:
            effect: The effect to analyze
            context: Runtime context

        Returns:
            Analysis results
        """
        pass

    @abstractmethod
    async def execute_particle_state(
        self, effect: Effect, context: Optional[Dict[str, Any]] = None
    ) -> T:
        """Execute effect operations in particle state.

        This method implements the particle aspect of the CAW paradigm,
        executing concrete operations after wave state collapse.

        Args:
            effect: The effect to execute
            context: Runtime context

        Returns:
            Operation result
        """
        pass

    async def _initialize_resources(self) -> None:
        """Initialize handler resources.

        This method should be overridden by handlers that need
        to initialize specific resources.
        """
        pass

    async def _cleanup_resources(self) -> None:
        """Clean up handler resources.

        This method should be overridden by handlers that need
        to clean up specific resources.
        """
        pass

    def _get_wave_state(self, effect_id: str) -> Optional[EffectState]:
        """Get wave state for effect.

        Args:
            effect_id: Effect ID

        Returns:
            Wave state if exists
        """
        return self._wave_states.get(effect_id)

    def _get_particle_state(self, effect_id: str) -> Optional[EffectState]:
        """Get particle state for effect.

        Args:
            effect_id: Effect ID

        Returns:
            Particle state if exists
        """
        return self._particle_states.get(effect_id)

    def _set_wave_state(self, effect_id: str, state: EffectState) -> None:
        """Set wave state for effect.

        Args:
            effect_id: Effect ID
            state: Wave state
        """
        self._wave_states[effect_id] = state

    def _set_particle_state(self, effect_id: str, state: EffectState) -> None:
        """Set particle state for effect.

        Args:
            effect_id: Effect ID
            state: Particle state
        """
        self._particle_states[effect_id] = state

    def _clear_states(self, effect_id: str) -> None:
        """Clear states for effect.

        Args:
            effect_id: Effect ID
        """
        self._wave_states.pop(effect_id, None)
        self._particle_states.pop(effect_id, None)

    def _update_metrics(self, metric_name: str, value: float) -> None:
        """Update handler metrics.

        Args:
            metric_name: Metric name
            value: Metric value
        """
        self._metrics[metric_name] = value
