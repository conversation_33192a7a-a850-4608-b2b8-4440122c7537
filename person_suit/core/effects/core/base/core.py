"""
File: core.py
Purpose: Core effect type definitions for the effect system.

This module defines the core types and classes for the effect system,
including the Effect class and EffectType enum. These components
form the foundation of the effect system, allowing for explicit tracking
and handling of computational effects.

Related Files:
- person_suit/core/infrastructure/effects/handlers.py: Effect handlers
- person_suit/core/infrastructure/effects/runtime.py: Effect runtime

Dependencies:
- typing>=4.0.0: For type annotations
- abc: For abstract base classes
"""

import functools
import inspect
import logging
import time
import uuid
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum, auto
from typing import (
    TYPE_CHECKING,
    Any,
    Callable,
    Dict,
    Generic,
    List,
    Optional,
    Set,
    TypeVar,
)

# Import interfaces
from ...interfaces import (
    EffectHandlerInterface,
    EffectInterface,
    EffectResultInterface,
    EffectTypeInterface,
)

if TYPE_CHECKING:
    from person_suit.core.effects.interfaces.handler import EffectHandlerInterface

# Lazy import for concrete implementations to avoid circular imports
DualWaveFunction = None
DualContext = None
DualInformation = None
DualWaveFunctionInterface = Any
DualContextInterface = Any
DualInformationInterface = Any


# Initialize lazy imports
def _initialize_lazy_imports():
    global DualWaveFunction, DualContext, DualInformation

    if DualWaveFunction is None:
        from person_suit.core.infrastructure.dual_wave.wave_function import DualWaveFunction
        from person_suit.core.context import DualContext
        from person_suit.core.infrastructure.dual_wave.information import DualInformation


# Configure logging
logger = logging.getLogger(__name__)

# Type variables
T = TypeVar("T")  # Result type
E = TypeVar("E")  # Effect type


# Effect result type
class EffectResult(Generic[T], EffectResultInterface[T]):
    """Represents the result of an effect computation.

    This class implements the EffectResultInterface, ensuring that it adheres to
    the contract defined by the interface.
    """

    def __init__(self, value: T, metadata: Dict[str, Any] = None):
        """
        Initialize an effect result.

        Args:
            value: The result value
            metadata: Optional metadata about the effect execution
        """
        self._value = value
        self._metadata = metadata or {}

    @property
    def value(self) -> T:
        """
        Get the result value.

        Returns:
            The result value
        """
        return self._value

    @property
    def metadata(self) -> Dict[str, Any]:
        """
        Get the result metadata.

        Returns:
            The result metadata
        """
        return self._metadata

    def __repr__(self) -> str:
        return f"EffectResult(value={self._value}, metadata={self._metadata})"


from .metaclass import interface_delegate

# Create a delegate class for EffectTypeInterface
EffectTypeDelegate = interface_delegate(EffectTypeInterface)


class EffectType(Enum):
    """
    Enum for effect types.

    Effect types represent categories of computational effects, such as IO,
    Database, Network, etc. They are used to track and control the effects
    that computations can have on the system state.

    This class implements the EffectTypeInterface through delegation rather than
    inheritance to avoid metaclass conflicts. The delegation pattern aligns with
    the CAW paradigm's dual wave-particle representation by separating interface
    (wave aspect) from implementation (particle aspect).
    """

    IO = auto()  # Input/output operations
    DATABASE = auto()  # Database operations
    NETWORK = auto()  # Network operations
    STATE = auto()  # State modifications
    RANDOM = auto()  # Random number generation
    PURE = auto()  # No side effects
    MEMORY = auto()  # Memory operations
    COMPUTATION = auto()  # Heavy computation
    TELEMETRY = auto()  # Telemetry/monitoring
    SECURITY = auto()  # Security-related operations
    INFORMATION_FLOW = auto()  # Information flow between components
    CONTEXT_SWITCH = auto()  # Context switching operations
    MESSAGING = auto()  # Message passing operations
    STATE_READ = auto()  # State read operations
    STATE_CHANGE = auto()  # State change operations
    ACTOR_CREATION = auto()  # Actor creation operations
    ACTOR_LIFECYCLE = auto()  # Actor lifecycle operations
    CONVERSION = auto()  # Data conversion operations
    CREATION = auto()  # Entity creation operations
    RESOURCE_MANAGEMENT = auto()  # Resource management operations
    CUSTOM = auto()  # Custom effect types

    @classmethod
    def name(cls) -> str:
        """
        Get the name of the effect type.

        Returns:
            The name of the effect type
        """
        return cls.__name__

    # Register the class with the interface
    @classmethod
    def __init_subclass__(cls, **kwargs):
        super().__init_subclass__(**kwargs)
        # Register this class with the EffectTypeInterface
        EffectTypeInterface.register(cls)

    def __str__(self) -> str:
        """
        Get string representation of the effect type.

        Returns:
            String representation
        """
        return str(self.name)

    def __repr__(self) -> str:
        """
        Get detailed string representation of the effect type.

        Returns:
            Detailed string representation
        """
        return f"EffectType({self.name})"


@dataclass
class Effect(Generic[E, T], EffectInterface[E, T]):
    """
    Representation of a computation with effect E and result type T.

    An Effect represents a computation that may have side effects. The effect
    type E specifies what kind of effects the computation may have, and the
    result type T specifies the type of the result of the computation.

    Effects are executed by effect handlers, which interpret the effect and
    perform the actual computation.

    This class implements the EffectInterface, ensuring that it adheres to
    the contract defined by the interface.
    """

    def __init__(
        self,
        effect_type: E,
        operation: str,
        computation: Optional[Callable[..., T]] = None,
        *args: Any,
        **kwargs: Any,
    ):
        """
        Initialize a new effect.

        Args:
            effect_type: The type of effect
            operation: The operation to perform
            computation: Optional function to compute the result
            *args: Positional arguments for the computation
            **kwargs: Keyword arguments for the computation
        """
        self._effect_type = effect_type
        self._operation = operation
        self._computation = computation
        self._args = args
        self._kwargs = kwargs
        self._effect_id = str(uuid.uuid4())
        self._created_at = time.time()
        self._metadata: Dict[str, Any] = {}
        self._context: Optional[DualContextInterface] = None
        self._wave_function: Optional[DualWaveFunctionInterface] = None

    @property
    def effect_type(self) -> E:
        """
        Get the effect type.

        Returns:
            The effect type
        """
        return self._effect_type

    @property
    def operation(self) -> str:
        """
        Get the operation.

        Returns:
            The operation
        """
        return self._operation

    @property
    def computation(self) -> Optional[Callable[..., T]]:
        """
        Get the computation.

        Returns:
            The computation
        """
        return self._computation

    @property
    def args(self) -> tuple:
        """
        Get the positional arguments.

        Returns:
            The positional arguments
        """
        return self._args

    @property
    def kwargs(self) -> dict:
        """
        Get the keyword arguments.

        Returns:
            The keyword arguments
        """
        return self._kwargs

    def run_with(self, handler: Any) -> T:
        """
        Run the effect with a handler.

        Args:
            handler: The handler to use for running the effect

        Returns:
            The result of running the effect
        """
        # If the handler supports context-aware handling, use it
        if hasattr(handler, "handle_in_context") and self._context is not None:
            return handler.handle_in_context(
                self._effect_type,
                self._operation,
                self._context,
                self._computation,
                *self._args,
                **self._kwargs,
            )

        # Otherwise, use standard handling
        return handler.handle(
            self._effect_type,
            self._operation,
            self._computation,
            *self._args,
            **self._kwargs,
        )

    def with_context(self, context: Any) -> "Effect[E, T]":
        """
        Set the context of the effect.

        Args:
            context: The context to set

        Returns:
            Self with context set
        """
        self._context = context
        return self

    def with_wave_function(
        self, wave_function: DualWaveFunctionInterface
    ) -> "Effect[E, T]":
        """
        Set the wave function of the effect.

        Args:
            wave_function: The wave function to set

        Returns:
            Self with wave function set
        """
        self._wave_function = wave_function
        return self

    def as_information(self) -> Optional[DualInformationInterface]:
        """
        Convert the effect to an information object.

        Returns:
            An information object representing the effect, or None if no wave function is set
        """
        if self._wave_function is None:
            return None

        # Lazily import DualInformation to avoid circular imports
        global DualInformation
        if DualInformation is None:
            _initialize_lazy_imports()

        return DualInformation(self, self._wave_function)

    def interpret_in_context(
        self, context: DualContextInterface
    ) -> Optional[DualInformationInterface]:
        """
        Interpret the effect in a specific context.

        Args:
            context: The context in which to interpret the effect

        Returns:
            The interpreted effect, or None if no wave function is set
        """
        info = self.as_information()
        if info is None:
            return None

        return info.interpret(context)

    def map(self, f: Callable[[T], Any]) -> "Effect[E, Any]":
        """
        Transform the result of this effect.

        Args:
            f: Function to transform the result

        Returns:
            A new effect with the transformed result
        """

        def mapped_computation(*args: Any, **kwargs: Any) -> Any:
            if self._computation:
                result = self._computation(*args, **kwargs)
                return f(result)
            return None

        new_effect = Effect(
            self._effect_type,
            self._operation,
            mapped_computation,
            *self._args,
            **self._kwargs,
        )
        new_effect._metadata = self._metadata.copy()
        return new_effect

    def flat_map(self, f: Callable[[T], "Effect[E, Any]"]) -> "Effect[E, Any]":
        """
        Chain this effect with another effect.

        Args:
            f: Function to create the next effect

        Returns:
            A new effect that chains this effect with another
        """

        def chained_computation(*args: Any, **kwargs: Any) -> Any:
            if self._computation:
                result = self._computation(*args, **kwargs)
                next_effect = f(result)
                if next_effect.computation:
                    return next_effect.computation(
                        *next_effect.args, **next_effect.kwargs
                    )
            return None

        new_effect = Effect(
            self._effect_type,
            self._operation,
            chained_computation,
            *self._args,
            **self._kwargs,
        )
        new_effect._metadata = {**self._metadata, "chained_from": self._effect_id}
        return new_effect

    def __str__(self) -> str:
        """
        Get string representation of the effect.

        Returns:
            String representation
        """
        return f"Effect({self._effect_type}, {self._operation})"

    def __repr__(self) -> str:
        """
        Get detailed string representation of the effect.

        Returns:
            Detailed string representation
        """
        return (
            f"Effect(effect_type={self._effect_type}, "
            f"operation={self._operation}, "
            f"args={self._args}, "
            f"kwargs={self._kwargs}, "
            f"effect_id={self._effect_id})"
        )


# Create a delegate class for EffectHandlerInterface
EffectHandlerDelegate = interface_delegate(EffectHandlerInterface)


class IEffectHandler(ABC):
    """Interface for effect handlers.

    This class implements the EffectHandlerInterface through delegation rather than
    inheritance to avoid metaclass conflicts. The delegation pattern aligns with
    the CAW paradigm's dual wave-particle representation by separating interface
    (wave aspect) from implementation (particle aspect).
    """

    # Register this class with the EffectHandlerInterface
    @classmethod
    def __init_subclass__(cls, **kwargs):
        super().__init_subclass__(**kwargs)
        # Register this class with the EffectHandlerInterface
        EffectHandlerInterface.register(cls)

    @abstractmethod
    def can_handle(self, effect_type: Any) -> bool:
        """
        Check if this handler can handle the given effect type.

        Args:
            effect_type: The effect type to check

        Returns:
            True if this handler can handle the effect type, False otherwise
        """
        pass

    @abstractmethod
    def handle(
        self,
        effect_type: Any,
        operation: str,
        computation: Optional[Callable[..., Any]],
        *args: Any,
        **kwargs: Any,
    ) -> Any:
        """
        Handle an effect.

        Args:
            effect_type: The type of effect to handle
            operation: The operation to perform
            computation: The computation to run
            *args: Arguments to pass to the computation
            **kwargs: Keyword arguments to pass to the computation

        Returns:
            The result of handling the effect
        """
        pass


def infer_effects(func: Callable) -> Set[Any]:
    """
    Infer the effects of a function from its implementation.

    Args:
        func: The function to analyze

    Returns:
        Set of inferred effect types
    """
    inferred_effects = set()

    # Get the source code of the function
    try:
        source = inspect.getsource(func)

        # Simple pattern matching for common effects
        if "open(" in source or "file" in source.lower():
            inferred_effects.add(EffectType.IO)

        if (
            "requests." in source
            or "http" in source.lower()
            or "socket" in source.lower()
        ):
            inferred_effects.add(EffectType.NETWORK)

        if "random" in source.lower():
            inferred_effects.add(EffectType.RANDOM)

        if (
            "database" in source.lower()
            or "sql" in source.lower()
            or "query" in source.lower()
        ):
            inferred_effects.add(EffectType.DATABASE)

        # More sophisticated analysis could be added here

    except (IOError, TypeError):
        # If we can't get the source, we can't infer effects
        pass

    return inferred_effects


def effects(effect_types: List[Any], context_param: str = "context"):
    """
    Decorator to specify the effects of a function.

    Args:
        effect_types: List of effect types that the function can have
        context_param: The name of the context parameter

    Returns:
        Decorator function
    """

    def decorator(func):
        func.__effects__ = set(effect_types)

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Get the context if available
            context = kwargs.get(context_param)
            if context is None:
                # Try to find the context in positional arguments
                for arg in args:
                    if isinstance(arg, DualContext):
                        context = arg
                        break

            result = func(*args, **kwargs)

            # If result is already an Effect, return it
            if isinstance(result, Effect):
                # Set the context if available
                if context is not None and result.context is None:
                    result.context = context
                return result

            # Otherwise, wrap it in an Effect
            effect = Effect(
                effect_type=tuple(effect_types),
                operation="wrapped_function",
                computation=lambda: result,
            )

            # Set the context if available
            if context is not None:
                effect.context = context

            return effect

        return wrapper

    return decorator


class EffectTracker:
    """
    Tracks effects in the system.

    This class is responsible for tracking effects in the system, providing
    a centralized way to monitor and analyze the effects that computations
    have on the system state.

    This implementation follows the CAW paradigm's dual wave-particle representation
    by tracking both the structural aspects (particle) and the potential/field aspects
    (wave) of effects.
    """

    def __init__(self):
        """
        Initialize a new effect tracker.
        """
        self._effects = []

    def track_effect(
        self,
        effect_type: Any,
        operation: str,
        computation: Optional[Callable[..., Any]] = None,
        *args: Any,
        **kwargs: Any,
    ) -> "Effect":
        """
        Track an effect.

        Args:
            effect_type: The type of effect
            operation: The operation to perform
            computation: Optional function to compute the result
            *args: Positional arguments for the computation
            **kwargs: Keyword arguments for the computation

        Returns:
            The tracked effect
        """
        effect = Effect(
            effect_type=effect_type,
            operation=operation,
            computation=computation,
            *args,
            **kwargs,
        )
        self._effects.append(effect)
        return effect

    def get_effects(self) -> List["Effect"]:
        """
        Get all tracked effects.

        Returns:
            List of tracked effects
        """
        return self._effects

    def get_effects_by_type(self, effect_type: Any) -> List["Effect"]:
        """
        Get all effects of a specific type.

        Args:
            effect_type: The type of effects to filter by

        Returns:
            List of effects of the specified type
        """
        return [effect for effect in self._effects if effect.effect_type == effect_type]


def effect(effect_type: Any, context_param: str = "context"):
    """
    Decorator to specify a single effect for a function.

    Args:
        effect_type: The type of effect that the function has
        context_param: The name of the context parameter

    Returns:
        Decorator function
    """

    def decorator(func):
        func.__effects__ = {effect_type}

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Get the context if available
            context = kwargs.get(context_param)
            if context is None:
                # Try to find the context in positional arguments
                for arg in args:
                    if isinstance(arg, DualContext):
                        context = arg
                        break

            result = func(*args, **kwargs)

            # If result is already an Effect, return it
            if isinstance(result, Effect):
                # Set the context if available
                if context is not None and result.context is None:
                    result.context = context
                return result

            # Otherwise, wrap it in an Effect
            effect = Effect(
                effect_type=effect_type,
                operation="wrapped_function",
                computation=lambda: result,
            )

            # Set the context if available
            if context is not None:
                effect.context = context

            return effect

        return wrapper

    return decorator


def effect_handler(effect_type: Any, context_aware: bool = False):
    """
    Decorator to specify a function as an effect handler.

    Args:
        effect_type: The type of effect that the function handles
        context_aware: Whether the handler is context-aware

    Returns:
        Decorator function
    """

    def decorator(func):
        func.__handled_effect__ = effect_type
        func.__context_aware__ = context_aware
        return func

    return decorator


# Convenience functions for creating effects
def io_effect(func: Callable[..., T], *args, **kwargs) -> Effect[EffectType, T]:
    """Create an IO effect."""
    return Effect(EffectType.IO, "io_operation", func, *args, **kwargs)


def db_effect(func: Callable[..., T], *args, **kwargs) -> Effect[EffectType, T]:
    """Create a database effect."""
    return Effect(EffectType.DATABASE, "db_operation", func, *args, **kwargs)


def network_effect(func: Callable[..., T], *args, **kwargs) -> Effect[EffectType, T]:
    """Create a network effect."""
    return Effect(EffectType.NETWORK, "network_operation", func, *args, **kwargs)


def pure_effect(func: Callable[..., T], *args, **kwargs) -> Effect[EffectType, T]:
    """Create a pure effect (no side effects)."""
    return Effect(EffectType.PURE, "pure_operation", func, *args, **kwargs)


def memory_effect(func: Callable[..., T], *args, **kwargs) -> Effect[EffectType, T]:
    """Create a memory effect."""
    return Effect(EffectType.MEMORY, "memory_operation", func, *args, **kwargs)


def telemetry_effect(func: Callable[..., T], *args, **kwargs) -> Effect[EffectType, T]:
    """Create a telemetry effect."""
    return Effect(EffectType.TELEMETRY, "telemetry_operation", func, *args, **kwargs)


# Standard effect type aliases for convenience
IO = EffectType.IO
Database = EffectType.DATABASE
Network = EffectType.NETWORK
State = EffectType.STATE
Random = EffectType.RANDOM
Pure = EffectType.PURE
Memory = EffectType.MEMORY
Security = EffectType.SECURITY
Logging = EffectType.TELEMETRY  # Use TELEMETRY for logging
Telemetry = EffectType.TELEMETRY
Computation = EffectType.COMPUTATION
InformationFlow = EffectType.INFORMATION_FLOW
ContextSwitch = EffectType.CONTEXT_SWITCH
Messaging = EffectType.MESSAGING
StateRead = EffectType.STATE_READ
StateChange = EffectType.STATE_CHANGE
ActorCreation = EffectType.ACTOR_CREATION
ActorLifecycle = EffectType.ACTOR_LIFECYCLE
