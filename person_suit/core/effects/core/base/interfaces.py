"""
Effect System Interfaces
=======================

This module defines the interfaces for the effect system, which provides a way to
represent and handle side effects in a functional style.

The effect system is based on the concept of algebraic effects, where effects are
first-class values that can be passed around, composed, and handled by effect handlers.

Related Files:
- core.py: Core effect system components
- handlers.py: Effect handlers for different effect types
- registry.py: Registry for effect handlers
- types.py: Effect type definitions

Dependencies:
- typing>=4.0.0: For type annotations
- abc: For abstract base classes
"""

from abc import ABC, abstractmethod
from typing import (
    Any,
    Callable,
    Dict,
    Generic,
    Optional,
    Protocol,
    TypeVar,
    runtime_checkable,
)

from .._internal_types.types import (
    EffectContext,
    EffectID,
    EffectState,
    HandlerID,
    RuntimeContext,
    RuntimeMetrics,
)

# Type variables
T = TypeVar("T")  # Result type
E = TypeVar("E")  # Effect type

# ============================================================================
# Effect Result Interface
# ============================================================================


class EffectResultInterface(Generic[T], ABC):
    """Interface for effect results."""

    @property
    @abstractmethod
    def value(self) -> T:
        """
        Get the result value.

        Returns:
            The result value
        """
        pass

    @property
    @abstractmethod
    def metadata(self) -> Dict[str, Any]:
        """
        Get the result metadata.

        Returns:
            The result metadata
        """
        pass


# ============================================================================
# Effect Handler Interface
# ============================================================================


class EffectHandlerInterface(ABC):
    """Interface for effect handlers."""

    @abstractmethod
    def can_handle(self, effect_type: Any) -> bool:
        """
        Check if this handler can handle the given effect type.

        Args:
            effect_type: The effect type to check

        Returns:
            True if this handler can handle the effect type, False otherwise
        """
        pass

    @abstractmethod
    def handle(
        self,
        effect_type: Any,
        operation: str,
        computation: Optional[Callable[..., Any]],
        *args: Any,
        **kwargs: Any,
    ) -> Any:
        """
        Handle an effect.

        Args:
            effect_type: The type of effect to handle
            operation: The operation to perform
            computation: The computation to run
            *args: Arguments to pass to the computation
            **kwargs: Keyword arguments to pass to the computation

        Returns:
            The result of handling the effect
        """
        pass


# ============================================================================
# Specialized Handler Interfaces
# ============================================================================


class IOHandlerInterface(EffectHandlerInterface):
    """Interface for IO effect handlers."""

    pass


class DatabaseHandlerInterface(EffectHandlerInterface):
    """Interface for Database effect handlers."""

    pass


class NetworkHandlerInterface(EffectHandlerInterface):
    """Interface for Network effect handlers."""

    pass


class MemoryHandlerInterface(EffectHandlerInterface):
    """Interface for Memory effect handlers."""

    pass


class TelemetryHandlerInterface(EffectHandlerInterface):
    """Interface for Telemetry effect handlers."""

    pass


class InformationFlowHandlerInterface(EffectHandlerInterface):
    """Interface for Information Flow effect handlers."""

    pass


@runtime_checkable
class Effect(Protocol):
    """Protocol defining the interface for effects."""

    @property
    def effect_id(self) -> EffectID:
        """Get the effect's unique identifier."""
        ...

    @property
    def effect_type(self) -> str:
        """Get the effect's type identifier."""
        ...

    @property
    def state(self) -> EffectState:
        """Get the effect's current state."""
        ...

    @property
    def context(self) -> EffectContext:
        """Get the effect's execution context."""
        ...

    async def execute(self, context: RuntimeContext) -> Any:
        """Execute the effect.

        Args:
            context: Runtime context for execution

        Returns:
            Effect execution result
        """
        ...

    async def cancel(self) -> None:
        """Cancel the effect's execution."""
        ...


class RuntimeInterface(ABC):
    """Interface for the effect system runtime."""

    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the runtime."""
        pass

    @abstractmethod
    async def shutdown(self) -> None:
        """Shutdown the runtime."""
        pass

    @abstractmethod
    async def register_handler(
        self, handler: "HandlerInterface", handler_id: Optional[HandlerID] = None
    ) -> HandlerID:
        """Register a handler with the runtime."""
        pass

    @abstractmethod
    async def execute_effect(
        self, effect: Effect, context: Optional[RuntimeContext] = None
    ) -> Any:
        """Execute an effect."""
        pass

    @abstractmethod
    async def get_metrics(self) -> RuntimeMetrics:
        """Get runtime metrics."""
        pass


class HandlerInterface(ABC):
    """Interface for effect handlers."""

    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the handler."""
        pass

    @abstractmethod
    async def cleanup(self) -> None:
        """Clean up handler resources."""
        pass

    @abstractmethod
    def can_handle(self, effect: Effect) -> bool:
        """Check if handler can handle an effect."""
        pass

    @abstractmethod
    async def analyze_wave_state(
        self, effect: Effect, context: RuntimeContext
    ) -> Dict[str, Any]:
        """Analyze effect in wave state."""
        pass

    @abstractmethod
    async def execute_particle_state(
        self, effect: Effect, context: RuntimeContext
    ) -> Any:
        """Execute effect in particle state."""
        pass


class ContextInterface(ABC):
    """Interface for context objects."""

    @abstractmethod
    def get_data(self) -> Dict[str, Any]:
        """Get context data."""
        pass

    @abstractmethod
    def set_data(self, data: Dict[str, Any]) -> None:
        """Set context data."""
        pass

    @abstractmethod
    def merge(self, other: "ContextInterface") -> "ContextInterface":
        """Merge with another context."""
        pass

    @abstractmethod
    def clone(self) -> "ContextInterface":
        """Create a copy of the context."""
        pass


class MetricsInterface(ABC):
    """Interface for metrics collection."""

    @abstractmethod
    def record_value(self, name: str, value: Any) -> None:
        """Record a metric value."""
        pass

    @abstractmethod
    def get_value(self, name: str) -> Any:
        """Get a metric value."""
        pass

    @abstractmethod
    def get_all(self) -> Dict[str, Any]:
        """Get all metrics."""
        pass

    @abstractmethod
    def reset(self) -> None:
        """Reset all metrics."""

    pass
