"""
Person Suit - Effect Implementation

Provides the concrete implementation of the Effect interface.
"""

from typing import Any, Dict

from ...interfaces import EffectInterface, EffectTypeInterface


class Effect(EffectInterface):
    """
    Concrete implementation of the Effect interface.
    """

    def __init__(
        self,
        effect_type: EffectTypeInterface,
        operation: str,
        parameters: Dict[str, Any],
    ):
        """
        Initialize a new effect.

        Args:
            effect_type: The type of the effect
            operation: The operation to perform
            parameters: The parameters for the operation
        """
        self._effect_type = effect_type
        self._operation = operation
        self._parameters = parameters

    @property
    def effect_type(self) -> EffectTypeInterface:
        """
        Get the type of the effect.

        Returns:
            The effect type
        """
        return self._effect_type

    @property
    def operation(self) -> str:
        """
        Get the operation of the effect.

        Returns:
            The operation name
        """
        return self._operation

    @property
    def parameters(self) -> Dict[str, Any]:
        """
        Get the parameters of the effect.

        Returns:
            The parameters as a dictionary
        """
        return self._parameters
