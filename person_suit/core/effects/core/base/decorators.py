"""
File: decorators.py
Purpose: Effect decorators for the effect system.

This module defines decorators for working with effects, such as the @effects
decorator for specifying the effects of a function, and the @effect_handler
decorator for specifying a function as an effect handler.

Related Files:
- person_suit/core/effects/core.py: Core effect type definitions
- person_suit/core/effects/handlers.py: Effect handlers

Dependencies:
- typing>=4.0.0: For type annotations
- functools: For decorator utilities
"""

import functools
import inspect
from typing import (
    TYPE_CHECKING,
    Any,
    Callable,
    Dict,
    List,
    Optional,
    Set,
    TypeVar,
    cast,
)

from ...interfaces import EffectTypeInterface

if TYPE_CHECKING:
    # Avoid circular import for type hinting
    from .interfaces.context import ContextProtocol

# Type variables
T = TypeVar("T")  # Result type
F = TypeVar("F", bound=Callable[..., Any])  # Function type


def effects(effect_types: List[Any]):
    """
    Decorator to specify the effects of a function.

    This decorator annotates a function with the effects it may have,
    allowing for explicit tracking and handling of computational effects.

    Args:
        effect_types: List of effect types the function may have

    Returns:
        Decorated function

    Example:
        ```python
        @effects([IO, Database])
        def process_data(file_path: str) -> List[Dict[str, Any]]:
            # Read file (IO effect)
            content = perform_effect(IO, "read_file", file_path)

            # Parse data (pure computation)
            parsed_data = parse_data(content)

            # Save to database (Database effect)
            for item in parsed_data:
                perform_effect(Database, "insert", "items", item)

            return parsed_data
        ```
    """

    def decorator(func: F) -> F:
        # Store effect types on the function
        func.__effects__ = set(effect_types)  # type: ignore

        @functools.wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            # Call the original function
            result = func(*args, **kwargs)

            # If result is already an Effect, return it
            if isinstance(result, Effect):
                return result

            # Otherwise, return the result as is
            return result

        return cast(F, wrapper)

    return decorator


def effect_handler(effect_type: Any):
    """
    Decorator to specify a function as an effect handler.

    This decorator annotates a function as an effect handler for a specific
    effect type, allowing it to be registered with the effect handler registry.

    Args:
        effect_type: The effect type this function handles

    Returns:
        Decorated function

    Example:
        ```python
        @effect_handler(IO)
        def handle_read_file(path: str) -> str:
            with open(path, 'r') as f:
                return f.read()
        ```
    """

    def decorator(func: F) -> F:
        # Store handled effect type on the function
        func.__handled_effect__ = effect_type  # type: ignore

        return func

    return decorator


def with_effects(func: F) -> F:
    """
    Decorator to automatically infer the effects of a function.

    This decorator analyzes a function's implementation to infer its effects,
    based on the effects of the functions it calls. It uses AST analysis to
    detect calls to perform_effect and other functions with known effects.

    Args:
        func: The function to decorate

    Returns:
        Decorated function

    Example:
        ```python
        @with_effects
        def process_data(file_path: str) -> List[Dict[str, Any]]:
            # This function's effects will be inferred from its implementation
            content = perform_effect(IO, "read_file", file_path)
            parsed_data = parse_data(content)
            for item in parsed_data:
                perform_effect(Database, "insert", "items", item)
            return parsed_data
        ```
    """
    import ast
    import re

    # Get the source code of the function
    source = inspect.getsource(func)

    # Inferred effects will be stored here
    inferred_effects: Set[Any] = set()

    # Import effect types for reference
    from ._internal_types import (
        IO,
        Computation,
        Database,
        Logging,
        Network,
        Pure,
        Random,
        Security,
        State,
        Telemetry,
    )

    # Map of common patterns to effect types
    pattern_to_effect = {
        # IO patterns
        r"open\(": IO,
        r"read\(": IO,
        r"write\(": IO,
        r"file\(": IO,
        r"\.read\(": IO,
        r"\.write\(": IO,
        r"os\.": IO,
        r"shutil\.": IO,
        r"pathlib\.": IO,
        r"Path\(": IO,
        # Database patterns
        r"cursor\.": Database,
        r"connection\.": Database,
        r"execute\(": Database,
        r"query\(": Database,
        r"select\(": Database,
        r"insert\(": Database,
        r"update\(": Database,
        r"delete\(": Database,
        r"commit\(": Database,
        r"rollback\(": Database,
        r"SQLAlchemy": Database,
        r"Model\.": Database,
        r"session\.": Database,
        # Network patterns
        r"requests\.": Network,
        r"urllib\.": Network,
        r"http": Network,
        r"socket\.": Network,
        r"aiohttp\.": Network,
        r"websocket": Network,
        r"fetch": Network,
        r"download": Network,
        r"upload": Network,
        # State patterns
        r"state\.": State,
        r"store\.": State,
        r"context\.": State,
        r"global": State,
        # Random patterns
        r"random\.": Random,
        r"randint": Random,
        r"choice\(": Random,
        r"sample\(": Random,
        r"shuffle\(": Random,
        r"uuid": Random,
        # Security patterns
        r"encrypt": Security,
        r"decrypt": Security,
        r"hash": Security,
        r"password": Security,
        r"auth": Security,
        r"token": Security,
        r"permission": Security,
        r"cryptography": Security,
        # Logging patterns
        r"log\.": Logging,
        r"logger\.": Logging,
        r"logging\.": Logging,
        r"debug\(": Logging,
        r"info\(": Logging,
        r"warning\(": Logging,
        r"error\(": Logging,
        r"critical\(": Logging,
        # Telemetry patterns
        r"metric": Telemetry,
        r"trace": Telemetry,
        r"span": Telemetry,
        r"monitor": Telemetry,
        r"measure": Telemetry,
        r"counter": Telemetry,
        r"gauge": Telemetry,
        r"histogram": Telemetry,
        # Computation patterns
        r"parallel": Computation,
        r"concurrent": Computation,
        r"thread": Computation,
        r"process": Computation,
        r"async": Computation,
        r"await": Computation,
        r"gpu": Computation,
        r"cuda": Computation,
        r"tensor": Computation,
    }

    # Cache of known function effects
    known_function_effects: Dict[str, Set[Any]] = {}

    # AST visitor to find effect operations
    class EffectVisitor(ast.NodeVisitor):
        def __init__(self):
            self.effects: Set[Any] = set()
            self.imported_modules: Dict[str, str] = {}
            self.imported_names: Dict[str, Any] = {}

            # Add effect types to imported names
            self.imported_names["IO"] = IO
            self.imported_names["Database"] = Database
            self.imported_names["Network"] = Network
            self.imported_names["State"] = State
            self.imported_names["Random"] = Random
            self.imported_names["Pure"] = Pure
            self.imported_names["Security"] = Security
            self.imported_names["Logging"] = Logging
            self.imported_names["Telemetry"] = Telemetry
            self.imported_names["Computation"] = Computation

        def visit_Import(self, node: ast.Import) -> None:
            """Process import statements."""
            for name in node.names:
                self.imported_modules[name.asname or name.name] = name.name
            self.generic_visit(node)

        def visit_ImportFrom(self, node: ast.ImportFrom) -> None:
            """Process from ... import statements."""
            module = node.module or ""
            for name in node.names:
                imported_name = name.asname or name.name
                self.imported_names[imported_name] = f"{module}.{name.name}"
            self.generic_visit(node)

        def visit_Call(self, node: ast.Call) -> None:
            """Process function calls."""
            # Check for perform_effect calls
            if isinstance(node.func, ast.Name) and node.func.id == "perform_effect":
                if len(node.args) >= 2:
                    effect_arg = node.args[0]
                    if isinstance(effect_arg, ast.Name):
                        effect_name = effect_arg.id
                        if effect_name in self.imported_names:
                            effect = self.imported_names[effect_name]
                            self.effects.add(effect)

            # Check for calls to functions with known effects
            elif isinstance(node.func, ast.Name):
                func_name = node.func.id
                if func_name in known_function_effects:
                    self.effects.update(known_function_effects[func_name])

            # Check for calls to methods with known effects
            elif isinstance(node.func, ast.Attribute) and isinstance(
                node.func.value, ast.Name
            ):
                obj_name = node.func.value.id
                method_name = node.func.attr
                full_name = f"{obj_name}.{method_name}"
                if full_name in known_function_effects:
                    self.effects.update(known_function_effects[full_name])

            self.generic_visit(node)

        def visit_Attribute(self, node: ast.Attribute) -> None:
            """Process attribute access."""
            # This helps detect patterns like 'file.write()'
            attr_source = ast.unparse(node)
            for pattern, effect in pattern_to_effect.items():
                if re.search(pattern, attr_source):
                    self.effects.add(effect)
            self.generic_visit(node)

    try:
        # Parse the source code into an AST
        tree = ast.parse(source)

        # Visit the AST to find effects
        visitor = EffectVisitor()
        visitor.visit(tree)

        # Add inferred effects
        inferred_effects.update(visitor.effects)

        # Also check the source code for patterns that imply effects
        for pattern, effect in pattern_to_effect.items():
            if re.search(pattern, source):
                inferred_effects.add(effect)

        # If no effects were inferred, assume Pure
        if not inferred_effects:
            inferred_effects.add(Pure)

    except (SyntaxError, TypeError, ValueError) as e:
        # If we can't parse the source, log a warning and assume no effects
        import logging

        logger = logging.getLogger(__name__)
        logger.warning(f"Could not infer effects for {func.__name__}: {e}")
        inferred_effects.add(Pure)  # Assume Pure if we can't infer effects

    # Store inferred effects on the function
    func.__effects__ = inferred_effects  # type: ignore

    # Store in known function effects for future inference
    known_function_effects[func.__name__] = inferred_effects

    @functools.wraps(func)
    def wrapper(*args: Any, **kwargs: Any) -> Any:
        # Call the original function
        result = func(*args, **kwargs)

        # If result is already an Effect, return it
        if isinstance(result, Effect):
            return result

        # Otherwise, return the result as is
        return result

    return cast(F, wrapper)


def context_effects(
    effect_types: List[EffectTypeInterface],
    context_provider: Optional[Callable[..., "ContextProtocol"]] = None,
    track_chain: bool = False,
    track_transitions: bool = False,
    context_param: str = "context",
    wave_amplitude: float = 1.0,
    wave_phase: float = 0.0,
    wave_function: Optional[Any] = None,  # Use WaveFunction placeholder/real type
):
    """
    Decorator for tracking effects in a specific context with wave properties.

    Tracks specified effect types using the ContextualEffectTracker, associating
    them with context provided either by `context_provider` or a function argument.
    Optionally tracks effect chains and transitions within that context.

    Args:
        effect_types: The types of effects to track (e.g., [IO, Database]).
        context_provider: Optional function (sync or async) that takes the
                          decorated function's arguments and returns the context.
        track_chain: If True, track the sequence of effects as a chain.
        track_transitions: If True, track transitions between consecutive effects.
        context_param: Name of the parameter in the decorated function's
                       signature that holds the context (used if context_provider
                       is None). Defaults to 'context'.
        wave_amplitude: Default amplitude for the tracked ContextualEffect.
        wave_phase: Default phase for the tracked ContextualEffect.
        wave_function: Optional specific WaveFunction for the tracked effect.

    Returns:
        Decorator function.
    """
    # Import necessary components locally to avoid top-level circular imports
    # if this decorator needs access to the tracker singleton immediately.
    import logging

    from .contextual_tracker import get_contextual_effect_tracker

    logger = logging.getLogger(__name__)

    def decorator(func: F) -> F:
        # --- Helper to get context ---
        async def _get_context_async(
            *args: Any, **kwargs: Any
        ) -> Optional["ContextProtocol"]:
            ctx = None
            if context_provider:
                if inspect.iscoroutinefunction(context_provider):
                    ctx = await context_provider(*args, **kwargs)
                else:
                    ctx = context_provider(*args, **kwargs)
            elif context_param in kwargs:
                ctx = kwargs[context_param]
            # Could also check args by position if needed, but kwarg is safer
            if ctx and not hasattr(
                ctx, "domain"
            ):  # Basic check for Context-like object
                logger.warning(
                    f"Context object for {func.__name__} lacks 'domain' attribute."
                )
            return ctx

        def _get_context_sync(*args: Any, **kwargs: Any) -> Optional["ContextProtocol"]:
            ctx = None
            if context_provider:
                if inspect.iscoroutinefunction(context_provider):
                    # Cannot call async provider from sync func easily
                    logger.error(
                        f"Cannot use async context_provider with sync function {func.__name__}"
                    )
                    return None
                else:
                    ctx = context_provider(*args, **kwargs)
            elif context_param in kwargs:
                ctx = kwargs[context_param]
            if ctx and not hasattr(ctx, "domain"):
                logger.warning(
                    f"Context object for {func.__name__} lacks 'domain' attribute."
                )
            return ctx

        # --- Helper to track effects ---
        def _track_effects(tracker, source, context, tracked_effects):
            context_key = (
                context.domain if context and hasattr(context, "domain") else "default"
            )
            if track_chain:
                tracker.track_effect_chain(tracked_effects, context_key)
            if track_transitions and len(tracked_effects) >= 2:
                for i in range(len(tracked_effects) - 1):
                    tracker.track_effect_transition(
                        tracked_effects[i], tracked_effects[i + 1]
                    )
            for effect in tracked_effects:
                logger.debug(f"Completed effect: {effect}")

        # --- Async Wrapper ---
        @functools.wraps(func)
        async def async_wrapper(*args: Any, **kwargs: Any) -> Any:
            context = await _get_context_async(*args, **kwargs)
            source = f"{func.__module__}.{func.__qualname__}"
            tracker = get_contextual_effect_tracker()
            tracked_effects = []

            for effect_type in effect_types:
                try:
                    effect = tracker.track_effect_in_context(
                        effect_type=effect_type,
                        source=source,
                        context=context,
                        wave_amplitude=wave_amplitude,
                        wave_phase=wave_phase,
                        wave_function=wave_function,  # Pass the actual wave function object
                    )
                    tracked_effects.append(effect)
                except Exception as e:
                    logger.error(
                        f"Error tracking effect {effect_type} for {source}: {e}",
                        exc_info=True,
                    )

            try:
                # Execute the original async function
                result = await func(*args, **kwargs)
                return result
            finally:
                # Track chains/transitions and log completion after execution
                _track_effects(tracker, source, context, tracked_effects)

        # --- Sync Wrapper ---
        @functools.wraps(func)
        def sync_wrapper(*args: Any, **kwargs: Any) -> Any:
            context = _get_context_sync(*args, **kwargs)
            source = f"{func.__module__}.{func.__qualname__}"
            tracker = get_contextual_effect_tracker()
            tracked_effects = []

            for effect_type in effect_types:
                try:
                    effect = tracker.track_effect_in_context(
                        effect_type=effect_type,
                        source=source,
                        context=context,
                        wave_amplitude=wave_amplitude,
                        wave_phase=wave_phase,
                        wave_function=wave_function,  # Pass the actual wave function object
                    )
                    tracked_effects.append(effect)
                except Exception as e:
                    logger.error(
                        f"Error tracking effect {effect_type} for {source}: {e}",
                        exc_info=True,
                    )

            try:
                # Execute the original sync function
                result = func(*args, **kwargs)
                return result
            finally:
                # Track chains/transitions and log completion after execution
                _track_effects(tracker, source, context, tracked_effects)

        # Return the appropriate wrapper
        if inspect.iscoroutinefunction(func):
            return cast(F, async_wrapper)
        else:
            return cast(F, sync_wrapper)

    return decorator


def wave_effects(*args, **kwargs):
    """Placeholder for wave_effects decorator (not yet implemented)."""
    raise NotImplementedError("wave_effects is not yet implemented.")


__all__ = [
    "effect",
    "with_effects",
    "context_effects",
    "wave_effects",
]

# Ensure TYPE_CHECKING is imported for the hints if not already present
from typing import TYPE_CHECKING

effect = effects
