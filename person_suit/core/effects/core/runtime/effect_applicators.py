"""
File: effect_applicators.py
Purpose: Provides functions to apply specific state manipulation effects.

This module contains standalone functions that take a specific effect and the
current particle state, apply the change immutably, and return the potentially
modified new particle state. These are used by the StateTransformationLogic
and the dynamics simulation.

Related Files:
- person_suit/core/effects/transformation_logic.py
- person_suit/core/effects/dynamics_simulation.py
- person_suit/core/caw/schemas.py
- person_suit/core/caw/particle_state.py
"""

import logging
from typing import Any, Dict, Optional

from ....caw.schemas import (  # Assuming InfonFlavor is in schemas
    BaseEdge,
    BaseEffect,
    BaseNode,
    ConcreteParticleState,
    EdgeID,
    FlexMetadata,
    InfonFlavor,
    NodeID,
)

logger = logging.getLogger(__name__)


async def apply_metadata_update(
    effect: BaseEffect, p_state: ConcreteParticleState, node_update: bool
) -> Optional[ConcreteParticleState]:
    """
    Applies metadata update using immutable methods of ParticleState.
    Returns the new state if changed, None otherwise.
    Handles special keys like 'intended_new_flavor' and 'intended_new_mass'
    which trigger node recreation.
    """
    params = effect.parameters
    entity_id = params.get("node_id" if node_update else "edge_id")
    metadata_update = params.get("metadata_update")
    if not entity_id or not metadata_update or not isinstance(metadata_update, dict):
        logging.warning(
            f"Missing or invalid parameters for {effect.effect_type}: {params}"
        )
        return None

    try:
        if node_update:
            target_node_id = NodeID(entity_id)
            node = p_state.get_node(target_node_id)
            if not node:
                logging.warning(
                    f"Target node {target_node_id} not found for {effect.effect_type}."
                )
                return None

            # Handle special keys requiring recreation
            intended_flavor_str = metadata_update.get("intended_new_flavor")
            intended_mass = metadata_update.get("intended_new_mass")
            actual_update = metadata_update.copy()
            recreate_node = False
            new_node_args: Dict[str, Any] = {}

            if intended_flavor_str:
                try:
                    new_flavor = InfonFlavor[intended_flavor_str]
                    recreate_node = True
                    new_node_args["flavor"] = new_flavor
                    del actual_update["intended_new_flavor"]
                except KeyError:
                    logging.error(
                        f"Invalid 'intended_new_flavor': {intended_flavor_str}"
                    )
                    return None  # Fail if flavor invalid
            if intended_mass is not None:
                try:
                    new_mass_val = float(intended_mass)
                    recreate_node = True
                    new_node_args["computational_mass"] = new_mass_val
                    del actual_update["intended_new_mass"]
                except (ValueError, TypeError):
                    logging.error(f"Invalid 'intended_new_mass': {intended_mass}")
                    return None  # Fail if mass invalid

            if recreate_node:
                # Check if effective update is needed before recreating
                current_meta = node.metadata
                merged_meta = {**current_meta, **actual_update}
                current_attrs = {
                    k: getattr(node, k)
                    for k in new_node_args.keys()
                    if hasattr(node, k)
                }
                if (
                    new_node_args == current_attrs
                    and FlexMetadata(merged_meta) == current_meta
                ):
                    logging.debug(
                        f"Recreation update for node {target_node_id} resulted in no effective change."
                    )
                    return None  # No change

                new_state = p_state.recreate_node(
                    node_id=target_node_id,
                    new_attributes=new_node_args,
                    metadata_update=FlexMetadata(actual_update),
                )
                logging.debug(
                    f"Recreated node {target_node_id} with new attributes/metadata."
                )
            else:
                # Standard metadata update - check if needed
                current_meta = node.metadata
                if all(current_meta.get(k) == v for k, v in actual_update.items()):
                    logging.debug(
                        f"Metadata update for node {target_node_id} resulted in no change."
                    )
                    return None  # No change

                new_state = p_state.update_node_metadata(
                    node_id=target_node_id,
                    new_metadata=FlexMetadata({**current_meta, **actual_update}),
                )
                logging.debug(f"Updated node metadata for {target_node_id}.")

        else:  # Edge update
            target_edge_id = EdgeID(entity_id)
            edge = p_state.get_edge(target_edge_id)
            if not edge:
                logging.warning(
                    f"Target edge {target_edge_id} not found for {effect.effect_type}."
                )
                return None

            current_meta = edge.metadata
            if all(current_meta.get(k) == v for k, v in metadata_update.items()):
                logging.debug(
                    f"Metadata update for edge {target_edge_id} resulted in no change."
                )
                return None  # No change

            new_state = p_state.update_edge_metadata(
                edge_id=target_edge_id,
                new_metadata=FlexMetadata({**current_meta, **metadata_update}),
            )
            logging.debug(f"Updated edge metadata for {target_edge_id}.")

        # Return new state only if the instance actually changed
        return new_state if new_state is not p_state else None

    except Exception as e:
        logging.exception(f"Error applying metadata update for {entity_id}: {e}")
        return None


async def apply_remove_node(
    effect: BaseEffect, p_state: ConcreteParticleState
) -> Optional[ConcreteParticleState]:
    """Removes a node using immutable methods. Returns new state if changed, None otherwise."""
    params = effect.parameters
    node_id_str = params.get("node_id")
    if not node_id_str:
        logging.warning(f"Missing node_id for REMOVE_NODE_EFFECT: {params}")
        return None
    node_id = NodeID(node_id_str)
    try:
        if not p_state.get_node(node_id):
            logging.warning(f"Node {node_id} not found for removal.")
            return None  # No change if node doesn't exist

        new_state = p_state.remove_node(node_id)
        logging.debug(f"Removed node {node_id}.")
        return new_state if new_state is not p_state else None
    except Exception as e:
        logging.exception(f"Error removing node {node_id}: {e}")
        return None


async def apply_add_node(
    effect: BaseEffect, p_state: ConcreteParticleState
) -> Optional[ConcreteParticleState]:
    """Adds a node using immutable methods. Returns new state if changed, None otherwise."""
    params = effect.parameters
    node_to_add = params.get("node")
    if not isinstance(node_to_add, BaseNode):
        logging.warning(
            f"Missing or invalid 'node' parameter for ADD_NODE_EFFECT: {params}"
        )
        return None
    try:
        if p_state.get_node(node_to_add.node_id):
            logging.warning(f"Node {node_to_add.node_id} already exists. Cannot add.")
            return None  # No change if node exists

        new_state = p_state.add_node(node_to_add)
        logging.debug(f"Added node {node_to_add.node_id}.")
        return new_state if new_state is not p_state else None
    except (ValueError, TypeError, KeyError) as e:
        logging.exception(
            f"Error adding node {getattr(node_to_add, 'node_id', 'N/A')}: {e}"
        )
        return None


async def apply_add_edge(
    effect: BaseEffect, p_state: ConcreteParticleState
) -> Optional[ConcreteParticleState]:
    """Adds an edge using immutable methods. Returns new state if changed, None otherwise."""
    params = effect.parameters
    edge_to_add = params.get("edge")
    if not isinstance(edge_to_add, BaseEdge):
        logging.warning(
            f"Missing or invalid 'edge' parameter for ADD_EDGE_EFFECT: {params}"
        )
        return None
    try:
        if p_state.get_edge(edge_to_add.edge_id):
            logging.warning(f"Edge {edge_to_add.edge_id} already exists. Cannot add.")
            return None  # No change if edge exists

        for node_id in edge_to_add.connected_nodes:
            if not p_state.get_node(node_id):
                logging.warning(
                    f"Cannot add edge {edge_to_add.edge_id}: Connected node {node_id} does not exist."
                )
                return None  # Fail if node missing

        new_state = p_state.add_edge(edge_to_add)
        logging.debug(f"Added edge {edge_to_add.edge_id}.")
        return new_state if new_state is not p_state else None
    except (ValueError, TypeError, KeyError) as e:
        logging.exception(
            f"Error adding edge {getattr(edge_to_add, 'edge_id', 'N/A')}: {e}"
        )
        return None


async def apply_remove_edge(
    effect: BaseEffect, p_state: ConcreteParticleState
) -> Optional[ConcreteParticleState]:
    """Removes an edge using immutable methods. Returns new state if changed, None otherwise."""
    params = effect.parameters
    edge_id_str = params.get("edge_id")
    if not edge_id_str:
        logging.warning(f"Missing edge_id for REMOVE_EDGE_EFFECT: {params}")
        return None
    edge_id = EdgeID(edge_id_str)
    try:
        if not p_state.get_edge(edge_id):
            logging.warning(f"Edge {edge_id} not found for removal.")
            return None  # No change if edge doesn't exist

        new_state = p_state.remove_edge(edge_id)
        logging.debug(f"Removed edge {edge_id}.")
        return new_state if new_state is not p_state else None
    except Exception as e:
        logging.exception(f"Error removing edge {edge_id}: {e}")
        return None
