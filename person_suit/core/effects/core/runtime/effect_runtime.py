"""
File: effect_runtime.py
Purpose: Effect runtime system for managing and executing effects.

This module provides the runtime environment for executing effects in the system.
It manages effect handlers, effect execution, and runtime context.

Related Files:
- person_suit/core/effects/registry.py: Effect handler registry
- person_suit/core/effects/handlers.py: Effect handlers
- person_suit/core/effects/types.py: Effect type definitions

Dependencies:
- typing>=4.0.0: For type annotations
- threading: For thread safety
"""

import logging
import threading
from typing import Any, Dict, Optional

from ...interfaces import (
    EffectHandlerInterface,
    EffectInterface,
)
from ...registry import global_effect_registry

logger = logging.getLogger(__name__)


class EffectRuntime:
    """
    Runtime environment for executing effects.

    This class manages the execution of effects, including:
    - Effect handler registration and lookup
    - Effect execution and result handling
    - Runtime context management
    - Error handling and recovery
    """

    def __init__(self):
        """Initialize the effect runtime."""
        self._registry = global_effect_registry
        self._context: Dict[str, Any] = {}
        self._lock = threading.Lock()

    def execute_effect(
        self, effect: EffectInterface, context: Optional[Dict[str, Any]] = None
    ) -> Any:
        """
        Execute an effect with the given context.

        Args:
            effect: The effect to execute
            context: Optional execution context

        Returns:
            The result of executing the effect

        Raises:
            RuntimeError: If no handler is found for the effect
        """
        if context is not None:
            self._context.update(context)

        handler = self._registry.get_handler_for_effect(effect)
        if handler is None:
            raise RuntimeError(f"No handler found for effect: {effect}")

        try:
            with self._lock:
                result = handler.handle(effect, self._context)
            return result
        except Exception as e:
            logger.error(f"Error executing effect {effect}: {e}")
            raise

    def register_handler(self, handler: EffectHandlerInterface) -> None:
        """
        Register an effect handler.

        Args:
            handler: The handler to register
        """
        self._registry.register_handler(handler)

    def unregister_handler(self, handler: EffectHandlerInterface) -> None:
        """
        Unregister an effect handler.

        Args:
            handler: The handler to unregister
        """
        self._registry.unregister_handler(handler)

    def get_context(self) -> Dict[str, Any]:
        """
        Get the current runtime context.

        Returns:
            The current context dictionary
        """
        return self._context.copy()

    def set_context(self, context: Dict[str, Any]) -> None:
        """
        Set the runtime context.

        Args:
            context: The new context dictionary
        """
        with self._lock:
            self._context = context.copy()

    def update_context(self, context: Dict[str, Any]) -> None:
        """
        Update the runtime context.

        Args:
            context: Dictionary of context values to update
        """
        with self._lock:
            self._context.update(context)


# Global runtime instance
_runtime = EffectRuntime()


def get_runtime() -> EffectRuntime:
    """
    Get the global effect runtime instance.

    Returns:
        The global EffectRuntime instance
    """
    return _runtime
