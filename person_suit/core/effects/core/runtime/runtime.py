"""Effect System Runtime.

This module provides the core runtime functionality for the Effect System,
combining both general runtime operations and effect-specific runtime handling.
It follows the wave-particle duality pattern for managing effects and their
execution.

The runtime supports:
- Effect registration and discovery
- Effect execution and lifecycle management
- Resource management and cleanup
- Monitoring and metrics collection
- Error handling and recovery

Related Files:
    - core/base/interfaces.py: Core interfaces
    - core/types/types.py: Type definitions
    - handlers/core/context.py: Context management
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional
from uuid import uuid4

from ...core.base.handler import EffectHandler
from ...core.base.interfaces import Effect, RuntimeContext, RuntimeMetrics
from ...core._internal_types import EffectID, HandlerID, RuntimeState

logger = logging.getLogger(__name__)


class EffectRuntime:
    """Core runtime for effect management and execution.

    The runtime maintains both wave and particle states:
    - Wave state: Represents potential effect executions and resource requirements
    - Particle state: Represents concrete effect instances and their execution
    """

    def __init__(self):
        """Initialize the runtime."""
        self._handlers: Dict[<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>] = {}
        self._active_effects: Dict[EffectID, Effect] = {}
        self._metrics: RuntimeMetrics = RuntimeMetrics()
        self._state: RuntimeState = RuntimeState.INITIALIZING

    async def initialize(self) -> None:
        """Initialize the runtime and its components."""
        try:
            # Initialize handlers
            await self._initialize_handlers()
            # Initialize metrics
            await self._initialize_metrics()
            # Set runtime state
            self._state = RuntimeState.RUNNING
            logger.info("Runtime initialized successfully")
        except Exception as e:
            self._state = RuntimeState.ERROR
            logger.error(f"Runtime initialization failed: {e}")
            raise

    async def shutdown(self) -> None:
        """Shutdown the runtime and cleanup resources."""
        try:
            # Set state to shutting down
            self._state = RuntimeState.SHUTTING_DOWN
            # Cancel active effects
            await self._cancel_active_effects()
            # Cleanup handlers
            await self._cleanup_handlers()
            # Final state
            self._state = RuntimeState.STOPPED
            logger.info("Runtime shutdown completed")
        except Exception as e:
            self._state = RuntimeState.ERROR
            logger.error(f"Runtime shutdown failed: {e}")
            raise

    async def register_handler(
        self, handler: EffectHandler, handler_id: Optional[HandlerID] = None
    ) -> HandlerID:
        """Register an effect handler with the runtime.

        Args:
            handler: The handler to register
            handler_id: Optional ID for the handler

        Returns:
            The handler's ID
        """
        handler_id = handler_id or HandlerID(uuid4())
        self._handlers[handler_id] = handler
        await handler.initialize()
        return handler_id

    async def execute_effect(
        self, effect: Effect, context: Optional[RuntimeContext] = None
    ) -> Any:
        """Execute an effect through appropriate handlers.

        Args:
            effect: The effect to execute
            context: Optional runtime context

        Returns:
            Effect execution result
        """
        effect_id = EffectID(uuid4())
        self._active_effects[effect_id] = effect

        try:
            # Find compatible handlers
            handlers = self._find_compatible_handlers(effect)
            if not handlers:
                raise ValueError(f"No compatible handlers for effect: {effect}")

            # Execute through handlers
            result = await self._execute_through_handlers(effect, handlers, context)

            # Update metrics
            await self._update_execution_metrics(effect_id, True)

            return result
        except Exception:
            await self._update_execution_metrics(effect_id, False)
            raise
        finally:
            del self._active_effects[effect_id]

    async def _initialize_handlers(self) -> None:
        """Initialize registered handlers."""
        for handler in self._handlers.values():
            await handler.initialize()

    async def _initialize_metrics(self) -> None:
        """Initialize runtime metrics."""
        self._metrics = RuntimeMetrics()

    async def _cancel_active_effects(self) -> None:
        """Cancel all active effects."""
        for effect_id, effect in self._active_effects.items():
            try:
                await effect.cancel()
            except Exception as e:
                logger.error(f"Failed to cancel effect {effect_id}: {e}")

    async def _cleanup_handlers(self) -> None:
        """Cleanup all registered handlers."""
        for handler in self._handlers.values():
            try:
                await handler.cleanup()
            except Exception as e:
                logger.error(f"Handler cleanup failed: {e}")

    def _find_compatible_handlers(self, effect: Effect) -> List[EffectHandler]:
        """Find handlers compatible with an effect."""
        return [
            handler for handler in self._handlers.values() if handler.can_handle(effect)
        ]

    async def _execute_through_handlers(
        self,
        effect: Effect,
        handlers: List[EffectHandler],
        context: Optional[RuntimeContext],
    ) -> Any:
        """Execute effect through compatible handlers."""
        context = context or RuntimeContext()

        # Execute wave operations first
        wave_results = await asyncio.gather(
            *[handler.analyze_wave_state(effect, context) for handler in handlers]
        )

        # Select best handler based on wave analysis
        selected_handler = self._select_handler(handlers, wave_results)

        # Execute particle operation
        return await selected_handler.execute_particle_state(effect, context)

    def _select_handler(
        self, handlers: List[EffectHandler], wave_results: List[Any]
    ) -> EffectHandler:
        """Select best handler based on wave analysis results."""
        # Simple selection - choose first handler
        # Could be enhanced with more sophisticated selection logic
        return handlers[0]

    async def _update_execution_metrics(
        self, effect_id: EffectID, success: bool
    ) -> None:
        """Update metrics for effect execution."""
        if success:
            self._metrics.successful_executions += 1
        else:
            self._metrics.failed_executions += 1


# Context-aware singleton factory for EffectRuntime
_runtime_instance: Optional[EffectRuntime] = None


def get_runtime() -> EffectRuntime:
    global _runtime_instance
    if _runtime_instance is None:
        _runtime_instance = EffectRuntime()
    return _runtime_instance
