"""
File: dynamics_simulation.py
Purpose: Encapsulates the logic for simulating CAW dynamics.

This module contains the function responsible for executing a single step or multiple
steps of the CAW dynamics simulation, including:
- Determining Adaptive Computational Fidelity (ACF) parameters.
- Calculating wave state from particle state.
- Applying wave potential effects back onto the particle state.
- Calculating particle-particle interaction effects (force analogs).
- Applying the combined generated effects to update the particle state.

Related Files:
- person_suit/core/effects/transformation_logic.py (Calls this simulation)
- person_suit/core/caw/dynamics/* (Implements specific calculations)
- person_suit/core/caw/adaptive/* (ACF logic and monitoring)
- person_suit/core/caw/schemas.py
"""

import logging
from copy import replace
from typing import Any, Dict, List, Optional, Tuple

# Import ACF and Resource Monitoring
from ....caw.acf_logic import ACFManager

# Import dynamics functions
from ....caw.force_analogs import (
    calculate_em_force_effect,
    calculate_gravity_force_effect,
    calculate_higgs_field_effect,
    calculate_weak_force_effect,
)
from ....caw.particle_state import ConcreteParticleState
from ....caw.resource_monitor import ResourceMonitor
from ....caw.schemas import (
    ACFParams,
    BaseEffect,
    Context,
    EffectType,
    NodeID,
)
from ....caw.wave_function import calculate_wave_state_from_particle
from ....caw.wave_particle_interaction import (
    apply_wave_potential_to_particle,
)
from ....caw.wave_state import ConcreteWaveState

# Import basic effect application helpers (needs to be defined/imported)
# These would contain the logic previously in StateTransformationLogic._apply_*
# For now, assume they exist in an 'applicators' module or similar.
from .effect_applicators import (  # ... other basic applicators if needed
    apply_add_edge,
    apply_add_node,
    apply_metadata_update,
    apply_remove_edge,
    apply_remove_node,
)

logger = logging.getLogger(__name__)


async def simulate_caw_dynamics(
    initial_particle_state: ConcreteParticleState,
    initial_wave_state: Optional[ConcreteWaveState],  # Pass initial wave state
    context: Context,
    acf_manager: ACFManager,
    resource_monitor: ResourceMonitor,
    # transformation_logic: Any # Pass instance if helpers are methods
) -> Tuple[ConcreteParticleState, Optional[ConcreteWaveState], bool]:
    """
    Runs the CAW dynamics simulation steps based on context and ACF.

    Args:
        initial_particle_state: The starting particle state for the simulation.
        initial_wave_state: The starting wave state (used if no changes happen).
        context: The context driving the simulation (contains ACF settings).
        acf_manager: Instance to interpret ACF settings.
        resource_monitor: Instance to get dynamic ACF parameters.
        # transformation_logic: Instance containing basic effect applicators if needed.

    Returns:
        A tuple containing:
        - final_particle_state: The particle state after simulation.
        - final_wave_state: The calculated wave state based on the final particle state.
        - particle_state_changed: Boolean indicating if the particle state was modified.
    """
    particle_state_changed_overall = False
    sim_particle_state = initial_particle_state.copy()  # Start with a copy

    try:
        # --- Dynamic ACF Adjustment --- #
        dynamic_acf_params: ACFParams = resource_monitor.get_current_acf_params(
            context.acf_setting
        )
        logging.debug(
            f"Dynamics using ACF params determined by resource monitor: {dynamic_acf_params}"
        )
        interpreted_acf_params = acf_manager.interpret_acf_params(dynamic_acf_params)
        # --- End Dynamic ACF --- #

        max_steps = interpreted_acf_params.get("max_simulation_steps", 1)
        logging.info(f"Starting dynamics simulation for {max_steps} steps.")

        for step in range(max_steps):
            logging.debug(f"Dynamics Step {step + 1}/{max_steps}")
            current_step_particle_state = (
                sim_particle_state  # State at the beginning of this step
            )
            step_particle_state_changed_this_step = (
                False  # Track changes *within* this step
            )

            # 1. Particle -> Wave Calculation
            temp_context_for_wave = replace(context, acf_setting=dynamic_acf_params)
            wave_calc_result = await calculate_wave_state_from_particle(
                current_step_particle_state,  # Use state at start of step
                temp_context_for_wave,
            )

            step_wave_state: Optional[ConcreteWaveState] = None
            node_to_index: Optional[Dict[NodeID, int]] = None
            if wave_calc_result:
                step_wave_state, node_to_index = wave_calc_result
                if step_wave_state and node_to_index:
                    logging.debug(
                        f"Step {step + 1}: Calculated wave state (Tensor: {step_wave_state.tensor.shape}) and node_to_index map ({len(node_to_index)} entries)."
                    )
                else:
                    logging.warning(
                        f"Step {step + 1}: Wave state calculation returned None or empty map, skipping wave interactions."
                    )
                    step_wave_state = None  # Ensure it's None
                    node_to_index = None
            else:
                logging.warning(
                    f"Step {step + 1}: Wave state calculation suppressed or failed, skipping wave interactions."
                )
                step_wave_state = None  # Ensure it's None
                node_to_index = None

            # 2. Wave -> Particle Effects (Only if wave state and map are valid)
            wave_effects: List[BaseEffect] = []
            if step_wave_state and node_to_index:
                wave_effects = await apply_wave_potential_to_particle(
                    step_wave_state,
                    current_step_particle_state,  # Apply to state at start of step
                    context,  # Original context for non-ACF info
                    interpreted_acf_params,
                    node_to_index,
                )
            else:
                logging.debug(
                    f"Step {step + 1}: Skipping Wave->Particle effects due to missing wave state or index map."
                )

            # 3. Particle -> Particle Effects
            all_node_ids = list(current_step_particle_state.nodes.keys())
            if not all_node_ids:
                logging.debug(
                    f"Step {step + 1}: Skipping Particle->Particle effects as there are no nodes."
                )
                particle_effects: List[BaseEffect] = []
            else:
                particle_effects = await calculate_particle_interaction_effects(
                    current_step_particle_state,  # Calculate based on state at start of step
                    all_node_ids,
                    context,  # Original context for non-ACF info
                    interpreted_acf_params,
                )

            # 4. Combine and Apply Effects
            combined_effects = wave_effects + particle_effects
            if not combined_effects:
                logging.debug(f"Step {step + 1}: No dynamics effects generated.")
                continue  # Move to next step

            logging.debug(
                f"Step {step + 1}: Applying {len(combined_effects)} generated dynamics effects."
            )
            working_particle_state_in_step = (
                current_step_particle_state  # State being modified within this step
            )

            for sub_effect in combined_effects:
                # Ensure category if needed (might depend on applicator implementation)
                if not hasattr(sub_effect, "category"):
                    # This assumes BaseEffect is mutable or applicators handle it.
                    # Ideally, effect creation assigns category. Patching here is risky.
                    # setattr(sub_effect, 'category', EffectCategory.STATE_MANIPULATION)
                    logging.warning(
                        f"Sub-effect {sub_effect.effect_type} missing category during dynamics."
                    )

                # Apply simple effects directly using imported helpers
                new_state_after_sub_effect: Optional[ConcreteParticleState] = None
                try:
                    # --- Dispatch to Applicators --- #
                    if sub_effect.effect_type == EffectType.UPDATE_NODE_METADATA:
                        new_state_after_sub_effect = await apply_metadata_update(
                            sub_effect, working_particle_state_in_step, node_update=True
                        )
                    elif sub_effect.effect_type == EffectType.UPDATE_EDGE_METADATA:
                        new_state_after_sub_effect = await apply_metadata_update(
                            sub_effect,
                            working_particle_state_in_step,
                            node_update=False,
                        )
                    elif sub_effect.effect_type == EffectType.REMOVE_NODE:
                        new_state_after_sub_effect = await apply_remove_node(
                            sub_effect, working_particle_state_in_step
                        )
                    elif sub_effect.effect_type == EffectType.ADD_NODE:
                        new_state_after_sub_effect = await apply_add_node(
                            sub_effect, working_particle_state_in_step
                        )
                    elif sub_effect.effect_type == EffectType.ADD_EDGE:
                        new_state_after_sub_effect = await apply_add_edge(
                            sub_effect, working_particle_state_in_step
                        )
                    elif sub_effect.effect_type == EffectType.REMOVE_EDGE:
                        new_state_after_sub_effect = await apply_remove_edge(
                            sub_effect, working_particle_state_in_step
                        )
                    # TODO: Add handlers for any other basic effects generated by dynamics
                    else:
                        logging.warning(
                            f"Unhandled effect type generated during dynamics simulation: {sub_effect.effect_type}"
                        )
                except Exception as app_err:
                    logging.error(
                        f"Error applying sub-effect {sub_effect.effect_type} via applicator: {app_err}",
                        exc_info=True,
                    )
                    # Optionally: Decide whether to stop the step or continue
                    continue  # Skip this sub-effect

                # If the helper returned a new state (meaning a change occurred),
                # update our working state for the next sub-effect in this step.
                if new_state_after_sub_effect is not None:
                    working_particle_state_in_step = new_state_after_sub_effect
                    step_particle_state_changed_this_step = True

            # After processing all sub-effects for this step,
            # update the main simulation state if any changes occurred within the step.
            if step_particle_state_changed_this_step:
                sim_particle_state = (
                    working_particle_state_in_step  # Update state for the next step
                )
                particle_state_changed_overall = (
                    True  # Mark overall change across all steps
                )
                logging.debug(f"Step {step + 1}: Particle state updated.")
            else:
                logging.debug(
                    f"Step {step + 1}: No state changes resulted from dynamics effects."
                )

        # --- After Simulation Loop --- #
        final_particle_state = sim_particle_state

        # Recalculate final wave state based on final particle state & dynamic ACF
        final_wave_state_calc_result = await calculate_wave_state_from_particle(
            final_particle_state,
            temp_context_for_wave,  # Use same dynamic context
        )
        final_wave_state: Optional[ConcreteWaveState] = None
        if final_wave_state_calc_result:
            final_wave_state = final_wave_state_calc_result[0]

        logging.info(
            f"Dynamics simulation finished. Particle state changed: {particle_state_changed_overall}"
        )
        return final_particle_state, final_wave_state, particle_state_changed_overall

    except Exception as sim_err:
        logging.exception(f"Error during CAW dynamics simulation: {sim_err}")
        # Return initial state and False for change on error
        return initial_particle_state, initial_wave_state, False


async def calculate_particle_interaction_effects(
    particle_state: ConcreteParticleState,
    all_node_ids: List[NodeID],
    context: Context,
    interpreted_acf_params: Dict[str, Any],
) -> List[BaseEffect]:
    """
    Calculates effects arising from particle-particle interactions (force analogs).
    """
    if not all_node_ids:
        return []

    try:
        # Parallelize force calculations
        em_task = calculate_em_force_effect(
            particle_state, all_node_ids, context, interpreted_acf_params
        )
        grav_task = calculate_gravity_force_effect(
            particle_state, all_node_ids, context, interpreted_acf_params
        )
        weak_tasks = [
            calculate_weak_force_effect(
                particle_state, node_id, context, interpreted_acf_params
            )
            for node_id in all_node_ids
        ]
        higgs_task = calculate_higgs_field_effect(
            particle_state, all_node_ids, context, interpreted_acf_params
        )

        # Gather results
        (
            em_effects,
            grav_effects,
            weak_effects_results,
            higgs_effects,
        ) = await asyncio.gather(
            em_task, grav_task, asyncio.gather(*weak_tasks), higgs_task
        )

        # Filter out None results from weak force
        weak_effects = [eff for eff in weak_effects_results if eff is not None]

        combined = (
            (em_effects or [])
            + (grav_effects or [])
            + weak_effects
            + (higgs_effects or [])
        )
        logging.debug(f"Calculated {len(combined)} particle interaction effects.")
        return combined

    except Exception as e:
        logging.exception(f"Error calculating particle interaction effects: {e}")
        return []
