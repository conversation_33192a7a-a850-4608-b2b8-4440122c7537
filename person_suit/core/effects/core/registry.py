"""Effect Registry.

This module implements the effect registry system, managing effect handlers
and their lifecycle. It integrates with the CAW paradigm by maintaining
both wave and particle states of handlers.
"""

import logging
from contextlib import asynccontextmanager
from typing import TYPE_CHECKING, Any, Dict, Optional

from ._internal_types import Effect

if TYPE_CHECKING:
    from .base import EffectHandler

logger = logging.getLogger(__name__)


class EffectRegistry:
    """Registry for effect handlers.

    Manages the lifecycle and state of effect handlers, supporting:
    - Handler registration and discovery
    - Handler state management
    - Wave-particle duality in handler execution
    - Context propagation
    """

    def __init__(self):
        """Initialize the registry."""
        self._handlers: Dict[str, "EffectHandler"] = {}
        self._handler_states: Dict[str, Dict[str, Any]] = {}
        self._active_effects: Dict[str, Effect] = {}

    async def register_handler(
        self, handler_type: str, handler: "EffectHandler"
    ) -> None:
        """Register a new effect handler.

        Args:
            handler_type: Type of handler to register
            handler: Handler instance
        """

        if handler_type in self._handlers:
            logger.warning(f"Handler {handler_type} already registered, replacing")
            await self._cleanup_handler(handler_type)

        self._handlers[handler_type] = handler
        self._handler_states[handler_type] = {
            "wave_state": {},
            "particle_state": {},
            "context": {},
        }
        await handler._initialize_resources()

    async def unregister_handler(self, handler_type: str) -> None:
        """Unregister a handler.

        Args:
            handler_type: Type of handler to unregister
        """
        if handler_type in self._handlers:
            await self._cleanup_handler(handler_type)
            del self._handlers[handler_type]
            del self._handler_states[handler_type]

    async def handle_effect(
        self, effect: Effect, context: Optional[Dict[str, Any]] = None
    ) -> Any:
        """Handle an effect through appropriate handler.

        Args:
            effect: Effect to handle
            context: Runtime context

        Returns:
            Handler result
        """
        handler = self._get_handler_for_effect(effect)
        if not handler:
            raise ValueError(f"No handler found for effect type {effect.effect_type}")

        # Analyze wave state
        wave_state = await handler.analyze_wave_state(effect, context)

        # Store effect state
        effect_id = id(effect)
        self._active_effects[effect_id] = effect
        self._handler_states[effect.effect_type]["wave_state"][effect_id] = wave_state

        try:
            # Execute if wave state allows
            if wave_state.get("should_execute", True):
                result = await handler.execute_particle_state(effect, context)
                self._handler_states[effect.effect_type]["particle_state"][
                    effect_id
                ] = {"status": "completed", "result": result}
                return result
            else:
                logger.warning(f"Effect {effect_id} not executed due to wave state")
                return None
        except Exception as e:
            logger.error(f"Effect handling failed: {e}")
            self._handler_states[effect.effect_type]["particle_state"][effect_id] = {
                "status": "failed",
                "error": str(e),
            }
            raise
        finally:
            # Cleanup effect state
            self._cleanup_effect_state(effect_id, effect.effect_type)

    async def _cleanup_handler(self, handler_type: str) -> None:
        """Clean up handler resources.

        Args:
            handler_type: Handler type to clean up
        """
        handler = self._handlers.get(handler_type)
        if handler:
            try:
                await handler._cleanup_resources()
            except Exception as e:
                logger.error(f"Failed to cleanup handler {handler_type}: {e}")

    def _get_handler_for_effect(self, effect: Effect) -> Optional["EffectHandler"]:
        """Get appropriate handler for effect.

        Args:
            effect: Effect to handle

        Returns:
            Handler instance or None if not found
        """

        for handler in self._handlers.values():
            if handler.can_handle(effect):
                return handler
        return None

    def _cleanup_effect_state(self, effect_id: str, effect_type: str) -> None:
        """Clean up effect state.

        Args:
            effect_id: Effect ID
            effect_type: Effect type
        """
        if effect_id in self._active_effects:
            del self._active_effects[effect_id]

        states = self._handler_states.get(effect_type, {})
        if "wave_state" in states and effect_id in states["wave_state"]:
            del states["wave_state"][effect_id]
        if "particle_state" in states and effect_id in states["particle_state"]:
            del states["particle_state"][effect_id]

    @asynccontextmanager
    async def handler_context(self, handler_type: str, context: Dict[str, Any]):
        """Context manager for handler operations.

        Args:
            handler_type: Handler type
            context: Context information

        Yields:
            Handler instance
        """
        handler = self._handlers.get(handler_type)
        if not handler:
            raise ValueError(f"No handler found for type {handler_type}")

        # Store context
        self._handler_states[handler_type]["context"].update(context)

        try:
            yield handler
        finally:
            # Cleanup context
            for key in context:
                self._handler_states[handler_type]["context"].pop(key, None)


# Module-level singleton instance for global effect registry

global_effect_registry: EffectRegistry = EffectRegistry()
"""
A global singleton instance of EffectRegistry for use throughout the effect system.
This is the canonical registry for registering and discovering effect handlers.
"""
