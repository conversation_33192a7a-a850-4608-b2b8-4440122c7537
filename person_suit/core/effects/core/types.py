"""
Effect System Types
==================

This module defines the core types used in the effect system, integrating
with the CAW paradigm for wave-particle duality and context awareness.

The type system supports:
- Wave-particle duality through state transitions
- Interference pattern tracking
- Context-dependent behavior
- Quantum-inspired state management
"""

import logging
from dataclasses import dataclass
from enum import Enum, auto
from typing import Any, Dict, List, Optional

from ..interfaces import EffectInterface, EffectTypeInterface

from .constants import (
    DEFAULT_COLLAPSE_PROBABILITY,
    DEFAULT_PRIORITY,
    DEFAULT_STATE,
    EffectPriority,
    EffectState,
)

logger = logging.getLogger(__name__)


@dataclass
class EffectState:
    """Represents the state of an effect.

    Attributes:
        wave_potential: Wave function potential
        particle_position: Current particle position
        context_field: Contextual field information
        quantum_numbers: Quantum state numbers
    """

    wave_potential: float
    particle_position: Dict[str, float]
    context_field: Dict[str, Any]
    quantum_numbers: Dict[str, int]


@dataclass
class HandlerMetrics:
    """Metrics for effect handler performance.

    Attributes:
        operation_count: Number of operations performed
        success_rate: Operation success rate
        latency: Operation latency statistics
        resource_usage: Resource usage statistics
    """

    operation_count: int
    success_rate: float
    latency: Dict[str, float]
    resource_usage: Dict[str, float]


@dataclass
class ResourceMetrics:
    """Metrics for resource usage.

    Attributes:
        memory_usage: Memory usage statistics
        cpu_usage: CPU usage statistics
        io_operations: I/O operation statistics
        network_usage: Network usage statistics
    """

    memory_usage: Dict[str, float]
    cpu_usage: Dict[str, float]
    io_operations: Dict[str, int]
    network_usage: Dict[str, float]


class EffectType(EffectTypeInterface):
    """
    Effect type implementation representing both wave and particle aspects.

    An EffectType defines:
    - The name and nature of the effect
    - Supported operations in both wave and particle states
    - State transition rules
    - Interference patterns with other effects
    """

    def __init__(
        self,
        name: str,
        operations: List[str],
        wave_operations: Optional[List[str]] = None,
        collapse_probability: float = DEFAULT_COLLAPSE_PROBABILITY,
    ):
        """
        Initialize an effect type.

        Args:
            name: The name of the effect type
            operations: List of supported particle (manifested) operations
            wave_operations: Optional list of wave-state operations
            collapse_probability: Probability of wave function collapse
        """
        self._name = name
        self._operations = set(operations)
        self._wave_operations = set(wave_operations or [])
        self._collapse_probability = collapse_probability

    @property
    def name(self) -> str:
        """Get the name of this effect type."""
        return self._name

    @property
    def operations(self) -> List[str]:
        """Get the operations supported in particle state."""
        return list(self._operations)

    @property
    def wave_operations(self) -> List[str]:
        """Get the operations supported in wave state."""
        return list(self._wave_operations)

    def has_operation(self, operation: str, wave_state: bool = False) -> bool:
        """
        Check if this effect type supports an operation.

        Args:
            operation: The operation to check
            wave_state: Whether to check wave state operations

        Returns:
            True if the operation is supported
        """
        if wave_state:
            return operation in self._wave_operations
        return operation in self._operations

    def __eq__(self, other: object) -> bool:
        if not isinstance(other, EffectType):
            return NotImplemented
        return self._name == other._name

    def __hash__(self) -> int:
        return hash(self._name)

    def __str__(self) -> str:
        return f"EffectType({self._name})"

    def __repr__(self) -> str:
        return (
            f"EffectType(name='{self._name}', "
            f"operations={list(self._operations)}, "
            f"wave_operations={list(self._wave_operations)})"
        )


@dataclass
class Effect(EffectInterface):
    """
    Concrete implementation of an effect with wave-particle duality.

    An Effect represents a computation that can exist in both wave (potential)
    and particle (manifested) states. The state can collapse based on context
    and observation.
    """

    def __init__(
        self,
        effect_type: EffectTypeInterface,
        operation: str,
        parameters: Dict[str, Any],
        wave_state: bool = True,
        context: Optional[Dict[str, Any]] = None,
    ):
        """
        Initialize a new effect.

        Args:
            effect_type: The type of the effect
            operation: The operation to perform
            parameters: Operation parameters
            wave_state: Whether to start in wave state
            context: Optional context information

        Raises:
            ValueError: If the operation is not supported
        """
        if not effect_type.has_operation(operation, wave_state):
            supported = (
                effect_type.wave_operations if wave_state else effect_type.operations
            )
            raise ValueError(
                f"Effect type {effect_type.name} doesn't support operation "
                f"{operation} in {'wave' if wave_state else 'particle'} state. "
                f"Supported operations: {', '.join(supported)}"
            )

        self._effect_type = effect_type
        self._operation = operation
        self._parameters = parameters.copy()
        self._wave_state = wave_state
        self._context = context or {}
        self._state = DEFAULT_STATE
        self._priority = DEFAULT_PRIORITY

    @property
    def effect_type(self) -> EffectTypeInterface:
        """Get the type of the effect."""
        return self._effect_type

    @property
    def operation(self) -> str:
        """Get the operation of the effect."""
        return self._operation

    @property
    def parameters(self) -> Dict[str, Any]:
        """Get the parameters of the effect."""
        return self._parameters.copy()

    @property
    def wave_state(self) -> bool:
        """Check if the effect is in wave state."""
        return self._wave_state

    @property
    def context(self) -> Dict[str, Any]:
        """Get the effect's context."""
        return self._context.copy()

    @property
    def state(self) -> EffectState:
        """Get the effect's processing state."""
        return self._state

    @property
    def priority(self) -> EffectPriority:
        """Get the effect's processing priority."""
        return self._priority

    def collapse(self) -> None:
        """Collapse the wave function, transitioning to particle state."""
        if self._wave_state:
            self._wave_state = False
            logger.debug(f"Effect {self} collapsed to particle state")

    def __repr__(self) -> str:
        return (
            f"Effect(type={self.effect_type.name}, "
            f"operation={self.operation}, "
            f"wave_state={self.wave_state}, "
            f"state={self.state.name}, "
            f"priority={self.priority.name})"
        )


# Pre-defined effect types with wave-particle operations
IO = EffectType(
    "io",
    [
        "read",  # Read from a file
        "write",  # Write to a file
        "append",  # Append to a file
        "delete",  # Delete a file
        "exists",  # Check if a file exists
    ],
    wave_operations=[
        "potential_read",  # Calculate potential read paths
        "access_pattern",  # Analyze access patterns
        "locality_check",  # Check data locality
    ],
)

Database = EffectType(
    "database",
    [
        "query",  # Query data
        "insert",  # Insert data
        "update",  # Update data
        "delete",  # Delete data
        "execute",  # Execute a command
    ],
    wave_operations=[
        "query_plan",  # Generate query plan
        "impact_analysis",  # Analyze operation impact
        "consistency_check",  # Check consistency constraints
    ],
)

Network = EffectType(
    "network",
    [
        "get",  # HTTP GET
        "post",  # HTTP POST
        "put",  # HTTP PUT
        "delete",  # HTTP DELETE
        "patch",  # HTTP PATCH
        "head",  # HTTP HEAD
        "options",  # HTTP OPTIONS
    ],
    wave_operations=[
        "route_analysis",  # Analyze routing options
        "latency_predict",  # Predict latency
        "topology_check",  # Check network topology
    ],
)

State = EffectType(
    "state",
    [
        "get",  # Get state
        "set",  # Set state
        "update",  # Update state
        "delete",  # Delete state
        "watch",  # Watch state changes
    ],
    wave_operations=[
        "state_predict",  # Predict state changes
        "conflict_check",  # Check for conflicts
        "coherence_verify",  # Verify state coherence
    ],
)

Computation = EffectType(
    "computation",
    [
        "execute",  # Execute computation
        "cancel",  # Cancel computation
        "pause",  # Pause computation
        "resume",  # Resume computation
        "status",  # Get computation status
    ],
    wave_operations=[
        "resource_estimate",  # Estimate resource usage
        "optimize_path",  # Optimize execution path
        "parallel_analyze",  # Analyze parallelization
    ],
)

# Additional effect types
Random = EffectType("random", ["generate", "seed"], wave_operations=["entropy_check"])
Pure = EffectType("pure", ["evaluate"], wave_operations=["purity_verify"])
Security = EffectType(
    "security", ["verify", "encrypt", "decrypt"], wave_operations=["threat_model"]
)
Logging = EffectType(
    "logging",
    ["debug", "info", "warning", "error", "critical"],
    wave_operations=["pattern_analyze"],
)
Telemetry = EffectType(
    "telemetry", ["record", "measure", "track"], wave_operations=["trend_predict"]
)
InformationFlow = EffectType(
    "information_flow",
    ["transform", "route", "filter"],
    wave_operations=["flow_analyze"],
)

# Additional effect types for handlers
Probabilistic = EffectType(
    "probabilistic",
    [
        "sample",  # Sample from distribution
        "measure",  # Measure quantum state
        "get_statistics",  # Get distribution statistics
        "expectation",  # Calculate expectation value
    ],
    wave_operations=[
        "create_distribution",  # Create probability distribution
        "transform_distribution",  # Transform distribution
        "create_superposition",  # Create quantum superposition
        "analyze_interference",  # Analyze interference patterns
    ],
)

Differential = EffectType(
    "differential",
    [
        "update",  # Apply discrete updates
        "propagate",  # Propagate changes
        "convergence",  # Check convergence
        "history",  # Get update history
    ],
    wave_operations=[
        "create_field",  # Create differential field
        "compute_gradient",  # Compute gradients
        "transform_field",  # Transform field
        "analyze_derivatives",  # Analyze derivatives
    ],
)

Context = EffectType(
    "context",
    [
        "create_context",  # Create context instance
        "aggregate_contexts",  # Aggregate contexts
        "propagate_context",  # Propagate context
        "cleanup",  # Clean up context
    ],
    wave_operations=[
        "analyze_propagation",  # Analyze propagation paths
        "predict_aggregation",  # Predict aggregation results
        "estimate_evolution",  # Estimate context evolution
    ],
)

Memory = EffectType("memory", ["allocate", "free", "read", "write"])
Messaging = EffectType("messaging", ["send", "receive"])
StateRead = EffectType("state_read", ["read"])
StateChange = EffectType("state_change", ["change"])
ActorCreation = EffectType("actor_creation", ["create"])
ActorLifecycle = EffectType("actor_lifecycle", ["start", "stop", "restart"])
Conversion = EffectType("conversion", ["convert"])
Creation = EffectType("creation", ["create"])
ResourceManagement = EffectType("resource_management", ["allocate", "release"])
Custom = EffectType("custom", ["custom"])
