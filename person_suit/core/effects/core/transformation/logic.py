# -*- coding: utf-8 -*-
"""
State Transformation Logic for CAW

This module is responsible for interpreting STATE_MANIPULATION Effects and applying
the corresponding changes to the immutable DualInformation state (WaveState and
ParticleState). It encapsulates the core "business logic" of state modification
within CAW, dispatching specific effects to dedicated applicator functions and
handling dynamics simulation triggers.

Related Files:
- docs/design/Central_State_Actor_Design.md
- schemas.python_schema.py
- person_suit/core/caw/dual_information.py
- person_suit/core/actors/central_state_actor.py (Uses this class)
- person_suit/core/effects/dynamics_simulation.py (Handles dynamics logic)
- person_suit/core/effects/effect_applicators.py (Handles individual effect application)
"""

import logging
from typing import Any, Awaitable, Callable, Dict, Optional
from uuid import uuid4

# Import ACF and Resource Monitor for dependency injection
from ....caw.acf_logic import ACFManager
from ....caw.dual_information import ConcreteDualInformation
from ....caw.particle_state import ConcreteParticleState
from ....caw.resource_monitor import ResourceMonitor
from ....caw.schemas import (
    BaseEdge,
    BaseEffect,
    BaseNode,
    Context,
    DualInformation,
    EdgeID,
    EffectCategory,
    EffectType,
    NodeID,
    StateRef,
)

# Import wave calculation function (needed after state changes)
from ....caw.wave_function import calculate_wave_state_from_particle
from ....caw.wave_state import ConcreteWaveState

# Import dynamics simulation function
from .dynamics_simulation import simulate_caw_dynamics

# Import specific effect applicators
from .effect_applicators import (
    apply_add_edge,
    apply_add_node,
    apply_metadata_update,
    apply_remove_edge,
    apply_remove_node,
)

# Type alias for the handler function signature
EffectApplicator = Callable[
    [BaseEffect, ConcreteParticleState], Awaitable[Optional[ConcreteParticleState]]
]

logger = logging.getLogger(__name__)


class StateTransformationLogic:
    """
    Applies STATE_MANIPULATION effects to DualInformation state immutably.

    Uses a dispatch mechanism for standard effects and delegates dynamics
    simulation to a dedicated function.
    Relies on injected dependencies for ACF and resource monitoring.
    """

    def __init__(
        self,
        acf_manager: ACFManager,
        resource_monitor: ResourceMonitor,
        config: Optional[Dict[str, Any]] = None,  # Config might be needed
    ):
        """
        Initializes the StateTransformationLogic with dependencies.

        Args:
            acf_manager: Instance of ACFManager.
            resource_monitor: Instance of ResourceMonitor.
            config: Application configuration (optional, currently unused here).
        """
        self.acf_manager = acf_manager
        self.resource_monitor = resource_monitor
        self.config = config or {}
        self._effect_handler_map: Dict[EffectType, EffectApplicator] = (
            self._build_handler_map()
        )
        logger.info("StateTransformationLogic initialized.")

    def _build_handler_map(self) -> Dict[EffectType, EffectApplicator]:
        """Builds the map from EffectType to applicator functions."""
        # Assumes EffectType has the necessary members like UPDATE_NODE_METADATA etc.
        # If these are defined elsewhere (e.g., string constants), adjust accordingly.
        return {
            # Node operations
            EffectType.UPDATE_NODE_METADATA: lambda effect,
            state: apply_metadata_update(effect, state, node_update=True),
            EffectType.ADD_NODE: apply_add_node,
            EffectType.REMOVE_NODE: apply_remove_node,
            # Edge operations
            EffectType.UPDATE_EDGE_METADATA: lambda effect,
            state: apply_metadata_update(effect, state, node_update=False),
            EffectType.ADD_EDGE: apply_add_edge,
            EffectType.REMOVE_EDGE: apply_remove_edge,
            # Add other direct state manipulation effects here
            # Example: EffectType.SOME_OTHER_STATE_OP: handle_some_other_op
        }

    async def check_preconditions(
        self, effect: BaseEffect, current_state: DualInformation, context: Context
    ) -> bool:
        """
        Checks if the effect can be applied to the current state under the given context.

        Args:
            effect: The effect to check.
            current_state: The current state.
            context: The current context.

        Returns:
            True if preconditions are met, False otherwise.
        """
        # Placeholder: Implement actual precondition checks based on effect type and state.
        # This logic remains largely the same as the original, just checking existence.
        p_state = current_state.particle_state
        params = effect.parameters

        if effect.effect_type == EffectType.UPDATE_NODE_METADATA:
            target_node_id = params.get("node_id")
            if not target_node_id or not p_state.get_node(NodeID(target_node_id)):
                logging.warning(
                    f"Precondition failed for {effect.effect_type}: Node {target_node_id} not found."
                )
                return False
        elif effect.effect_type == EffectType.UPDATE_EDGE_METADATA:
            target_edge_id = params.get("edge_id")
            if not target_edge_id or not p_state.get_edge(EdgeID(target_edge_id)):
                logging.warning(
                    f"Precondition failed for {effect.effect_type}: Edge {target_edge_id} not found."
                )
                return False
        elif effect.effect_type == EffectType.ADD_NODE:
            node_data = params.get("node")
            if not isinstance(node_data, BaseNode):
                logging.warning(
                    f"Precondition failed for {effect.effect_type}: Invalid or missing 'node' parameter."
                )
                return False
            if p_state.get_node(node_data.node_id):
                logging.warning(
                    f"Precondition failed for {effect.effect_type}: Node ID {node_data.node_id} already exists."
                )
                return False
        elif effect.effect_type == EffectType.ADD_EDGE:
            edge_data = params.get("edge")
            if not isinstance(edge_data, BaseEdge):
                logging.warning(
                    f"Precondition failed for {effect.effect_type}: Invalid or missing 'edge' parameter."
                )
                return False
            if p_state.get_edge(edge_data.edge_id):
                logging.warning(
                    f"Precondition failed for {effect.effect_type}: Edge ID {edge_data.edge_id} already exists."
                )
                return False
            for node_id in edge_data.connected_nodes:
                if not p_state.get_node(node_id):
                    logging.warning(
                        f"Precondition failed for {effect.effect_type}: Connected node {node_id} does not exist."
                    )
                    return False
        elif effect.effect_type == EffectType.REMOVE_NODE:
            target_node_id = params.get("node_id")
            if not target_node_id or not p_state.get_node(NodeID(target_node_id)):
                logging.warning(
                    f"Precondition failed for {effect.effect_type}: Node {target_node_id} not found."
                )
                return False
        elif effect.effect_type == EffectType.REMOVE_EDGE:
            target_edge_id = params.get("edge_id")
            if not target_edge_id or not p_state.get_edge(EdgeID(target_edge_id)):
                logging.warning(
                    f"Precondition failed for {effect.effect_type}: Edge {target_edge_id} not found."
                )
                return False
        elif effect.effect_type == EffectType.SIMULATE_DYNAMICS:
            # No specific particle state preconditions here, maybe context checks?
            pass

        # Allow other effect types to pass for now, assuming non-state effects handled elsewhere
        logging.debug(f"Preconditions check passed for effect {effect.effect_type}")
        return True

    async def apply_effect(
        self, effect: BaseEffect, current_state: DualInformation, context: Context
    ) -> Optional[DualInformation]:
        """
        Applies a single STATE_MANIPULATION effect to the current state.
        Non-state manipulation effects are ignored, returning the original state.
        Returns the new DualInformation state if successful, None on failure.
        """
        if effect.category != EffectCategory.STATE_MANIPULATION:
            logging.debug(
                f"Ignoring effect type {effect.effect_type} with category {effect.category} in StateTransformationLogic."
            )
            return current_state

        logging.debug(
            f"Applying STATE_MANIPULATION effect: {effect.effect_type} ({getattr(effect, 'target_entity_id', 'N/A')}) with context {getattr(context, 'context_id', 'N/A')}"
        )

        initial_particle_state: ConcreteParticleState = current_state.particle_state
        initial_wave_state: Optional[ConcreteWaveState] = current_state.wave_state
        result_particle_state: Optional[ConcreteParticleState] = None
        result_wave_state: Optional[ConcreteWaveState] = initial_wave_state
        particle_state_changed: bool = False

        try:
            if effect.effect_type == EffectType.SIMULATE_DYNAMICS:
                # Delegate to the dynamics simulation function
                final_p_state, final_w_state, sim_changed = await simulate_caw_dynamics(
                    initial_particle_state,
                    initial_wave_state,
                    context,
                    self.acf_manager,
                    self.resource_monitor,
                )
                result_particle_state = final_p_state
                result_wave_state = final_w_state  # Use wave state calculated by sim
                particle_state_changed = sim_changed

            else:
                # Standard effect handling using dispatch map
                handler = self._effect_handler_map.get(effect.effect_type)
                if handler:
                    modified_p_state = await handler(effect, initial_particle_state)
                    if (
                        modified_p_state is not None
                    ):  # Applicator returns None if no change
                        result_particle_state = modified_p_state
                        particle_state_changed = True
                    else:
                        result_particle_state = (
                            initial_particle_state  # No change occurred
                        )
                else:
                    logging.error(
                        f"Unhandled STATE_MANIPULATION effect type: {effect.effect_type}"
                    )
                    return None  # Indicate failure for unhandled state effect

                # If standard effect changed particle state, recalculate wave state
                if particle_state_changed:
                    logging.debug(
                        "Particle state changed by standard effect, calculating final wave state..."
                    )
                    wave_calc_result = await calculate_wave_state_from_particle(
                        result_particle_state, context
                    )
                    if wave_calc_result:
                        result_wave_state = wave_calc_result[0]
                    else:
                        logging.warning(
                            "Wave state calculation failed or suppressed after standard effect."
                        )
                        result_wave_state = None  # Or retain initial_wave_state?

            # If no change occurred at all, return the original state reference
            if not particle_state_changed:
                logging.debug(
                    f"Effect {effect.effect_type} applied but resulted in no particle state change."
                )
                return current_state

            # --- Create and return the new DualInformation state --- #
            # Ensure we have the final particle state (could be initial if only sim ran w/o change)
            final_particle_state_to_use = (
                result_particle_state
                if result_particle_state is not None
                else initial_particle_state
            )

            # Create the next immutable state version
            # TODO: Ensure ConcreteDualInformation.create_next_version exists and works
            if hasattr(ConcreteDualInformation, "create_next_version"):
                new_dual_state = ConcreteDualInformation.create_next_version(
                    previous_state=current_state,
                    new_particle_state=final_particle_state_to_use,
                    new_wave_state=result_wave_state,  # Use the definitive wave state
                )
                logging.info(
                    f"Successfully applied effect {effect.effect_type}. New state ref: {new_dual_state.state_ref}"
                )
                return new_dual_state
            else:
                logging.error(
                    "ConcreteDualInformation lacks create_next_version method."
                )
                # Fallback: Manually create a new instance (might miss versioning logic)
                fallback_ref = StateRef(
                    f"st_{uuid4().hex[:8]}"
                )  # Example temporary ref
                logging.warning(
                    f"Creating fallback DualInformation with ref {fallback_ref}"
                )
                return ConcreteDualInformation(
                    particle_state=final_particle_state_to_use,
                    wave_state=result_wave_state,
                    state_ref=fallback_ref,
                    previous_state_ref=current_state.state_ref,
                )

        except Exception as e:
            logging.exception(
                f"Error during apply_effect for {effect.effect_type}: {e}"
            )
            return None  # Return None on any exception during application

    # --- Helper Methods Removed --- #
    # _apply_metadata_update, _apply_remove_node, etc. are now in effect_applicators.py
