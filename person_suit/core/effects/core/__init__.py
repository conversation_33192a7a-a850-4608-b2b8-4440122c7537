"""
Core Effect System Components
===========================

This module provides the fundamental building blocks of the effect system,
implementing the Contextual Adaptive Wave (CAW) paradigm. The core components
support:

- Wave-particle duality in computation
- Context-dependent effect behavior
- Interference pattern tracking
- Quantum-inspired state management
- Adaptive computational fidelity

Key Concepts:
------------
- Effects exist in both wave (potential) and particle (actual) states
- Context influences effect behavior and state transitions
- Interference patterns emerge from effect interactions
- Effects can be entangled and exhibit quantum-like properties
- Computational fidelity adapts based on context and resources

Module Structure:
---------------
- types.py: Core type definitions and implementations
- constants.py: System constants and configuration
- base.py: Abstract base classes and protocols
- utils.py: Utility functions and helpers
"""

from .types import (
    Effect, 
    EffectType,
    # Predefined effect types
    IO,
    Database,
    Network,
    State,
    Random,
    Pure,
    Security,
    Logging,
    Telemetry,
    Computation,
    InformationFlow,
    Probabilistic,
    Differential,
    Context,
    Memory,
    Messaging,
    StateRead,
    StateChange,
    ActorCreation,
    ActorLifecycle,
    Conversion,
    Creation,
    ResourceManagement,
    Custom,
)
from ..interfaces import (
    EffectHandlerInterface,
    EffectInterface,
    EffectRegistryInterface,
    EffectRuntimeInterface,
    EffectTypeInterface,
)

from .constants import (
    DEFAULT_COLLAPSE_PROBABILITY,
    DEFAULT_PRIORITY,
    DEFAULT_STATE,
    MAX_EFFECT_CHAIN_LENGTH,
    MAX_ENTANGLEMENT_GROUP_SIZE,
    MAX_INTERFERENCE_PATTERNS,
    MAX_SUPERPOSITION_STATES,
    MAX_WAVE_FUNCTION_DIMENSION,
    ContextKeys,
    EffectPriority,
    EffectState,
    SystemTags,
)

__all__ = [
    # Constants
    "EffectPriority",
    "EffectState",
    "ContextKeys",
    "SystemTags",
    "DEFAULT_PRIORITY",
    "DEFAULT_STATE",
    "DEFAULT_COLLAPSE_PROBABILITY",
    "MAX_EFFECT_CHAIN_LENGTH",
    "MAX_ENTANGLEMENT_GROUP_SIZE",
    "MAX_SUPERPOSITION_STATES",
    "MAX_INTERFERENCE_PATTERNS",
    "MAX_WAVE_FUNCTION_DIMENSION",
    # Types
    "EffectType",
    "Effect",
    # Predefined effect types
    "IO",
    "Database",
    "Network",
    "State",
    "Random",
    "Pure",
    "Security",
    "Logging",
    "Telemetry",
    "Computation",
    "InformationFlow",
    "Probabilistic",
    "Differential",
    "Context",
    "Memory",
    "Messaging",
    "StateRead",
    "StateChange",
    "ActorCreation",
    "ActorLifecycle",
    "Conversion",
    "Creation",
    "ResourceManagement",
    "Custom",
    # Interfaces
    "EffectInterface",
    "EffectTypeInterface",
    "EffectHandlerInterface",
    "EffectRegistryInterface",
    "EffectRuntimeInterface",
]
