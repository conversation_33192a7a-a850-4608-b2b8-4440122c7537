"""
File: types.py
Purpose: Effect type definitions for the Person Suit effect system

This module provides the Effect and EffectType classes that implement the
interfaces defined in the person_suit.core.effects.interfaces package.

Related Files:
- person_suit/core/infrastructure/effects/core.py: Core effect type definitions
- person_suit/core/infrastructure/effects/handlers.py: Effect handlers

Dependencies:
- typing>=4.0.0: For type annotations
"""

from dataclasses import dataclass, field
from enum import Enum, auto
from typing import Any, Dict, Optional, Set
from uuid import UUID

# Type aliases for identifiers
EffectID = UUID
HandlerID = UUID
ContextID = UUID


class RuntimeState(Enum):
    """Runtime state enumeration."""

    INITIALIZING = auto()
    RUNNING = auto()
    SHUTTING_DOWN = auto()
    STOPPED = auto()
    ERROR = auto()


class EffectState(Enum):
    """Effect state enumeration."""

    CREATED = auto()
    ANALYZING = auto()
    EXECUTING = auto()
    COMPLETED = auto()
    FAILED = auto()
    CANCELLED = auto()


class HandlerState(Enum):
    """Handler state enumeration."""

    UNINITIALIZED = auto()
    INITIALIZING = auto()
    READY = auto()
    BUSY = auto()
    ERROR = auto()
    CLEANING_UP = auto()
    STOPPED = auto()


@dataclass
class RuntimeMetrics:
    """Runtime metrics data."""

    total_effects: int = 0
    active_effects: int = 0
    successful_executions: int = 0
    failed_executions: int = 0
    cancelled_executions: int = 0
    average_execution_time: float = 0.0
    handler_metrics: Dict[HandlerID, Dict[str, Any]] = field(default_factory=dict)


@dataclass
class EffectMetrics:
    """Effect execution metrics data."""

    creation_time: float = 0.0
    start_time: float = 0.0
    end_time: float = 0.0
    wave_analysis_time: float = 0.0
    particle_execution_time: float = 0.0
    resource_usage: Dict[str, float] = field(default_factory=dict)
    handler_specific: Dict[HandlerID, Dict[str, Any]] = field(default_factory=dict)


@dataclass
class HandlerMetrics:
    """Handler operation metrics data."""

    total_operations: int = 0
    successful_operations: int = 0
    failed_operations: int = 0
    average_wave_time: float = 0.0
    average_particle_time: float = 0.0
    resource_usage: Dict[str, float] = field(default_factory=dict)
    error_counts: Dict[str, int] = field(default_factory=dict)


@dataclass
class ResourceMetrics:
    """Resource usage metrics data."""

    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    io_operations: int = 0
    network_calls: int = 0
    database_queries: int = 0
    cache_hits: int = 0
    cache_misses: int = 0


@dataclass
class EffectContext:
    """Context data for effect execution."""

    effect_id: EffectID
    handler_id: Optional[HandlerID] = None
    parent_context: Optional["EffectContext"] = None
    state: EffectState = EffectState.CREATED
    metadata: Dict[str, Any] = field(default_factory=dict)
    metrics: EffectMetrics = field(default_factory=EffectMetrics)


@dataclass
class HandlerContext:
    """Context data for handler operations."""

    handler_id: HandlerID
    state: HandlerState = HandlerState.UNINITIALIZED
    supported_effects: Set[str] = field(default_factory=set)
    config: Dict[str, Any] = field(default_factory=dict)
    metrics: HandlerMetrics = field(default_factory=HandlerMetrics)


@dataclass
class RuntimeContext:
    """Context data for runtime operations."""

    state: RuntimeState = RuntimeState.INITIALIZING
    active_effects: Dict[EffectID, EffectContext] = field(default_factory=dict)
    active_handlers: Dict[HandlerID, HandlerContext] = field(default_factory=dict)
    metrics: RuntimeMetrics = field(default_factory=RuntimeMetrics)
    config: Dict[str, Any] = field(default_factory=dict)
