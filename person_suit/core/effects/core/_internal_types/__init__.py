"""Core Types Package.

This package provides type definitions for the effect system.
"""

from .types import (
    Context<PERSON>,
    EffectContext,
    EffectID,
    EffectMetrics,
    EffectState,
    HandlerContext,
    HandlerID,
    HandlerMetrics,
    HandlerState,
    ResourceMetrics,
    RuntimeContext,
    RuntimeMetrics,
    RuntimeState,
)

from typing import Any, Dict, Optional, Set
from uuid import UUID

# Import Effect class from the correct location
from ...effect_types import Effect

# Import interface base classes from interfaces module
from ...interfaces import (
    EffectHandlerInterface,
    EffectInterface,
    EffectRegistryInterface,
    EffectRuntimeInterface,
    EffectTypeInterface,
)

# Type aliases for identifiers
EffectID = UUID

__all__ = [
    "EffectID",
    "HandlerID",
    "ContextID",
    "RuntimeState",
    "EffectState",
    "HandlerState",
    "RuntimeMetrics",
    "EffectMetrics",
    "HandlerMetrics",
    "ResourceMetrics",
    "EffectContext",
    "HandlerContext",
    "RuntimeContext",
    "Effect",
    "EffectTypeInterface",
    # 'EffectResultInterface' is intentionally not re-exported here to avoid circular import.
    # Import it directly from 'person_suit.core.effects.core.base.interfaces' if needed.
    "EffectHandlerInterface",
    "EffectRegistryInterface",
    "EffectRuntimeInterface",
]
