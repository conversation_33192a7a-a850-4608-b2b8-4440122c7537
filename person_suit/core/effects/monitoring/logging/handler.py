"""
Person Suit - Logging Effect Handler

Provides a handler for logging effects.
"""

import logging
from typing import Any, Dict, List, Optional

# Import moved base class definition
from ...handlers import BaseEffectHandler
from ...interfaces import (
    EffectTypeInterface,
)


class LoggingEffectType(EffectTypeInterface):
    """Effect type for logging."""

    # Operations
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

    @property
    def name(self) -> str:
        """
        Get the name of the effect type.

        Returns:
            The name of the effect type
        """
        return "logging"

    @property
    def operations(self) -> List[str]:
        """
        Get the operations supported by this effect type.

        Returns:
            A list of supported operations
        """
        return [self.DEBUG, self.INFO, self.WARNING, self.ERROR, self.CRITICAL]


class LoggingHandler(BaseEffectHandler):
    """Handler for logging effects."""

    def __init__(self, logger_name: str = "person_suit.effects"):
        """
        Initialize the logging handler.

        Args:
            logger_name: Name of the logger to use
        """
        super().__init__("logging_handler")
        self._logger = logging.getLogger(logger_name)

    def can_handle(self, effect_type: EffectTypeInterface) -> bool:
        """
        Check if this handler can handle the given effect type.

        Args:
            effect_type: The effect type to check

        Returns:
            True if this handler can handle the effect type, False otherwise
        """
        return isinstance(effect_type, LoggingEffectType)

    def can_handle_operation(
        self, effect_type: EffectTypeInterface, operation: str
    ) -> bool:
        """
        Check if this handler can handle the given operation for the effect type.

        Args:
            effect_type: The effect type
            operation: The operation to check

        Returns:
            True if this handler can handle the operation, False otherwise
        """
        if not self.can_handle(effect_type):
            return False

        return operation in effect_type.operations

    def get_supported_operations(self, effect_type: EffectTypeInterface) -> List[str]:
        """
        Get the operations supported by this handler for the given effect type.

        Args:
            effect_type: The effect type

        Returns:
            A list of supported operations
        """
        if not self.can_handle(effect_type):
            return []

        return effect_type.operations

    def handle(
        self,
        effect_type: EffectTypeInterface,
        operation: str,
        context: Optional[Dict[str, Any]] = None,
        **kwargs: Any,
    ) -> None:
        """
        Handle a logging effect.

        Args:
            effect_type: The type of the effect
            operation: The operation to perform
            context: Optional context information
            **kwargs: The parameters for the operation

        Returns:
            None

        Raises:
            ValueError: If the effect type or operation is not supported
        """
        if not self.can_handle(effect_type):
            raise ValueError(f"Unsupported effect type: {effect_type.name}")

        if not self.can_handle_operation(effect_type, operation):
            raise ValueError(
                f"Unsupported operation '{operation}' for effect type '{effect_type.name}'"
            )

        message = kwargs.get("message", "")
        extra = kwargs.get("extra", {})

        # Add context to extra if provided
        if context:
            if extra:
                extra.update(context)
            else:
                extra = context

        # Log with the appropriate level
        if operation == LoggingEffectType.DEBUG:
            self._logger.debug(message, extra=extra if extra else None)
        elif operation == LoggingEffectType.INFO:
            self._logger.info(message, extra=extra if extra else None)
        elif operation == LoggingEffectType.WARNING:
            self._logger.warning(message, extra=extra if extra else None)
        elif operation == LoggingEffectType.ERROR:
            self._logger.error(message, extra=extra if extra else None)
        elif operation == LoggingEffectType.CRITICAL:
            self._logger.critical(message, extra=extra if extra else None)
