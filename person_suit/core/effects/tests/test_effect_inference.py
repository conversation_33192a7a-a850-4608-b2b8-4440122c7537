"""
File: test_effect_inference.py
Purpose: Tests for the enhanced effect inference system.

This module contains tests for the enhanced effect inference system,
verifying that effects are correctly inferred from function implementations.

Related Files:
- person_suit/core/infrastructure/effects/decorators.py: Effect decorators
- person_suit/core/infrastructure/effects/types.py: Effect type definitions

Dependencies:
- pytest: For testing
- unittest.mock: For mocking
"""

import os
from unittest.mock import MagicMock, patch

from ...infrastructure.effects.decorators import with_effects
from ...infrastructure.effects.types import (
    IO,
    Database,
    Logging,
    Network,
    Pure,
    Random,
    Security,
)


class TestEffectInference:
    """Tests for the enhanced effect inference system."""

    def test_infer_io_effects(self):
        """Test that IO effects are correctly inferred."""

        @with_effects
        def read_file(path):
            with open(path, "r") as f:
                return f.read()

        # Check that IO effects were inferred
        assert hasattr(read_file, "__effects__")
        assert IO in read_file.__effects__

    def test_infer_database_effects(self):
        """Test that Database effects are correctly inferred."""

        @with_effects
        def query_database(query):
            connection = MagicMock()
            cursor = connection.cursor()
            cursor.execute(query)
            return cursor.fetchall()

        # Check that Database effects were inferred
        assert hasattr(query_database, "__effects__")
        assert Database in query_database.__effects__

    def test_infer_network_effects(self):
        """Test that Network effects are correctly inferred."""

        @with_effects
        def fetch_url(url):
            import requests

            response = requests.get(url)
            return response.json()

        # Check that Network effects were inferred
        assert hasattr(fetch_url, "__effects__")
        assert Network in fetch_url.__effects__

    def test_infer_random_effects(self):
        """Test that Random effects are correctly inferred."""

        @with_effects
        def generate_random():
            import random

            return random.randint(1, 100)

        # Check that Random effects were inferred
        assert hasattr(generate_random, "__effects__")
        assert Random in generate_random.__effects__

    def test_infer_logging_effects(self):
        """Test that Logging effects are correctly inferred."""

        @with_effects
        def log_message(message):
            import logging

            logger = logging.getLogger(__name__)
            logger.info(message)

        # Check that Logging effects were inferred
        assert hasattr(log_message, "__effects__")
        assert Logging in log_message.__effects__

    def test_infer_security_effects(self):
        """Test that Security effects are correctly inferred."""

        @with_effects
        def hash_password(password):
            import hashlib

            return hashlib.sha256(password.encode()).hexdigest()

        # Check that Security effects were inferred
        assert hasattr(hash_password, "__effects__")
        assert Security in hash_password.__effects__

    def test_infer_pure_effects(self):
        """Test that Pure effects are correctly inferred for pure functions."""

        @with_effects
        def add(a, b):
            return a + b

        # Check that Pure effects were inferred
        assert hasattr(add, "__effects__")
        assert Pure in add.__effects__

    def test_infer_multiple_effects(self):
        """Test that multiple effects are correctly inferred."""

        @with_effects
        def process_data(path):
            # IO effect
            with open(path, "r") as f:
                content = f.read()

            # Random effect
            import random

            if random.random() > 0.5:
                # Logging effect
                import logging

                logger = logging.getLogger(__name__)
                logger.info("Processing data")

            return content

        # Check that multiple effects were inferred
        assert hasattr(process_data, "__effects__")
        assert IO in process_data.__effects__
        assert Random in process_data.__effects__
        assert Logging in process_data.__effects__

    def test_infer_effects_from_perform_effect(self):
        """Test that effects are correctly inferred from perform_effect calls."""
        # Mock the perform_effect function
        with patch(
            "person_suit.core.infrastructure.effects.decorators.perform_effect"
        ) as mock_perform_effect:
            # Define a function that uses perform_effect
            @with_effects
            def process_data(path):
                from person_suit.core.infrastructure.effects import (
                    IO,
                    Database,
                    perform_effect,
                )

                # IO effect
                content = perform_effect(IO, "read_file", path)

                # Database effect
                perform_effect(Database, "insert", "data", content)

                return content

            # Check that effects were inferred
            assert hasattr(process_data, "__effects__")
            assert IO in process_data.__effects__
            assert Database in process_data.__effects__

    def test_infer_effects_from_function_calls(self):
        """Test that effects are correctly inferred from function calls."""

        # Define a function with known effects
        @with_effects
        def read_file(path):
            with open(path, "r") as f:
                return f.read()

        # Define a function that calls the function with known effects
        @with_effects
        def process_file(path):
            content = read_file(path)
            return content.upper()

        # Check that effects were inferred from the function call
        assert hasattr(process_file, "__effects__")
        assert IO in process_file.__effects__

    def test_infer_effects_from_patterns(self):
        """Test that effects are correctly inferred from patterns."""

        @with_effects
        def complex_function():
            # This should be detected as having multiple effects
            import hashlib
            import logging
            import random

            # IO effect pattern
            files = os.listdir(".")

            # Random effect pattern
            value = random.choice(files)

            # Logging effect pattern
            logger = logging.getLogger(__name__)
            logger.info(f"Selected file: {value}")

            # Security effect pattern
            hash_value = hashlib.sha256(value.encode()).hexdigest()

            return hash_value

        # Check that effects were inferred from patterns
        assert hasattr(complex_function, "__effects__")
        assert IO in complex_function.__effects__
        assert Random in complex_function.__effects__
        assert Logging in complex_function.__effects__
        assert Security in complex_function.__effects__

    def test_handle_syntax_errors(self):
        """Test that syntax errors are handled gracefully."""
        # Define a function with a syntax error
        try:
            # This should raise a SyntaxError during effect inference
            exec(
                """
@with_effects
def invalid_function():
    # This has a syntax error
    if True
        return "Invalid"
"""
            )
            # If we get here, the test failed
            assert False, "SyntaxError was not raised"
        except SyntaxError:
            # This is expected
            pass
