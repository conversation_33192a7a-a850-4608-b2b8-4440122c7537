"""
Person Suit - Effect System Tests

Tests for the effect system components.
"""

import unittest
from unittest.mock import patch

from ..effect import Effect
from ..handler_registry import EffectHandlerRegistry
from ..handlers.logging_handler import (
    LoggingEffectType,
    LoggingHandler,
)


class TestEffectSystem(unittest.TestCase):
    """Tests for the effect system."""

    def setUp(self):
        """Set up test fixtures."""
        self.registry = EffectHandlerRegistry()
        self.logging_handler = LoggingHandler()
        self.registry.register_handler(self.logging_handler)

    def test_effect_creation(self):
        """Test creating an effect."""
        effect_type = LoggingEffectType()
        effect = Effect(
            effect_type, LoggingEffectType.INFO, {"message": "Test message"}
        )

        self.assertEqual(effect.effect_type, effect_type)
        self.assertEqual(effect.operation, LoggingEffectType.INFO)
        self.assertEqual(effect.parameters, {"message": "Test message"})

    def test_invalid_operation(self):
        """Test creating an effect with an invalid operation."""
        effect_type = LoggingEffectType()

        with self.assertRaises(ValueError):
            Effect(effect_type, "invalid_operation", {"message": "Test message"})

    @patch("logging.Logger.info")
    def test_handle_effect(self, mock_info):
        """Test handling an effect."""
        effect_type = LoggingEffectType()
        effect = Effect(
            effect_type, LoggingEffectType.INFO, {"message": "Test message"}
        )

        self.registry.handle_effect(effect)

        mock_info.assert_called_once_with("Test message")

    def test_registry_get_handler(self):
        """Test getting a handler from the registry."""
        effect_type = LoggingEffectType()
        effect = Effect(
            effect_type, LoggingEffectType.INFO, {"message": "Test message"}
        )

        handler = self.registry.get_handler_for_effect(effect)

        self.assertIsNotNone(handler)
        self.assertEqual(handler, self.logging_handler)

    def test_registry_no_handler(self):
        """Test handling an effect with no handler."""

        # Create a custom effect type not registered with any handler
        class CustomEffectType(LoggingEffectType):
            @property
            def name(self):
                return "custom"

        effect_type = CustomEffectType()
        effect = Effect(
            effect_type, LoggingEffectType.INFO, {"message": "Test message"}
        )

        with self.assertRaises(ValueError):
            self.registry.handle_effect(effect)

    def test_unregister_handler(self):
        """Test unregistering a handler."""
        self.registry.unregister_handler(self.logging_handler)

        effect_type = LoggingEffectType()
        effect = Effect(
            effect_type, LoggingEffectType.INFO, {"message": "Test message"}
        )

        handler = self.registry.get_handler_for_effect(effect)

        self.assertIsNone(handler)


if __name__ == "__main__":
    unittest.main()
