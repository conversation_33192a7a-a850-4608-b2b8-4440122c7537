"""
File: test_effects.py
Purpose: Tests for the effect system.

This module contains tests for the effect system, including tests for
effect types, effect handlers, and the effect runtime.

Related Files:
- person_suit/core/infrastructure/effects/core.py: Core effect type definitions
- person_suit/core/infrastructure/effects/handlers.py: Effect handlers
- person_suit/core/infrastructure/effects/runtime.py: Effect runtime

Dependencies:
- pytest: For testing
- unittest.mock: For mocking
"""

import os
import tempfile
from unittest.mock import MagicMock

import pytest

from ...infrastructure.effects import perform_effect, run_with_effects
from ...infrastructure.effects.core import Effect
from ...infrastructure.effects.decorators import (
    effect_handler,
    effects,
)
from ...infrastructure.effects.handlers import (
    DatabaseHandler,
    IOHandler,
)
from ...infrastructure.effects.registry import EffectHandlerRegistry
from ...infrastructure.effects.runtime import EffectRuntime, get_runtime
from ...infrastructure.effects.types import IO, Database, Network, Pure


class TestEffectTypes:
    """Tests for effect types."""

    def test_effect_type_name(self):
        """Test that effect type names are correct."""
        assert IO.name() == "IO"
        assert Database.name() == "Database"
        assert Network.name() == "Network"
        assert Pure.name() == "Pure"

    def test_effect_type_str(self):
        """Test that effect type string representations are correct."""
        assert str(IO()) == "IO"
        assert str(Database()) == "Database"
        assert str(Network()) == "Network"
        assert str(Pure()) == "Pure"

    def test_effect_type_repr(self):
        """Test that effect type detailed string representations are correct."""
        assert repr(IO()) == "EffectType(IO)"
        assert repr(Database()) == "EffectType(Database)"
        assert repr(Network()) == "EffectType(Network)"
        assert repr(Pure()) == "EffectType(Pure)"


class TestEffect:
    """Tests for the Effect class."""

    def test_effect_creation(self):
        """Test that effects can be created."""
        effect = Effect(IO, "read_file", None, "test.txt")
        assert effect.effect_type == IO
        assert effect.operation == "read_file"
        assert effect.args == ("test.txt",)
        assert effect.kwargs == {}

    def test_effect_run_with(self):
        """Test that effects can be run with a handler."""
        # Create a mock handler
        handler = MagicMock()
        handler.handle.return_value = "test content"

        # Create an effect
        effect = Effect(IO, "read_file", None, "test.txt")

        # Run the effect with the handler
        result = effect.run_with(handler)

        # Check that the handler was called correctly
        handler.handle.assert_called_once_with(IO, "read_file", None, "test.txt")

        # Check that the result is correct
        assert result == "test content"

    def test_effect_map(self):
        """Test that effects can be mapped."""

        # Create a computation function
        def computation(*args, **kwargs):
            return "test content"

        # Create an effect
        effect = Effect(IO, "read_file", computation, "test.txt")

        # Map the effect
        mapped_effect = effect.map(len)

        # Check that the mapped effect has the correct properties
        assert mapped_effect.effect_type == IO
        assert mapped_effect.operation == "read_file"
        assert mapped_effect.args == ("test.txt",)
        assert mapped_effect.kwargs == {}

        # Check that the mapped computation returns the correct result
        result = mapped_effect.computation("test.txt")
        assert result == 12  # len("test content") == 12

    def test_effect_flat_map(self):
        """Test that effects can be flat-mapped."""

        # Create a computation function
        def computation(*args, **kwargs):
            return "test content"

        # Create an effect
        effect = Effect(IO, "read_file", computation, "test.txt")

        # Create a function to flat-map the effect
        def flat_map_func(content):
            return Effect(IO, "write_file", None, "output.txt", content)

        # Flat-map the effect
        flat_mapped_effect = effect.flat_map(flat_map_func)

        # Check that the flat-mapped effect has the correct properties
        assert flat_mapped_effect.effect_type == IO
        assert flat_mapped_effect.operation == "read_file"
        assert flat_mapped_effect.args == ("test.txt",)
        assert flat_mapped_effect.kwargs == {}


class TestIOHandler:
    """Tests for the IOHandler class."""

    def test_can_handle(self):
        """Test that IOHandler can handle IO effects."""
        handler = IOHandler()
        assert handler.can_handle(IO)
        assert handler.can_handle(IO())
        assert not handler.can_handle(Database)
        assert not handler.can_handle(Database())

    def test_handle_read_file(self):
        """Test that IOHandler can handle read_file operations."""
        handler = IOHandler()

        # Create a temporary file
        with tempfile.NamedTemporaryFile(mode="w", delete=False) as f:
            f.write("test content")
            file_path = f.name

        try:
            # Handle a read_file operation
            result = handler.handle(IO, "read_file", None, file_path)

            # Check that the result is correct
            assert result == "test content"
        finally:
            # Clean up
            os.unlink(file_path)

    def test_handle_write_file(self):
        """Test that IOHandler can handle write_file operations."""
        handler = IOHandler()

        # Create a temporary file path
        with tempfile.NamedTemporaryFile(delete=True) as f:
            file_path = f.name

        try:
            # Handle a write_file operation
            handler.handle(IO, "write_file", None, file_path, "test content")

            # Check that the file was written correctly
            with open(file_path, "r") as f:
                content = f.read()
                assert content == "test content"
        finally:
            # Clean up
            if os.path.exists(file_path):
                os.unlink(file_path)

    def test_handle_unsupported_operation(self):
        """Test that IOHandler raises an error for unsupported operations."""
        handler = IOHandler()

        # Try to handle an unsupported operation
        with pytest.raises(NotImplementedError):
            handler.handle(IO, "unsupported_operation", None)


class TestEffectHandlerRegistry:
    """Tests for the EffectHandlerRegistry class."""

    def test_register_handler(self):
        """Test that handlers can be registered."""
        registry = EffectHandlerRegistry()
        handler = IOHandler()

        # Register the handler
        registry.register_handler(IO, handler)

        # Check that the handler was registered
        assert registry.get_handler(IO) == handler

    def test_get_handler(self):
        """Test that handlers can be retrieved."""
        registry = EffectHandlerRegistry()
        io_handler = IOHandler()
        db_handler = DatabaseHandler()

        # Register the handlers
        registry.register_handler(IO, io_handler)
        registry.register_handler(Database, db_handler)

        # Check that the handlers can be retrieved
        assert registry.get_handler(IO) == io_handler
        assert registry.get_handler(Database) == db_handler
        assert registry.get_handler(Network) is None

    def test_handle_effect(self):
        """Test that effects can be handled."""
        registry = EffectHandlerRegistry()

        # Create a mock handler
        handler = MagicMock()
        handler.handle.return_value = "test content"

        # Register the handler
        registry.register_handler(IO, handler)

        # Handle an effect
        result = registry.handle_effect(IO, "read_file", None, "test.txt")

        # Check that the handler was called correctly
        handler.handle.assert_called_once_with(IO, "read_file", None, "test.txt")

        # Check that the result is correct
        assert result == "test content"

    def test_handle_effect_no_handler(self):
        """Test that an error is raised when no handler is available."""
        registry = EffectHandlerRegistry()

        # Try to handle an effect with no handler
        with pytest.raises(ValueError):
            registry.handle_effect(IO, "read_file", None, "test.txt")

    def test_clear(self):
        """Test that the registry can be cleared."""
        registry = EffectHandlerRegistry()
        handler = IOHandler()

        # Register the handler
        registry.register_handler(IO, handler)

        # Check that the handler was registered
        assert registry.get_handler(IO) == handler

        # Clear the registry
        registry.clear()

        # Check that the handler was removed
        assert registry.get_handler(IO) is None


class TestEffectRuntime:
    """Tests for the EffectRuntime class."""

    def test_register_handler(self):
        """Test that handlers can be registered."""
        runtime = EffectRuntime()
        handler = MagicMock()

        # Register the handler
        runtime.register_handler(IO, handler)

        # Check that the handler was registered
        assert runtime._registry.get_handler(IO) == handler

    def test_with_handler(self):
        """Test that handlers can be used temporarily."""
        runtime = EffectRuntime()
        handler = MagicMock()
        handler.can_handle.return_value = True
        handler.handle.return_value = "test content"

        # Use the handler temporarily
        with runtime.with_handler(handler):
            # Create an effect
            effect = Effect(IO, "read_file", None, "test.txt")

            # Run the effect
            result = runtime.run(effect)

            # Check that the handler was called correctly
            handler.can_handle.assert_called_with(IO)
            handler.handle.assert_called_once()

            # Check that the result is correct
            assert result == "test content"

    def test_with_context(self):
        """Test that contexts can be used temporarily."""
        runtime = EffectRuntime()

        # Use a context temporarily
        with runtime.with_context(user_id="123", request_id="456"):
            # Get the context
            context = runtime.get_context()

            # Check that the context is correct
            assert context == {"user_id": "123", "request_id": "456"}

    def test_get_context(self):
        """Test that contexts can be retrieved."""
        runtime = EffectRuntime()

        # Check that the context is empty by default
        assert runtime.get_context() == {}

        # Use a context temporarily
        with runtime.with_context(user_id="123"):
            # Check that the context is correct
            assert runtime.get_context() == {"user_id": "123"}

            # Use another context temporarily
            with runtime.with_context(request_id="456"):
                # Check that the contexts are merged
                assert runtime.get_context() == {"user_id": "123", "request_id": "456"}

            # Check that the context is restored
            assert runtime.get_context() == {"user_id": "123"}

        # Check that the context is empty again
        assert runtime.get_context() == {}

    def test_run(self):
        """Test that effects can be run."""
        runtime = EffectRuntime()

        # Create a mock handler
        handler = MagicMock()
        handler.can_handle.return_value = True
        handler.handle.return_value = "test content"

        # Create an effect
        effect = Effect(IO, "read_file", None, "test.txt")

        # Run the effect with the handler
        result = runtime.run(effect, [handler])

        # Check that the handler was called correctly
        handler.can_handle.assert_called_with(IO)
        handler.handle.assert_called_once()

        # Check that the result is correct
        assert result == "test content"

    def test_perform(self):
        """Test that effects can be performed."""
        runtime = EffectRuntime()

        # Create a mock handler
        handler = MagicMock()
        handler.can_handle.return_value = True
        handler.handle.return_value = "test content"

        # Register the handler
        runtime._registry.register_handler(IO, handler)

        # Perform an effect
        result = runtime.perform(IO, "read_file", "test.txt")

        # Check that the result is correct
        assert result == "test content"


class TestEffectDecorators:
    """Tests for the effect decorators."""

    def test_effects_decorator(self):
        """Test that the @effects decorator works correctly."""

        # Define a function with effects
        @effects([IO, Database])
        def process_data(file_path):
            return f"Processing {file_path}"

        # Check that the function has the correct effects
        assert hasattr(process_data, "__effects__")
        assert process_data.__effects__ == {IO, Database}

        # Check that the function still works correctly
        result = process_data("test.txt")
        assert result == "Processing test.txt"

    def test_effect_handler_decorator(self):
        """Test that the @effect_handler decorator works correctly."""

        # Define a function as an effect handler
        @effect_handler(IO)
        def handle_read_file(path):
            return f"Content of {path}"

        # Check that the function has the correct handled effect
        assert hasattr(handle_read_file, "__handled_effect__")
        assert handle_read_file.__handled_effect__ == IO

        # Check that the function still works correctly
        result = handle_read_file("test.txt")
        assert result == "Content of test.txt"


class TestEffectSystem:
    """Integration tests for the effect system."""

    def test_perform_effect(self):
        """Test that the perform_effect function works correctly."""
        # Create a mock handler
        handler = MagicMock()
        handler.can_handle.return_value = True
        handler.handle.return_value = "test content"

        # Get the runtime
        runtime = get_runtime()

        # Register the handler
        runtime._registry.register_handler(IO, handler)

        # Perform an effect
        result = perform_effect(IO, "read_file", "test.txt")

        # Check that the result is correct
        assert result == "test content"

    def test_run_with_effects(self):
        """Test that the run_with_effects function works correctly."""
        # Create a mock handler
        handler = MagicMock()
        handler.can_handle.return_value = True
        handler.handle.return_value = "test content"

        # Create an effect
        effect = Effect(IO, "read_file", None, "test.txt")

        # Run the effect with the handler
        result = run_with_effects(effect, [handler])

        # Check that the handler was called correctly
        handler.can_handle.assert_called_with(IO)
        handler.handle.assert_called_once()

        # Check that the result is correct
        assert result == "test content"
