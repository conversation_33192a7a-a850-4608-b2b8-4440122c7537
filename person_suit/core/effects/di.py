"""Dependency Injection Registration for Effects Subsystem.

Registers effect runtime, core effect handlers, and advanced effect handlers with the DI container.
Aligns with v0.3 unified paradigm and design documentation for modular, context-aware effect orchestration.

References:
- docs/future/unified_paradigm/v0.3/unified_paradigm_v0.3.md
- docs/design/Event_Effect_Log_Service_Design.md
- docs/design/Context_Management_Design.md
"""

from .core.runtime import EffectRuntime
from .handlers.advanced.differential import DifferentialHandler
from .handlers.advanced.entanglement import EntanglementHandler
from .handlers.advanced.interference import InterferenceHandler
from .handlers.advanced.probabilistic import (
    ProbabilisticHandler,
)
from .handlers.advanced.tensor import TensorHandler
from .handlers.core.computation import ComputationEffectHandler
from .handlers.core.database import DatabaseEffectHandler
from .handlers.core.io import IOEffectHandler
from .handlers.core.memory import MemoryEffectHandler
from .handlers.core.network import NetworkEffectHandler
from .handlers.core.state import StateEffect<PERSON>and<PERSON>
from ..infrastructure.dependency_injection import (
    ServiceLifetime,
    ServiceProvider,
)


def register_with_container(container: ServiceProvider) -> None:
    """Register effects subsystem components with the DI container.

    Args:
        container: The DI service provider/container.
    """
    # Register Effect Runtime (singleton)
    container.register(EffectRuntime, EffectRuntime, lifetime=ServiceLifetime.SINGLETON)
    # Register core effect handlers (transient)
    container.register(
        IOEffectHandler, IOEffectHandler, lifetime=ServiceLifetime.TRANSIENT
    )
    container.register(
        DatabaseEffectHandler, DatabaseEffectHandler, lifetime=ServiceLifetime.TRANSIENT
    )
    container.register(
        ComputationEffectHandler,
        ComputationEffectHandler,
        lifetime=ServiceLifetime.TRANSIENT,
    )
    container.register(
        StateEffectHandler, StateEffectHandler, lifetime=ServiceLifetime.TRANSIENT
    )
    container.register(
        MemoryEffectHandler, MemoryEffectHandler, lifetime=ServiceLifetime.TRANSIENT
    )
    container.register(
        NetworkEffectHandler, NetworkEffectHandler, lifetime=ServiceLifetime.TRANSIENT
    )
    # Register advanced effect handlers (transient)
    container.register(TensorHandler, TensorHandler, lifetime=ServiceLifetime.TRANSIENT)
    container.register(
        ProbabilisticHandler, ProbabilisticHandler, lifetime=ServiceLifetime.TRANSIENT
    )
    container.register(
        DifferentialHandler, DifferentialHandler, lifetime=ServiceLifetime.TRANSIENT
    )
    container.register(
        EntanglementHandler, EntanglementHandler, lifetime=ServiceLifetime.TRANSIENT
    )
    container.register(
        InterferenceHandler, InterferenceHandler, lifetime=ServiceLifetime.TRANSIENT
    )
    # TODO: Register additional effect/context handlers as needed
    # TODO: Support context/capability injection for advanced CAW scenarios
