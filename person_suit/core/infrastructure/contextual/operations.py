"""
Operations for contextual processing in CAW.

This module provides operations for working with contexts in the
Contextual Adaptive Wave Programming (CAW) paradigm.
"""

import contextvars
import logging
import uuid
from typing import Any, Dict, List, Optional

from ...caw.schemas import Context

logger = logging.getLogger(__name__)

# Use contextvars instead of threading.local
_current_caw_context: contextvars.ContextVar[Optional[Context]] = (
    contextvars.ContextVar("current_caw_context", default=None)
)


def get_current_context() -> Optional[Context]:
    """
    Get the current context for the current asyncio task context.

    Returns:
        The current context, or None if not set.
    """
    return _current_caw_context.get()


def set_current_context(context: Optional[Context]) -> contextvars.Token:
    """
    Set the current context for the current asyncio task context.

    Use the returned token with `reset_current_context` to restore the previous value.

    Args:
        context: The context to set, or None to unset.

    Returns:
        A token to reset the context variable.
    """
    return _current_caw_context.set(context)


def reset_current_context(token: contextvars.Token) -> None:
    """Reset the context variable to its previous value using the token."""
    _current_caw_context.reset(token)


def switch_context(name: str) -> bool:
    """
    Switch to a named context.

    Args:
        name: The name of the context to switch to

    Returns:
        True if the switch was successful, False otherwise
    """
    from person_suit.core.infrastructure.contextual.core import get_context_registry

    registry = get_context_registry()
    context = registry.get_context(name)
    if context is None:
        return False

    set_current_context(context)
    return True


def match_context(context1: Context, context2: Context) -> float:
    """
    Compute the match score between two contexts.

    Args:
        context1: The first context
        context2: The second context

    Returns:
        A float between 0.0 and 1.0 representing the match score
    """
    # Placeholder - needs implementation based on Context schema
    # Example: Consider domain, priority, and constraint overlap
    score = 0.0
    if not context1 or not context2:
        return 0.0
    if context1.domain == context2.domain:
        score += 0.5
    if context1.priority == context2.priority:
        score += 0.3
    # Constraint matching (e.g., subset or Jaccard index)
    constraints1 = set(context1.constraints or [])
    constraints2 = set(context2.constraints or [])
    if constraints1 or constraints2:
        intersection = len(constraints1.intersection(constraints2))
        union = len(constraints1.union(constraints2))
        if union > 0:
            score += 0.2 * (intersection / union)

    # Add matching for custom_context if needed
    return min(score, 1.0)  # Clamp score to 1.0


def combine_contexts(
    contexts: List[Context], weights: Optional[List[float]] = None
) -> Context:
    """
    Combine multiple contexts into a single context.

    Args:
        contexts: The contexts to combine
        weights: Optional weights for the contexts

    Returns:
        A combined context

    Raises:
        ValueError: If the contexts list is empty
    """
    if not contexts:
        raise ValueError("Cannot combine empty list of contexts")

    if weights is None:
        weights = [1.0 / len(contexts)] * len(contexts)

    if len(weights) != len(contexts):
        raise ValueError("Number of weights must match number of contexts")

    # Normalize weights
    total_weight = sum(weights)
    normalized_weights = [w / total_weight for w in weights]

    # Placeholder: For now, just return the first context or a new default
    # Proper combination requires defining how to merge domains, priorities,
    # constraints, custom_context, acf_setting etc., which can be complex.
    logger.warning(
        "combine_contexts using simplified placeholder logic (returns first context)."
    )
    return contexts[0]


def create_context(
    domain: str,
    priority: str,
    constraints: List[str],
    properties: Optional[Dict[str, Any]] = None,
) -> Context:
    """
    Create a new context.

    Args:
        domain: The domain of the context
        priority: The priority of the context
        constraints: The constraints of the context
        properties: Optional properties for the context

    Returns:
        A new context
    """
    # Adapt to use new Context constructor
    context = Context(
        context_id=str(uuid.uuid4()),  # Generate ID
        domain=domain,
        priority=priority,
        constraints=constraints,
        custom_context=properties or {},  # Use custom_context
        # Initialize other Context fields as needed
    )

    return context


def register_context(name: str, context: Context) -> None:
    """
    Register a context with the global registry.

    Args:
        name: The name of the context
        context: The context to register
    """
    from person_suit.core.infrastructure.contextual.core import get_context_registry

    registry = get_context_registry()
    registry.register_context(name, context)


def get_context(name: str) -> Optional[Context]:
    """
    Get a context from the global registry.

    Args:
        name: The name of the context

    Returns:
        The context, or None if not found
    """
    from person_suit.core.infrastructure.contextual.core import get_context_registry

    registry = get_context_registry()
    return registry.get_context(name)


def set_default_context(context: Context) -> None:
    """
    Set the default context in the global registry.

    Args:
        context: The default context
    """
    from person_suit.core.infrastructure.contextual.core import get_context_registry

    registry = get_context_registry()
    registry.set_default_context(context)


def get_default_context() -> Optional[Context]:
    """
    Get the default context from the global registry.

    Returns:
        The default context, or None if not set
    """
    from person_suit.core.infrastructure.contextual.core import get_context_registry

    registry = get_context_registry()
    return registry.get_default_context()
