"""
Decorators for contextual processing in CAW.

This module provides decorators for working with context-sensitive
processing in the Contextual Adaptive Wave Programming (CAW) paradigm.
"""

import functools
import inspect
from typing import Any, Callable, Optional, TypeVar, Union

# Fix import
# from person_suit.core.infrastructure.wave.core import Context
from ...caw.schemas import Context
from .operations import (
    get_current_context,
    reset_current_context,
    set_current_context,
)

# Type variables for generic functions
T = TypeVar("T")
R = TypeVar("R")

# WARNING: The decorators rely on get_current_context/set_current_context from
# operations.py, which uses thread-local storage. This is generally unsafe for asyncio.
# These decorators need refactoring to work with explicit context passing or contextvars.


def contextual_processor(
    func: Callable[[Any, Context], R],
) -> Callable[[Any, Optional[Context]], R]:
    """
    Decorator that marks a function as a contextual processor.

    A contextual processor is a function that processes information
    differently based on the context.

    Args:
        func: The function to mark as a contextual processor

    Returns:
        A context-aware version of the function
    """

    @functools.wraps(func)
    def wrapper(value: Any, context: Optional[Context] = None) -> R:
        # If context is not provided, use the current context from contextvars
        if context is None:
            context = get_current_context()
            if context is None:
                raise ValueError(
                    "No context provided and no current context set via contextvars"
                )
        return func(value, context)

    wrapper.__contextual_processor__ = True
    return wrapper


def with_context(
    context: Union[Context, str],
) -> Callable[[Callable[..., R]], Callable[..., R]]:
    """
    Decorator that runs a function with a specific context.

    Args:
        context: The context to use, either a Context object or a context name

    Returns:
        A decorator that runs a function with the specified context
    """

    def decorator(func: Callable[..., R]) -> Callable[..., R]:
        # Resolve context name eagerly if it's a string
        resolved_ctx: Optional[Context] = None
        if isinstance(context, str):
            from person_suit.core.infrastructure.contextual.core import (
                get_context_registry,
            )

            registry = get_context_registry()
            resolved_ctx = registry.get_context(context)
            if resolved_ctx is None:
                raise ValueError(
                    f"Context not found during decorator definition: {context}"
                )
        elif isinstance(context, Context):
            resolved_ctx = context
        else:
            raise TypeError("context argument must be a Context object or string name")

        ctx_to_set = resolved_ctx  # Keep resolved context

        if inspect.iscoroutinefunction(func):

            @functools.wraps(func)
            async def async_wrapper(*args: Any, **kwargs: Any) -> R:
                # Set context using contextvars for the duration of this task
                token = set_current_context(ctx_to_set)
                try:
                    # Run the async function within the modified context
                    return await func(*args, **kwargs)
                finally:
                    reset_current_context(token)  # Reset context

            return async_wrapper
        else:

            @functools.wraps(func)
            def sync_wrapper(*args: Any, **kwargs: Any) -> R:
                # For sync functions, contextvars works similarly but within the thread
                token = set_current_context(ctx_to_set)
                try:
                    return func(*args, **kwargs)
                finally:
                    reset_current_context(token)  # Reset context

            return sync_wrapper

    return decorator


def context_sensitive(func: Callable[..., R]) -> Callable[..., R]:
    """
    Decorator that makes a function context-sensitive.

    A context-sensitive function can behave differently based on
    the current context.

    Args:
        func: The function to make context-sensitive

    Returns:
        A context-sensitive version of the function
    """
    sig = inspect.signature(func)
    accepts_context = "context" in sig.parameters

    if inspect.iscoroutinefunction(func):

        @functools.wraps(func)
        async def async_wrapper(*args: Any, **kwargs: Any) -> R:
            # Get context from contextvars
            current_ctx = get_current_context()
            # Inject context if function accepts it and it's not already provided
            if accepts_context and "context" not in kwargs:
                kwargs["context"] = current_ctx
            return await func(*args, **kwargs)

        async_wrapper.__context_sensitive__ = True
        return async_wrapper
    else:

        @functools.wraps(func)
        def sync_wrapper(*args: Any, **kwargs: Any) -> R:
            current_ctx = get_current_context()
            if accepts_context and "context" not in kwargs:
                kwargs["context"] = current_ctx
            return func(*args, **kwargs)

        sync_wrapper.__context_sensitive__ = True
        return sync_wrapper


def context_switch(
    condition: Callable[..., bool],
    true_context: Union[Context, str],
    false_context: Optional[Union[Context, str]] = None,
) -> Callable[[Callable[..., R]], Callable[..., R]]:
    """
    Decorator that switches context based on a condition.

    Args:
        condition: A function that returns True or False
        true_context: The context to use if the condition is True
        false_context: The context to use if the condition is False

    Returns:
        A decorator that switches context based on the condition
    """
    # Resolve context names eagerly
    resolved_true_ctx: Optional[Context] = None
    if isinstance(true_context, str):
        from person_suit.core.infrastructure.contextual.core import get_context_registry

        registry = get_context_registry()
        resolved_true_ctx = registry.get_context(true_context)
        if resolved_true_ctx is None:
            raise ValueError(
                f"True context not found during decorator definition: {true_context}"
            )
    elif isinstance(true_context, Context):
        resolved_true_ctx = true_context
    else:
        raise TypeError("true_context argument must be a Context object or string name")

    resolved_false_ctx: Optional[Context] = None
    if isinstance(false_context, str):
        from person_suit.core.infrastructure.contextual.core import get_context_registry

        registry = get_context_registry()
        resolved_false_ctx = registry.get_context(false_context)
        if resolved_false_ctx is None:
            raise ValueError(
                f"False context not found during decorator definition: {false_context}"
            )
    elif isinstance(false_context, Context):
        resolved_false_ctx = false_context
    elif false_context is not None:
        raise TypeError(
            "false_context argument must be None, a Context object, or string name"
        )

    ctx_true = resolved_true_ctx
    ctx_false = resolved_false_ctx

    def decorator(func: Callable[..., R]) -> Callable[..., R]:
        is_async = inspect.iscoroutinefunction(func)

        @functools.wraps(func)
        async def async_wrapper(*args: Any, **kwargs: Any) -> R:
            cond_result = condition(*args, **kwargs)
            # Await condition if it's a coroutine function
            if inspect.isawaitable(cond_result):
                cond_result = await cond_result

            ctx_to_set = ctx_true if cond_result else ctx_false
            if ctx_to_set is None:
                return await func(*args, **kwargs)  # Run in original context

            token = set_current_context(ctx_to_set)
            try:
                return await func(*args, **kwargs)
            finally:
                reset_current_context(token)

        @functools.wraps(func)
        def sync_wrapper(*args: Any, **kwargs: Any) -> R:
            cond_result = condition(*args, **kwargs)
            ctx_to_set = ctx_true if cond_result else ctx_false
            if ctx_to_set is None:
                return func(*args, **kwargs)

            token = set_current_context(ctx_to_set)
            try:
                return func(*args, **kwargs)
            finally:
                reset_current_context(token)

        return async_wrapper if is_async else sync_wrapper

    return decorator
