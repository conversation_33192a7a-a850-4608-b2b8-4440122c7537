"""
Integration module for contextual processing in CAW.

This module provides integration points for the contextual processing
module with other components of the CAW system.
"""

from typing import Any, Dict, List, Optional

# Fix import
# from person_suit.core.infrastructure.wave.core import Context
from ...caw.schemas import Context
from .core import (
    ContextProvider,
    get_context_registry,
)

# Registry of context providers
_context_provider_registry: Dict[str, ContextProvider] = {}


def register_context_provider(name: str, provider: ContextProvider) -> None:
    """
    Register a context provider.

    Args:
        name: The name of the provider
        provider: The context provider
    """
    _context_provider_registry[name] = provider


def get_context_provider(name: str) -> Optional[ContextProvider]:
    """
    Get a context provider by name.

    Args:
        name: The name of the provider

    Returns:
        The context provider, or None if not found
    """
    return _context_provider_registry.get(name)


def get_all_context_providers() -> Dict[str, ContextProvider]:
    """
    Get all registered context providers.

    Returns:
        A dictionary of all registered context providers
    """
    return _context_provider_registry.copy()


def get_context_from_provider(provider_name: str, **kwargs: Any) -> Optional[Context]:
    """
    Get a context from a specific provider.

    Args:
        provider_name: The name of the provider
        **kwargs: Parameters for context creation

    Returns:
        The context, or None if the provider is not found
    """
    provider = get_context_provider(provider_name)
    if provider is None:
        return None

    return provider.get_context(**kwargs)


def initialize_integration() -> None:
    """Initialize the contextual processing integration module."""
    # Register standard contexts
    registry = get_context_registry()

    # Standard context
    standard_context = Context("standard", "normal", [])
    registry.register_context("standard", standard_context)
    registry.set_default_context(standard_context)

    # Work context
    work_context = Context(
        "work", "efficiency", ["professional", "formal", "task-oriented"]
    )
    registry.register_context("work", work_context)

    # Personal context
    personal_context = Context(
        "personal", "enjoyment", ["casual", "emotional", "relationship-oriented"]
    )
    registry.register_context("personal", personal_context)

    # Emergency context
    emergency_context = Context(
        "emergency", "safety", ["urgent", "critical", "time-sensitive"]
    )
    registry.register_context("emergency", emergency_context)

    # Remove initialization of current context - should be handled per task/request
    # from person_suit.core.infrastructure.contextual.operations import set_current_context
    # set_current_context(standard_context)


class StandardContextProvider(ContextProvider):
    """
    Standard implementation of a context provider.

    This provider provides access to the standard contexts registered
    in the context registry.
    """

    def __init__(self):
        """Initialize the standard context provider."""
        self.registry = get_context_registry()

    def get_context(self, **kwargs: Any) -> Context:
        """
        Get a context based on the provided parameters.

        Args:
            name: The name of the context to get
            **kwargs: Additional parameters (ignored)

        Returns:
            The requested context, or the default context if not found

        Raises:
            ValueError: If the name parameter is not provided
        """
        name = kwargs.get("name")
        if name is None:
            raise ValueError("Name parameter is required")

        context = self.registry.get_context(name)
        if context is None:
            return self.registry.get_default_context() or Context(
                "default", "normal", []
            )

        return context

    def get_available_contexts(self) -> List[str]:
        """
        Get a list of available context names.

        Returns:
            A list of available context names
        """
        return list(self.registry.get_all_contexts().keys())

    def supports_context(self, name: str) -> bool:
        """
        Check if the provider supports a specific context.

        Args:
            name: The name of the context

        Returns:
            True if the provider supports the context, False otherwise
        """
        return name in self.registry.get_all_contexts()


# Register the standard context provider
register_context_provider("standard", StandardContextProvider())
