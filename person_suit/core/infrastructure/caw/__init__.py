"""
Core CAW (Contextual Adaptive Wave) Implementation

This module provides the foundational implementation of the CAW paradigm,
including wave-particle duality representations, context management, and
adaptive processing components.
"""

from ...caw.particle_state import ImmutableHypergraph as ParticleState
from ...caw.dual_information import DualInformation
from ...caw.schemas import Context
from ..wave.transformer import Transformer

from .caw_processor import CAWProcessor
from .wave_state import WaveState

__all__ = [
    "CAWProcessor",
    "WaveState",
    "ParticleState",
    "DualInformation",
    "Context",
    "Transformer",
]
