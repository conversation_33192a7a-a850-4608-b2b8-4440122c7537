# -*- coding: utf-8 -*-
"""
File: person_suit/core/infrastructure/dependency_injection/containers.py
Purpose: Dependency Injection containers for the Person Suit core components.

NOTE: This file was moved from core/containers.py during refactoring.
      Imports may need adjustment.

Uses the `dependency-injector` library to manage the creation and wiring of
services, actors, and other components.
"""

import asyncio  # Added for example usage
import logging  # Added for placeholder logging
import os  # Added for example usage

from dependency_injector import containers, providers

# Import interfaces/placeholders
from ...actors.central_state_actor import (
    CentralStateActor,
    EventLogServiceInterface,
    NotificationServiceInterface,
    StateStorageServiceInterface,
)

# Import concrete service implementations where available
from ...capabilities.validator import CapabilityValidator
from ...caw.dual_information import (
    DualInformation,
)
from ...caw.particle_state import ConcreteParticleState
from ...caw.wave_state import ConcreteWaveState
from ...effects.transformation_logic import StateTransformationLogic
from ...event_logging.arango_log_service import ArangoDBEventLogService
from ...notification.redis_notification_service import (
    RedisNotificationService,  # Import concrete service
)
from ...state_storage.hybrid_storage_service import (
    HybridStateStorageService,
)

# --- Placeholder Implementations for Missing Services --- #


class PlaceholderNotificationService(NotificationServiceInterface):
    async def publish(self, notification):
        logging.warning("PlaceholderNotificationService.publish called.")
        print(f"[DI NOTIF SVC] Publishing: {notification}")
        pass


# --- Core Container Definition --- #


class CoreContainer(containers.DeclarativeContainer):
    """DI Container for core CAW services and actors."""

    config = providers.Configuration(
        strict=True,
        config_keys=[
            "arangodb",
            "snapshot_storage",
            "redis",
            "particle_snapshot_collection",
        ],
    )

    # --- Service Providers --- #

    capability_validator = providers.Singleton(CapabilityValidator)

    event_log_service: providers.Provider[EventLogServiceInterface] = (
        providers.Singleton(
            ArangoDBEventLogService,
            db_host=config.arangodb.host,
            db_name=config.arangodb.db_name,
            db_user=config.arangodb.user,
            db_password=config.arangodb.password,
            collection_name=config.arangodb.event_log_collection.name,
            snapshot_meta_collection=config.arangodb.event_log_collection.snapshot_meta_name,
        )
    )

    # StateTransformationLogic requires ACFManager and ResourceMonitor
    # Assuming these are registered elsewhere (e.g., in ApplicationContainer or passed explicitly)
    # Option 1: Declare as required dependencies (if using wiring)
    # acf_manager = providers.Dependency(instance_of=ACFManager)
    # resource_monitor = providers.Dependency(instance_of=CawResourceMonitor)
    # state_transformation_logic = providers.Singleton(
    #     StateTransformationLogic,
    #     acf_manager=acf_manager,
    #     resource_monitor=resource_monitor,
    #     config=config.optional('state_transform') # Example optional config
    # )
    # Option 2: Assume they will be injected via ApplicationContainer wiring (simpler if possible)
    # Placeholder - This needs to be resolved based on how ACFManager/ResourceMonitor are provided
    state_transformation_logic = providers.Singleton(StateTransformationLogic)

    state_storage_service: providers.Provider[StateStorageServiceInterface] = (
        providers.Singleton(
            HybridStateStorageService,
            db_host=config.arangodb.host,
            db_name=config.arangodb.db_name,
            db_user=config.arangodb.user,
            db_password=config.arangodb.password,
            particle_collection_name=config.particle_snapshot_collection.name,
            tensor_storage_path=config.snapshot_storage.tensor_base_path,
        )
    )

    notification_service: providers.Provider[NotificationServiceInterface] = (
        providers.Singleton(
            RedisNotificationService,
            redis_host=config.redis.host,
            redis_port=config.redis.port,
            redis_db=config.redis.db,
            channel_prefix=config.redis.channel_prefix,
        )
    )

    # --- Actor Providers --- #

    central_state_actor_factory = providers.Factory(
        CentralStateActor,
        capability_validator=capability_validator,
        event_log_service=event_log_service,
        notification_service=notification_service,
        state_transformation_logic=state_transformation_logic,
        state_storage_service=state_storage_service,
    )


# --- Application Container (Example) --- #


class ApplicationContainer(containers.DeclarativeContainer):
    """Main application container, potentially wiring multiple sub-containers."""

    config = providers.Configuration(
        strict=False,  # Allow extra keys if main config is loaded here
        yaml_files=["config.yml"],
    )

    # Provide core dependencies needed by CoreContainer
    # Assuming ACFManager and CawResourceMonitor are registered here or globally accessible
    # Option: Register them here
    # acf_manager = providers.Singleton(ACFManager)
    # caw_monitor_config = config.core.caw.resource_monitor # Example path
    # resource_monitor = providers.Singleton(CawResourceMonitor, config=caw_monitor_config)

    # Wire the core container, passing its specific config section
    # Pass ACF/Monitor if CoreContainer declares them as dependencies
    core = providers.Container(
        CoreContainer,
        config=config.core,
        # acf_manager=acf_manager, # Pass dependency if needed
        # resource_monitor=resource_monitor # Pass dependency if needed
    )

    # Example: Define how to get initial state (placeholder)
    initial_state_provider = providers.Factory(
        lambda particle_state, wave_state: DualInformation.create_initial(
            particle_state=particle_state, wave_state=wave_state
        )
    )

    # Register other infrastructure services here (needed by __main__.py setup_dependencies)
    # from person_suit.core.infrastructure.monitoring import MonitoringServiceInterface, AlertManagerInterface, AlertManager, get_monitoring_service
    # from person_suit.core.application.interfaces.events import IEventManager
    # from person_suit.core.infrastructure.events.event_manager import InfrastructureEventManager

    # Assuming these are obtained/created elsewhere for now
    # monitoring_service = providers.Singleton(get_monitoring_service, config=config.monitoring)
    # alert_manager = providers.Singleton(AlertManager)
    # event_manager = providers.Singleton(InfrastructureEventManager)

    # Register other components needed by __main__.py setup_dependencies
    # ... (Persona, InterfaceManager, CLIInterface, etc.)


# --- Usage Example (Conceptual - Requires config.yml) --- #

if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
    )

    config_content = """
core:
  arangodb:
    host: "http://localhost:8529"
    db_name: "_system"
    user: "root"
    password: ""
    event_log_collection:
      name: "caw_event_log_test"
      snapshot_meta_name: "caw_snapshot_meta_test"
  snapshot_storage:
    tensor_base_path: "./_tensor_snapshots"
  particle_snapshot_collection:
    name: "caw_particle_snapshots_test"
  redis:
    host: "localhost"
    port: 6379
    db: 0
    channel_prefix: "caw_notify:"
# Add other sections if ApplicationContainer loads full config
monitoring:
  # dummy config
  enabled: true 
"""
    if not os.path.exists("config.yml"):
        logging.warning("config.yml not found, creating dummy version for example.")
        with open("config.yml", "w") as f:
            f.write(config_content)

    container = ApplicationContainer()
    try:
        container.config.load()
    except Exception as e:
        logging.error(
            f"Failed to load configuration. Ensure config.yml exists and is valid: {e}"
        )
        exit(1)

    # It's better practice to register all dependencies via the container
    # instead of manually passing them after creation.
    # The wiring feature of dependency-injector helps manage this.
    # container.wire(modules=[__name__, 'person_suit.__main__']) # Example wiring

    async def run_example():
        logging.info("Setting up DI example with HYBRID storage service...")
        core_container = container.core()
        core_container.wire(modules=[__name__])  # Wire core container too if needed

        try:
            log_service_instance = (
                await core_container.event_log_service()
            )  # Use await if provider is async factory
            logging.info(
                f"Created EventLogService instance: {type(log_service_instance)}"
            )
            storage_service_instance = await core_container.state_storage_service()
            logging.info(
                f"Created StateStorageService instance: {type(storage_service_instance)}"
            )
            notify_service_instance = await core_container.notification_service()
            logging.info(
                f"Created NotificationService instance: {type(notify_service_instance)}"
            )

            # Basic Initial State
            initial_particle = ConcreteParticleState.empty()
            initial_wave = ConcreteWaveState.create_empty()
            initial_dual_info = DualInformation.create_initial(
                particle_state=initial_particle, wave_state=initial_wave
            )
            initial_state_ref = initial_dual_info.state_ref
            logging.info(f"Created initial state with ref: {initial_state_ref}")

            # Snapshot Test
            logging.info("Testing HYBRID snapshot storage...")
            snapshot_success = await storage_service_instance.store_snapshot(
                initial_dual_info
            )
            if snapshot_success:
                logging.info(
                    f"Snapshot stored successfully for ref: {initial_state_ref}."
                )
                loaded_state = await storage_service_instance.load_state(
                    initial_state_ref
                )
                if loaded_state and loaded_state.state_ref == initial_state_ref:
                    if (
                        loaded_state.particle_state is not None
                        and loaded_state.wave_state is not None
                    ):
                        logging.info(
                            "Snapshot loaded successfully and basic structure seems correct."
                        )
                    else:
                        logging.error("Loaded state is missing particle or wave state!")
                else:
                    logging.error(
                        f"Failed to load snapshot correctly for ref: {initial_state_ref}! Loaded: {loaded_state}"
                    )
            else:
                logging.error(f"Failed to store snapshot for ref: {initial_state_ref}!")

            # Delete Test
            logging.info(f"Attempting to delete snapshot {initial_state_ref}...")
            if await storage_service_instance.load_state(initial_state_ref):
                delete_success = await storage_service_instance.delete_snapshot(
                    initial_state_ref
                )
                if delete_success:
                    logging.info(
                        f"Snapshot {initial_state_ref} deleted successfully (ArangoDB doc and tensor file)."
                    )
                    reloaded_state = await storage_service_instance.load_state(
                        initial_state_ref
                    )
                    if reloaded_state is None:
                        logging.info(
                            "Verified: Snapshot could not be loaded after deletion."
                        )
                    else:
                        logging.error(
                            "Verification FAILED: Snapshot was loaded even after deletion!"
                        )
                else:
                    logging.error(f"Failed to delete snapshot {initial_state_ref}.")
            else:
                logging.warning(
                    f"Skipping delete test as snapshot {initial_state_ref} was not found initially."
                )

            # Close Redis pool
            if hasattr(notify_service_instance, "close"):
                await notify_service_instance.close()
                logging.info("Redis pool closed.")

        except Exception as ex:
            logging.exception(f"Error during DI example execution: {ex}")

    try:
        asyncio.run(run_example())
    except Exception as e:
        logging.exception(f"Error running DI example: {e}")

    print("\nConceptual DI example with HYBRID storage service executed.")
