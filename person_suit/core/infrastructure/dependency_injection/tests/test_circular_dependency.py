"""
Tests for circular dependency detection in the dependency injection system.
"""

import unittest
from typing import Protocol

from ..container import (
    ServiceCollection,
)
from ...error_handling import ConfigurationError


# Define interfaces
class IServiceA(Protocol):
    def do_something(self) -> str: ...


class IServiceB(Protocol):
    def do_something(self) -> str: ...


class IServiceC(Protocol):
    def do_something(self) -> str: ...


# Define implementations with circular dependencies
class ServiceA:
    def __init__(self, service_b: IServiceB):
        self.service_b = service_b

    def do_something(self) -> str:
        return "ServiceA" + self.service_b.do_something()


class ServiceB:
    def __init__(self, service_a: IServiceA):
        self.service_a = service_a

    def do_something(self) -> str:
        return "ServiceB"


# Define implementations with valid dependencies
class ValidServiceA:
    def do_something(self) -> str:
        return "ValidServiceA"


class ValidServiceB:
    def __init__(self, service_a: IServiceA):
        self.service_a = service_a

    def do_something(self) -> str:
        return "ValidServiceB" + self.service_a.do_something()


# Define implementations with indirect circular dependencies
class ServiceX:
    def __init__(self, service_y: "IServiceY"):
        self.service_y = service_y

    def do_something(self) -> str:
        return "ServiceX"


class ServiceY:
    def __init__(self, service_z: "IServiceZ"):
        self.service_z = service_z

    def do_something(self) -> str:
        return "ServiceY"


class ServiceZ:
    def __init__(self, service_x: "IServiceX"):
        self.service_x = service_x

    def do_something(self) -> str:
        return "ServiceZ"


class IServiceX(Protocol):
    def do_something(self) -> str: ...


class IServiceY(Protocol):
    def do_something(self) -> str: ...


class IServiceZ(Protocol):
    def do_something(self) -> str: ...


class CircularDependencyTests(unittest.TestCase):
    """Tests for circular dependency detection."""

    def test_direct_circular_dependency(self):
        """Test detection of direct circular dependencies."""
        # Create service collection
        services = ServiceCollection()

        # Register services with circular dependency
        services.add_singleton(IServiceA, ServiceA)
        services.add_singleton(IServiceB, ServiceB)

        # This should throw a ConfigurationError
        with self.assertRaises(ConfigurationError) as context:
            services.validate_dependencies()

        # Check error message
        error_message = str(context.exception)
        self.assertIn("Circular dependencies detected", error_message)
        self.assertIn("ServiceA -> ServiceB -> ServiceA", error_message)

    def test_indirect_circular_dependency(self):
        """Test detection of indirect circular dependencies."""
        # Create service collection
        services = ServiceCollection()

        # Register services with indirect circular dependency
        services.add_singleton(IServiceX, ServiceX)
        services.add_singleton(IServiceY, ServiceY)
        services.add_singleton(IServiceZ, ServiceZ)

        # This should throw a ConfigurationError
        with self.assertRaises(ConfigurationError) as context:
            services.validate_dependencies()

        # Check error message
        error_message = str(context.exception)
        self.assertIn("Circular dependencies detected", error_message)
        self.assertIn("ServiceX -> ServiceY -> ServiceZ -> ServiceX", error_message)

    def test_valid_dependency(self):
        """Test valid dependencies."""
        # Create service collection
        services = ServiceCollection()

        # Register services with valid dependencies
        services.add_singleton(IServiceA, ValidServiceA)
        services.add_singleton(IServiceB, ValidServiceB)

        # This should not throw an error
        try:
            services.validate_dependencies()
            provider = services.build_service_provider()
            service_b = provider.get_required_service(IServiceB)
            self.assertEqual(service_b.do_something(), "ValidServiceBValidServiceA")
        except ConfigurationError:
            self.fail("validate_dependencies() raised ConfigurationError unexpectedly!")


if __name__ == "__main__":
    unittest.main()
