"""
Tests for advanced features of the dependency injection system.

This module tests the advanced features of the dependency injection system, including
property and method injection, factory-based registration, configuration-driven
registration, and the fluent API.
"""

import json
import unittest
from typing import List, Protocol

from . import (
    ServiceCollection,
    ServiceProvider,
)
from ..configuration import (
    register_services_from_json,
)
from ..factory import (
    KeyedServiceFactory,
    ServiceFactory,
    add_factory,
    add_keyed_factory,
)
from ..injection import (
    PropertyInjector,
    inject,
    inject_method,
    inject_property,
)


# Define test interfaces and classes for all tests
class ILogger(Protocol):
    def log(self, message: str) -> None: ...


class IRepository(Protocol):
    def get_data(self) -> List[str]: ...


class IService(Protocol):
    def do_something(self) -> str: ...


class ConsoleLogger:
    def __init__(self):
        self.logs: List[str] = []

    def log(self, message: str) -> None:
        self.logs.append(message)


class Repository:
    def __init__(self, logger: ILogger):
        self.logger = logger
        self.data = ["item1", "item2", "item3"]

    def get_data(self) -> List[str]:
        self.logger.log("Getting data from repository")
        return self.data


class SimpleService:
    def __init__(self, repository: IRepository, logger: ILogger):
        self.repository = repository
        self.logger = logger

    def do_something(self) -> str:
        self.logger.log("Doing something")
        data = self.repository.get_data()
        return ", ".join(data)


# Define classes for property and method injection tests
@inject
class ServiceWithPropertyInjection:
    def __init__(self):
        self.repository = None
        self.logger = None

    @inject_property(IRepository)
    def repository(self) -> IRepository:
        return self._repository

    @repository.setter
    def repository(self, value: IRepository) -> None:
        self._repository = value

    @inject_property(ILogger)
    def logger(self) -> ILogger:
        return self._logger

    @logger.setter
    def logger(self, value: ILogger) -> None:
        self._logger = value

    def do_something(self) -> str:
        self.logger.log("Doing something with property injection")
        data = self.repository.get_data()
        return ", ".join(data)


@inject
class ServiceWithMethodInjection:
    def __init__(self):
        self.repository = None
        self.logger = None

    @inject_method([IRepository, ILogger])
    def initialize(self, repository: IRepository, logger: ILogger) -> None:
        self.repository = repository
        self.logger = logger
        self.logger.log("Service initialized with method injection")

    def do_something(self) -> str:
        if self.repository is None or self.logger is None:
            raise ValueError("Service not initialized")

        self.logger.log("Doing something with method injection")
        data = self.repository.get_data()
        return ", ".join(data)


# Define factories for factory tests
class ServiceFactory(ServiceFactory[IService]):
    def __init__(self, use_logging: bool = True):
        self.use_logging = use_logging

    def create(self, provider: ServiceProvider) -> IService:
        logger = provider.get_required_service(ILogger)

        if self.use_logging:
            logger.log("Creating service with factory")

        repository = provider.get_required_service(IRepository)
        return SimpleService(repository, logger)


class AdvancedFeaturesTests(unittest.TestCase):
    """Tests for advanced features of the dependency injection system."""

    def setUp(self):
        """Set up test environment."""
        self.services = ServiceCollection()

        # Register basic services for most tests
        self.services.add_singleton(ILogger, ConsoleLogger)
        self.services.add_scoped(IRepository, Repository)

    def test_property_injection(self):
        """Test property injection."""
        # Register service with property injection
        self.services.add_transient(IService, ServiceWithPropertyInjection)

        # Build service provider
        provider = self.services.build_service_provider()

        # Get service
        service = provider.get_required_service(IService)

        # Properties should not be injected yet
        self.assertIsNone(service.repository)
        self.assertIsNone(service.logger)

        # Inject properties
        PropertyInjector.inject(service, provider)

        # Properties should be injected now
        self.assertIsNotNone(service.repository)
        self.assertIsNotNone(service.logger)

        # Service should work
        result = service.do_something()
        self.assertEqual(result, "item1, item2, item3")

        # Logger should have log entries
        logger = provider.get_required_service(ILogger)
        self.assertIn("Doing something with property injection", logger.logs)
        self.assertIn("Getting data from repository", logger.logs)

    def test_method_injection(self):
        """Test method injection."""
        # Register service with method injection
        self.services.add_transient(IService, ServiceWithMethodInjection)

        # Build service provider
        provider = self.services.build_service_provider()

        # Get service
        service = provider.get_required_service(IService)

        # Service should not be initialized yet
        self.assertIsNone(service.repository)
        self.assertIsNone(service.logger)

        # Inject method dependencies
        PropertyInjector.inject(service, provider)

        # Service should be initialized now
        self.assertIsNotNone(service.repository)
        self.assertIsNotNone(service.logger)

        # Service should work
        result = service.do_something()
        self.assertEqual(result, "item1, item2, item3")

        # Logger should have log entries
        logger = provider.get_required_service(ILogger)
        self.assertIn("Service initialized with method injection", logger.logs)
        self.assertIn("Doing something with method injection", logger.logs)

    def test_factory_registration(self):
        """Test factory-based registration."""
        # Create factory
        factory = ServiceFactory()

        # Register service with factory
        add_factory(self.services, IService, factory)

        # Build service provider
        provider = self.services.build_service_provider()

        # Get service
        service = provider.get_required_service(IService)

        # Service should be created and work
        result = service.do_something()
        self.assertEqual(result, "item1, item2, item3")

        # Logger should have log entries
        logger = provider.get_required_service(ILogger)
        self.assertIn("Creating service with factory", logger.logs)
        self.assertIn("Doing something", logger.logs)

    def test_keyed_factory(self):
        """Test keyed factory registration."""
        # Create keyed factory
        keyed_factory = KeyedServiceFactory[str, IService]()

        # Register implementations
        keyed_factory.register_factory(
            "simple",
            lambda provider: SimpleService(
                provider.get_required_service(IRepository),
                provider.get_required_service(ILogger),
            ),
        )

        keyed_factory.register_factory(
            "logging",
            lambda provider: SimpleService(
                provider.get_required_service(IRepository),
                ConsoleLogger(),  # Use a new logger instance
            ),
        )

        # Register keyed factory in services
        add_keyed_factory(self.services, IService, keyed_factory)

        # Build service provider
        provider = self.services.build_service_provider()

        # Get keyed factory
        factory = provider.get_required_service(KeyedServiceFactory)

        # Create services with different keys
        service1 = factory.create("simple", provider)
        service2 = factory.create("logging", provider)

        # Both services should work
        result1 = service1.do_something()
        result2 = service2.do_something()

        self.assertEqual(result1, "item1, item2, item3")
        self.assertEqual(result2, "item1, item2, item3")

        # service1 should use the shared logger, service2 should use its own
        self.assertIsNot(service1.logger, service2.logger)

    def test_configuration_registration(self):
        """Test configuration-driven registration."""
        # Create a temporary configuration file
        config = {
            "services": [
                {
                    "serviceType": "person_suit.core.infrastructure.dependency_injection.tests.test_advanced_features.IService",
                    "implementationType": "person_suit.core.infrastructure.dependency_injection.tests.test_advanced_features.SimpleService",
                    "lifetime": "singleton",
                }
            ]
        }

        # Register services from configuration
        register_services_from_json(self.services, json.dumps(config))

        # Build service provider
        provider = self.services.build_service_provider()

        # Get service
        service = provider.get_required_service(IService)

        # Service should be created and work
        result = service.do_something()
        self.assertEqual(result, "item1, item2, item3")

    def test_fluent_api(self):
        """Test fluent API for service registration."""
        # Register service using fluent API
        self.services.add_service(IService).with_implementation(
            SimpleService
        ).singleton()

        # Build service provider
        provider = self.services.build_service_provider()

        # Get service
        service = provider.get_required_service(IService)

        # Service should be created and work
        result = service.do_something()
        self.assertEqual(result, "item1, item2, item3")

        # Get service again, should be the same instance (singleton)
        service2 = provider.get_required_service(IService)
        self.assertIs(service, service2)

    def test_fluent_api_factory(self):
        """Test fluent API with factory function."""
        # Register service using fluent API with factory
        self.services.add_service(IService).with_factory(
            lambda provider: SimpleService(
                provider.get_required_service(IRepository),
                provider.get_required_service(ILogger),
            )
        ).transient()

        # Build service provider
        provider = self.services.build_service_provider()

        # Get services, should be different instances (transient)
        service1 = provider.get_required_service(IService)
        service2 = provider.get_required_service(IService)

        # Both services should work
        result1 = service1.do_something()
        result2 = service2.do_something()

        self.assertEqual(result1, "item1, item2, item3")
        self.assertEqual(result2, "item1, item2, item3")

        # Should be different instances
        self.assertIsNot(service1, service2)

    def test_fluent_api_instance(self):
        """Test fluent API with instance registration."""
        # Create an instance
        logger = provider = self.services.build_service_provider()
        repository = Repository(provider.get_required_service(ILogger))
        instance = SimpleService(repository, provider.get_required_service(ILogger))

        # Register instance using fluent API
        self.services.add_service(IService).as_instance(instance)

        # Build service provider
        provider = self.services.build_service_provider()

        # Get service, should be the same instance
        service = provider.get_required_service(IService)
        self.assertIs(service, instance)

        # Service should work
        result = service.do_something()
        self.assertEqual(result, "item1, item2, item3")


if __name__ == "__main__":
    unittest.main()
