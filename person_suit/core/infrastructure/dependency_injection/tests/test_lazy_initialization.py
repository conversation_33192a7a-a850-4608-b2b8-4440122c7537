"""
Tests for lazy initialization in the dependency injection system.
"""

import time
import unittest
from typing import List, Protocol

from . import (
    LazyActivationTracker,
    ServiceCollection,
)
from ..lazy import (
    add_lazy_scoped,
    add_lazy_singleton,
    add_lazy_transient,
)


# Define test interfaces
class IExpensiveService(Protocol):
    def get_data(self) -> str: ...
    def get_id(self) -> int: ...


class IUserService(Protocol):
    def process_user(self, user_id: int) -> str: ...
    def get_users(self) -> List[str]: ...


# Define implementations
class ExpensiveServiceImpl:
    def __init__(self):
        # Simulate expensive initialization
        time.sleep(0.01)
        self.id = id(self)

    def get_data(self) -> str:
        return "Data from expensive service"

    def get_id(self) -> int:
        return self.id


class UserService:
    def __init__(self, expensive_service: IExpensiveService):
        self.expensive_service = expensive_service

    def process_user(self, user_id: int) -> str:
        return f"User {user_id} processed with {self.expensive_service.get_data()}"

    def get_users(self) -> List[str]:
        return ["User1", "User2", "User3"]


class LazyInitializationTests(unittest.TestCase):
    """Tests for lazy initialization."""

    def setUp(self):
        """Set up test environment."""
        # Reset activation metrics before each test
        LazyActivationTracker.get_instance().reset_metrics()

    def test_lazy_singleton_initialization(self):
        """Test that lazy singleton services are only initialized when first used."""
        # Create service collection
        services = ServiceCollection()

        # Register a lazy singleton service
        add_lazy_singleton(services, IExpensiveService, ExpensiveServiceImpl)

        # Build service provider
        start_time = time.time()
        provider = services.build_service_provider()
        build_time = time.time() - start_time

        # Service should not be initialized yet
        metrics = LazyActivationTracker.get_instance().get_activation_metrics()
        self.assertEqual(
            len(metrics),
            0,
            "Service should not be initialized during provider creation",
        )

        # Get the service reference (should still not initialize)
        service_ref = provider.get_service(IExpensiveService)
        self.assertIsNotNone(service_ref, "Service reference should not be None")

        # Service should still not be initialized
        metrics = LazyActivationTracker.get_instance().get_activation_metrics()
        self.assertEqual(
            len(metrics), 0, "Service should not be initialized when getting reference"
        )

        # Use the service (should initialize)
        data = service_ref.get_data()
        self.assertEqual(
            data, "Data from expensive service", "Service should return correct data"
        )

        # Service should now be initialized
        metrics = LazyActivationTracker.get_instance().get_activation_metrics()
        self.assertEqual(len(metrics), 1, "Service should be initialized after use")
        self.assertTrue(
            IExpensiveService in metrics, "ExpensiveService should be in metrics"
        )
        self.assertEqual(
            metrics[IExpensiveService]["count"], 1, "Service should be initialized once"
        )

        # Get the service again (should use the same instance)
        service_ref2 = provider.get_service(IExpensiveService)
        service_id1 = service_ref.get_id()
        service_id2 = service_ref2.get_id()
        self.assertEqual(
            service_id1,
            service_id2,
            "Singleton service should return the same instance",
        )

        # Metrics should not change (no new initialization)
        new_metrics = LazyActivationTracker.get_instance().get_activation_metrics()
        self.assertEqual(
            new_metrics[IExpensiveService]["count"],
            1,
            "Service should still be initialized only once",
        )

    def test_lazy_transient_initialization(self):
        """Test that lazy transient services are initialized for each resolution."""
        # Create service collection
        services = ServiceCollection()

        # Register a lazy transient service
        add_lazy_transient(services, IExpensiveService, ExpensiveServiceImpl)

        # Build service provider
        provider = services.build_service_provider()

        # Get and use the service
        service1 = provider.get_service(IExpensiveService)
        data1 = service1.get_data()
        id1 = service1.get_id()

        # Get and use another instance
        service2 = provider.get_service(IExpensiveService)
        data2 = service2.get_data()
        id2 = service2.get_id()

        # Data should be the same, but IDs should be different
        self.assertEqual(data1, data2, "Services should return the same data")
        self.assertNotEqual(
            id1, id2, "Transient services should return different instances"
        )

        # Metrics should show two initializations
        metrics = LazyActivationTracker.get_instance().get_activation_metrics()
        self.assertEqual(
            metrics[IExpensiveService]["count"],
            2,
            "Two service instances should be initialized",
        )

    def test_lazy_service_as_dependency(self):
        """Test lazy services used as dependencies."""
        # Create service collection
        services = ServiceCollection()

        # Register a lazy singleton service and a regular service that depends on it
        add_lazy_singleton(services, IExpensiveService, ExpensiveServiceImpl)
        services.add_singleton(IUserService, UserService)

        # Build service provider
        provider = services.build_service_provider()

        # Get user service (expensive service should not be initialized yet)
        user_service = provider.get_required_service(IUserService)

        # Service should not be initialized yet
        metrics = LazyActivationTracker.get_instance().get_activation_metrics()
        self.assertEqual(
            len(metrics), 0, "Expensive service should not be initialized yet"
        )

        # Use user service (which should trigger expensive service initialization)
        result = user_service.process_user(123)
        self.assertIn("User 123", result, "User service should process user")
        self.assertIn(
            "Data from expensive service",
            result,
            "User service should use expensive service",
        )

        # Expensive service should now be initialized
        metrics = LazyActivationTracker.get_instance().get_activation_metrics()
        self.assertEqual(len(metrics), 1, "Expensive service should be initialized now")

    def test_lazy_scoped_services(self):
        """Test lazy scoped services."""
        # Create service collection
        services = ServiceCollection()

        # Register a lazy scoped service
        add_lazy_scoped(services, IExpensiveService, ExpensiveServiceImpl)

        # Build service provider
        provider = services.build_service_provider()

        # Create a scope
        with provider.create_scope() as scope:
            # Get the service from the scope
            scope_provider = scope.get_service_provider()
            service1 = scope_provider.get_service(IExpensiveService)
            id1 = service1.get_id()

            # Get the service again from the same scope
            service2 = scope_provider.get_service(IExpensiveService)
            id2 = service2.get_id()

            # Should be the same instance within a scope
            self.assertEqual(id1, id2, "Should get the same instance within a scope")

        # Create another scope
        with provider.create_scope() as scope2:
            # Get the service from the second scope
            scope2_provider = scope2.get_service_provider()
            service3 = scope2_provider.get_service(IExpensiveService)
            id3 = service3.get_id()

            # Should be a different instance in a different scope
            self.assertNotEqual(
                id1, id3, "Should get a different instance in different scope"
            )

        # Metrics should show two initializations (one per scope)
        metrics = LazyActivationTracker.get_instance().get_activation_metrics()
        self.assertEqual(
            metrics[IExpensiveService]["count"],
            2,
            "Two service instances should be initialized",
        )

    def test_lazy_activation_metrics(self):
        """Test activation metrics tracking."""
        # Create service collection
        services = ServiceCollection()

        # Register lazy services
        add_lazy_singleton(services, IExpensiveService, ExpensiveServiceImpl)

        # Build service provider
        provider = services.build_service_provider()

        # Use the service multiple times
        for _ in range(3):
            service = provider.get_service(IExpensiveService)
            service.get_data()

        # Get activation metrics
        metrics = LazyActivationTracker.get_instance().get_activation_metrics()

        # Check metrics content
        self.assertTrue(
            IExpensiveService in metrics, "ExpensiveService should be in metrics"
        )
        service_metrics = metrics[IExpensiveService]

        # Since it's a singleton, it should be initialized only once
        self.assertEqual(
            service_metrics["count"], 1, "Service should be initialized once"
        )

        # Check that all expected metrics are present
        self.assertIn(
            "avg_time", service_metrics, "Metrics should include average time"
        )
        self.assertIn("min_time", service_metrics, "Metrics should include min time")
        self.assertIn("max_time", service_metrics, "Metrics should include max time")
        self.assertIn(
            "first_activation",
            service_metrics,
            "Metrics should include first activation",
        )
        self.assertIn(
            "last_activation", service_metrics, "Metrics should include last activation"
        )

        # Times should be reasonable
        self.assertGreater(
            service_metrics["avg_time"], 0, "Average time should be positive"
        )
        self.assertGreaterEqual(
            service_metrics["min_time"], 0, "Min time should be non-negative"
        )
        self.assertGreaterEqual(
            service_metrics["max_time"],
            service_metrics["min_time"],
            "Max time should be >= min time",
        )


if __name__ == "__main__":
    unittest.main()
