from __future__ import annotations

# -*- coding: utf-8 -*-

"""
Person Suit Dependency Injection System.

This module provides a comprehensive dependency injection system for the
Person Suit architecture, including service registration, lifetime management,
and resolution capabilities.
"""

import logging  # Import logging for warnings
import threading

# Import typing
from typing import Optional, Type, TypeVar

# Import events interface
from ...application.interfaces.events_interface import IEventManager

# Example: Core Event Manager
# from person_suit.core.infrastructure.configuration import ConfigManager
# import person_suit.core.infrastructure.effects as effects_module
from ...events import InfrastructureEventManager

# Import configuration-driven registration components
from .configuration import (
    ConfigurationServiceRegistrar,
    ServiceRegistrationConfig,
    register_services_from_config,
    register_services_from_json,
)

# Import container components
from .container import ServiceCollection, ServiceDescriptor

# Import container access functions
from .container_access import create_service_collection, get_container

# Import dependency graph components
from .dependency_graph import DependencyAnalyzer, DependencyGraph, DependencyNode

# Import factory components
from .factory import (
    ActivatorServiceFactory,
    CompositeServiceFactory,
    DelegateServiceFactory,
    KeyedServiceFactory,
    ServiceFactory,
    add_factory,
    add_keyed_factory,
)

# Import fluent API components
from .fluent import FluentLifetimeRegistration, FluentServiceRegistration, add_service

# Import property and method injection components
from .injection import (
    PropertyInjector,
    inject,
    inject_instance,
    inject_method,
    inject_property,
)

# Import lazy initialization components
from .lazy import (
    LazyActivationTracker,
    LazyServiceDescriptor,
    LazyServiceProxy,
    add_lazy_scoped,
    add_lazy_singleton,
    add_lazy_transient,
)

# Import lifetime components
from .lifetime import (
    LifetimeManager,
    ScopedLifetime,
    ServiceLifetime,
    SingletonLifetime,
    TransientLifetime,
)

# Import provider components
from .provider import ServiceProvider, ServiceScope

# Meta-System Level Components
# from person_suit.meta_systems.analyst.core import AnalystCore


# Expose key functions and classes
__all__ = [
    # Container
    "ServiceDescriptor",
    "ServiceCollection",
    # Lifetime
    "ServiceLifetime",
    "TransientLifetime",
    "SingletonLifetime",
    "ScopedLifetime",
    "LifetimeManager",
    # Provider
    "ServiceProvider",
    "ServiceScope",
    # Lazy Initialization
    "LazyServiceDescriptor",
    "LazyServiceProxy",
    "LazyActivationTracker",
    "add_lazy_transient",
    "add_lazy_singleton",
    "add_lazy_scoped",
    # Dependency Graph
    "DependencyNode",
    "DependencyGraph",
    "DependencyAnalyzer",
    # Factory
    "ServiceFactory",
    "DelegateServiceFactory",
    "ActivatorServiceFactory",
    "KeyedServiceFactory",
    "CompositeServiceFactory",
    "add_factory",
    "add_keyed_factory",
    # Property and Method Injection
    "inject",
    "inject_property",
    "inject_method",
    "PropertyInjector",
    "inject_instance",
    # Configuration-Driven Registration
    "ServiceRegistrationConfig",
    "ConfigurationServiceRegistrar",
    "register_services_from_config",
    "register_services_from_json",
    # Fluent API
    "FluentServiceRegistration",
    "FluentLifetimeRegistration",
    "add_service",
    # Decorators - Keep these in __all__ so users can still import them
    # from the package, but they won't be imported by __init__ itself.
    "singleton",
    "transient",
    "scoped",
    "inject",
    "async_singleton",
    # Container Access
    "create_service_collection",
    "get_container",
]


# Convenience function to create a service collection
def create_service_collection() -> ServiceCollection:
    """Create a new service collection."""
    return ServiceCollection()


# --- Helper Factory Functions for DI ---


def _create_state_manager(provider: ServiceProvider) -> StateManager:
    log = logging.getLogger(__name__)
    try:
        config_manager = provider.get_required_service(IConfigManager)
        config_dict = config_manager.get_section("StateManager")
        if config_dict:
            try:
                config = StateManagerConfig(**config_dict)
                log.debug("Loaded StateManager config from source.")
            except TypeError as e:
                log.warning(
                    f"Error creating StateManagerConfig from dict: {e}. Using defaults."
                )
                config = StateManagerConfig()
        else:
            log.warning("'StateManager' config section not found. Using defaults.")
            config = StateManagerConfig()
    except Exception as e:  # Catch error resolving IConfigManager
        log.warning(
            f"Failed to get IConfigManager for StateManager config: {e}. Using defaults."
        )
        config = StateManagerConfig()

    return StateManager(
        event_manager=provider.get_required_service(IEventManager), config=config
    )


def _create_consistency_checker(provider: ServiceProvider) -> ConsistencyChecker:
    log = logging.getLogger(__name__)
    try:
        config_manager = provider.get_required_service(IConfigManager)
        config_dict = config_manager.get_section("ConsistencyChecker")
        if config_dict:
            try:
                config = ConsistencyCheckerConfig(**config_dict)
                log.debug("Loaded ConsistencyChecker config from source.")
            except TypeError as e:
                log.warning(
                    f"Error creating ConsistencyCheckerConfig from dict: {e}. Using defaults."
                )
                config = ConsistencyCheckerConfig()
        else:
            log.warning(
                "'ConsistencyChecker' config section not found. Using defaults."
            )
            config = ConsistencyCheckerConfig()
    except Exception as e:
        log.warning(
            f"Failed to get IConfigManager for ConsistencyChecker config: {e}. Using defaults."
        )
        config = ConsistencyCheckerConfig()

    return ConsistencyChecker(
        event_manager=provider.get_required_service(IEventManager),
        state_manager=provider.get_required_service(StateManager),
        config=config,
    )


def _create_synchronizer(provider: ServiceProvider) -> Synchronizer:
    log = logging.getLogger(__name__)
    try:
        config_manager = provider.get_required_service(IConfigManager)
        config_dict = config_manager.get_section("Synchronizer")
        if config_dict:
            try:
                config = SynchronizationConfig(**config_dict)
                log.debug("Loaded Synchronizer config from source.")
            except TypeError as e:
                log.warning(
                    f"Error creating SynchronizationConfig from dict: {e}. Using defaults."
                )
                config = SynchronizationConfig()
        else:
            log.warning("'Synchronizer' config section not found. Using defaults.")
            config = SynchronizationConfig()
    except Exception as e:
        log.warning(
            f"Failed to get IConfigManager for Synchronizer config: {e}. Using defaults."
        )
        config = SynchronizationConfig()

    return Synchronizer(
        event_manager=provider.get_required_service(IEventManager),
        state_manager=provider.get_required_service(StateManager),
        consistency_checker=provider.get_required_service(ConsistencyChecker),
        config=config,
    )


def _create_neurochemical_system(provider: ServiceProvider) -> NeurochemicalSystem:
    # Add config loading here if NeurochemicalSystem takes a config object later
    # log = logging.getLogger(__name__)
    # config = NeurochemicalConfig() # Default
    # try: ... load from config_manager ... except ...
    return NeurochemicalSystem(
        event_manager=provider.get_required_service(IEventManager)
        # config=config
    )


# --- End Helper Factory Functions ---

# Global container instance (managed by get_container)
_global_container_instance: Optional[ServiceProvider] = None
_global_container_lock = threading.RLock()  # Use RLock for reentrancy if needed


def get_container() -> ServiceProvider:
    """Gets the singleton ServiceProvider instance.

    Initializes it with a default ServiceCollection if not already created.
    """
    global _global_container_instance
    with _global_container_lock:
        if _global_container_instance is None:
            # Create a default service collection
            collection = create_service_collection()

            # --- Config Manager Registration ---
            from person_suit.core.application.interfaces.config import IConfigManager
            from person_suit.core.infrastructure.config import JsonConfigManager

            # Define path to config file (could come from env var or arg parser later)
            config_file_path = "config.json"
            try:
                config_manager_instance = JsonConfigManager(
                    config_path=config_file_path
                )
                collection.add_singleton(
                    IConfigManager, instance=config_manager_instance
                )
                print(f"[DI] Registered IConfigManager (using {config_file_path}).")
            except Exception as e:
                print(
                    f"[DI] WARNING: Failed to initialize/register IConfigManager: {e}. Using default configs."
                )
                # Optionally register a dummy/default config manager if needed as fallback
                # collection.add_singleton(IConfigManager, instance=DefaultConfigManager())
            # --- End Config Registration ---

            # --- Add Core Infrastructure Service Registrations ---
            # Import necessary interfaces and implementations
            from person_suit.core.application.interfaces.communication import (
                IChannelManager,
            )
            from person_suit.core.application.interfaces.events_interface import (
                IEventManager,
            )
            from person_suit.core.application.interfaces.registration import IRegistry
            from person_suit.core.events import InfrastructureEventManager
            from person_suit.core.infrastructure.communication import ChannelManager
            from person_suit.core.infrastructure.registration import (
                InfrastructureRegistry,
            )

            # Register Core Services as Singletons
            collection.add_singleton(
                IRegistry, implementation_type=InfrastructureRegistry
            )
            collection.add_singleton(
                IChannelManager, implementation_type=ChannelManager
            )
            collection.add_singleton(
                IEventManager, implementation_type=InfrastructureEventManager
            )
            print(
                "[DI] Registered core infrastructure services (Registry, ChannelManager, EventManager)."
            )
            # --- End Registrations ---

            # --- Add Meta-System Service Registrations (Folded Mind - Sync/SEM) ---
            # Import necessary classes (Adjust paths if necessary)
            from person_suit.core.application.interfaces.config import (
                IConfigManager,  # Make sure it's imported
            )
            from person_suit.meta_systems.persona_core.folded_mind.SEM.neurochemical.system import (
                NeurochemicalSystem,
            )
            from person_suit.meta_systems.persona_core.folded_mind.sync.consistency import (
                ConsistencyChecker,
                ConsistencyCheckerConfig,
            )
            from person_suit.meta_systems.persona_core.folded_mind.sync.state_manager import (
                StateManager,
                StateManagerConfig,
            )
            from person_suit.meta_systems.persona_core.folded_mind.sync.synchronizer import (  # Import sync config too
                SynchronizationConfig,
                Synchronizer,
            )

            log = logging.getLogger(__name__)  # Get logger for use in lambdas

            # Register Folded Mind components using factories where config/complex deps exist

            # StateManager Factory
            collection.add_singleton(
                StateManager,
                implementation_factory=lambda provider: _create_state_manager(provider),
            )

            # ConsistencyChecker Factory
            collection.add_singleton(
                ConsistencyChecker,
                implementation_factory=lambda provider: _create_consistency_checker(
                    provider
                ),
            )

            # Synchronizer Factory
            collection.add_singleton(
                Synchronizer,
                implementation_factory=lambda provider: _create_synchronizer(provider),
            )

            # NeurochemicalSystem Factory
            # Config loading omitted for now, but follows the same pattern if added
            collection.add_singleton(
                NeurochemicalSystem,
                implementation_factory=lambda provider: _create_neurochemical_system(
                    provider
                ),
            )

            print(
                "[DI] Registered Folded Mind services using factories (with config loading)."
            )
            # --- End Meta-System Registrations ---

            # TODO: Add default service registrations here if needed
            # collection.add_singleton(SomeDefaultService)

            # Build the service provider
            _global_container_instance = collection.build_service_provider()

            # TODO: Perform post-build actions if necessary (e.g., validation)

            print("[DI] Global ServiceProvider created.")  # Placeholder log

    return _global_container_instance


__all__.extend(
    ["create_service_collection", "get_container"]
)  # Add new functions to __all__
