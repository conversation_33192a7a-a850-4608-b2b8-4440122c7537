"""
Configuration-driven service registration for the Person Suit dependency injection system.

This module enables services to be registered from configuration files, providing a flexible
way to configure the dependency injection container without modifying code.
"""

import importlib
import json
from typing import Any, Dict, List, Optional, Type, TypeVar

from .container import (
    ServiceCollection,
)

T = TypeVar("T")


class ServiceRegistrationConfig:
    """Configuration for service registration."""

    def __init__(
        self,
        service_type: str,
        implementation_type: Optional[str] = None,
        lifetime: str = "transient",
        factory_type: Optional[str] = None,
        factory_method: Optional[str] = None,
        instance_key: Optional[str] = None,
    ):
        """
        Initialize service registration configuration.

        Args:
            service_type: Fully qualified name of the service type
            implementation_type: Fully qualified name of the implementation type
            lifetime: Service lifetime ('transient', 'singleton', or 'scoped')
            factory_type: Fully qualified name of the factory type
            factory_method: Name of the factory method
            instance_key: Key for locating an existing instance
        """
        self.service_type = service_type
        self.implementation_type = implementation_type
        self.lifetime = lifetime
        self.factory_type = factory_type
        self.factory_method = factory_method
        self.instance_key = instance_key

        # Validate configuration
        if not service_type:
            raise ValueError("Service type is required")

        if not implementation_type and not factory_type and not instance_key:
            raise ValueError(
                "Either implementation type, factory type, or instance key is required"
            )

        if factory_type and not factory_method:
            raise ValueError("Factory method is required when factory type is provided")

        if lifetime not in ["transient", "singleton", "scoped"]:
            raise ValueError(
                f"Invalid lifetime '{lifetime}'. Must be 'transient', 'singleton', or 'scoped'"
            )

    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> "ServiceRegistrationConfig":
        """
        Create a service registration configuration from a dictionary.

        Args:
            config_dict: Dictionary with configuration values

        Returns:
            Service registration configuration

        Raises:
            ValueError: If required fields are missing or invalid
        """
        return cls(
            service_type=config_dict.get("serviceType", ""),
            implementation_type=config_dict.get("implementationType"),
            lifetime=config_dict.get("lifetime", "transient"),
            factory_type=config_dict.get("factoryType"),
            factory_method=config_dict.get("factoryMethod"),
            instance_key=config_dict.get("instanceKey"),
        )

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert to dictionary.

        Returns:
            Dictionary representation
        """
        result = {"serviceType": self.service_type, "lifetime": self.lifetime}

        if self.implementation_type:
            result["implementationType"] = self.implementation_type

        if self.factory_type:
            result["factoryType"] = self.factory_type
            result["factoryMethod"] = self.factory_method

        if self.instance_key:
            result["instanceKey"] = self.instance_key

        return result


class ConfigurationServiceRegistrar:
    """Service registrar that registers services from configuration."""

    def __init__(self, instance_provider: Optional[Dict[str, Any]] = None):
        """
        Initialize configuration service registrar.

        Args:
            instance_provider: Optional dictionary of existing instances keyed by name
        """
        self.instance_provider = instance_provider or {}
        self._type_cache: Dict[str, Type] = {}

    def register_services(
        self,
        service_collection: ServiceCollection,
        configurations: List[ServiceRegistrationConfig],
    ) -> None:
        """
        Register services from configurations.

        Args:
            service_collection: Service collection to register services in
            configurations: Service registration configurations

        Raises:
            ValueError: If configuration is invalid
            ImportError: If a type cannot be imported
        """
        for config in configurations:
            self._register_service(service_collection, config)

    def register_services_from_dict(
        self,
        service_collection: ServiceCollection,
        config_dict: Dict[str, List[Dict[str, Any]]],
    ) -> None:
        """
        Register services from a configuration dictionary.

        Args:
            service_collection: Service collection to register services in
            config_dict: Dictionary with service configurations

        Raises:
            ValueError: If configuration is invalid
            ImportError: If a type cannot be imported
        """
        if "services" not in config_dict:
            raise ValueError("Configuration must contain a 'services' section")

        services_config = config_dict["services"]
        configurations = [
            ServiceRegistrationConfig.from_dict(service) for service in services_config
        ]
        self.register_services(service_collection, configurations)

    def register_services_from_json(
        self, service_collection: ServiceCollection, json_str: str
    ) -> None:
        """
        Register services from a JSON string.

        Args:
            service_collection: Service collection to register services in
            json_str: JSON string with service configurations

        Raises:
            ValueError: If configuration is invalid
            ImportError: If a type cannot be imported
            json.JSONDecodeError: If JSON is invalid
        """
        config_dict = json.loads(json_str)
        self.register_services_from_dict(service_collection, config_dict)

    def register_services_from_file(
        self, service_collection: ServiceCollection, file_path: str
    ) -> None:
        """
        Register services from a JSON file.

        Args:
            service_collection: Service collection to register services in
            file_path: Path to the JSON file

        Raises:
            ValueError: If configuration is invalid
            ImportError: If a type cannot be imported
            json.JSONDecodeError: If JSON is invalid
            FileNotFoundError: If the file does not exist
        """
        with open(file_path, "r") as f:
            json_str = f.read()

        self.register_services_from_json(service_collection, json_str)

    def _register_service(
        self, service_collection: ServiceCollection, config: ServiceRegistrationConfig
    ) -> None:
        """
        Register a service from configuration.

        Args:
            service_collection: Service collection to register the service in
            config: Service registration configuration

        Raises:
            ValueError: If configuration is invalid
            ImportError: If a type cannot be imported
        """
        # Get service type
        service_type = self._get_type(config.service_type)

        # Register based on configuration
        if config.implementation_type:
            # Register with implementation type
            implementation_type = self._get_type(config.implementation_type)

            if config.lifetime == "transient":
                service_collection.add_transient(service_type, implementation_type)
            elif config.lifetime == "singleton":
                service_collection.add_singleton(service_type, implementation_type)
            elif config.lifetime == "scoped":
                service_collection.add_scoped(service_type, implementation_type)

        elif config.factory_type:
            # Register with factory
            factory_type = self._get_type(config.factory_type)
            factory_method = getattr(factory_type, config.factory_method)

            if config.lifetime == "transient":
                service_collection.add_transient(
                    service_type, implementation_factory=factory_method
                )
            elif config.lifetime == "singleton":
                service_collection.add_singleton(
                    service_type, implementation_factory=factory_method
                )
            elif config.lifetime == "scoped":
                service_collection.add_scoped(
                    service_type, implementation_factory=factory_method
                )

        elif config.instance_key:
            # Register existing instance
            if config.instance_key not in self.instance_provider:
                raise ValueError(
                    f"Instance with key '{config.instance_key}' not found in instance provider"
                )

            instance = self.instance_provider[config.instance_key]
            service_collection.add_singleton(service_type, instance=instance)

    def _get_type(self, type_name: str) -> Type:
        """
        Get a type by its fully qualified name.

        Args:
            type_name: Fully qualified name of the type

        Returns:
            Type object

        Raises:
            ImportError: If the type cannot be imported
            AttributeError: If the type does not exist in the module
        """
        # Check cache first
        if type_name in self._type_cache:
            return self._type_cache[type_name]

        # Split module name and type name
        parts = type_name.rsplit(".", 1)
        if len(parts) != 2:
            raise ValueError(
                f"Invalid type name '{type_name}'. Must be in format 'module.TypeName'"
            )

        module_name, class_name = parts

        # Import module
        module = importlib.import_module(module_name)

        # Get type
        type_obj = getattr(module, class_name)

        # Cache type
        self._type_cache[type_name] = type_obj

        return type_obj


# Extension methods for ServiceCollection
def register_services_from_config(
    service_collection: ServiceCollection,
    config_file: str,
    instance_provider: Optional[Dict[str, Any]] = None,
) -> ServiceCollection:
    """
    Register services from a configuration file.

    Args:
        service_collection: Service collection
        config_file: Path to the configuration file
        instance_provider: Optional dictionary of existing instances keyed by name

    Returns:
        Service collection

    Raises:
        ValueError: If configuration is invalid
        ImportError: If a type cannot be imported
        json.JSONDecodeError: If JSON is invalid
        FileNotFoundError: If the file does not exist
    """
    registrar = ConfigurationServiceRegistrar(instance_provider)
    registrar.register_services_from_file(service_collection, config_file)
    return service_collection


def register_services_from_json(
    service_collection: ServiceCollection,
    json_str: str,
    instance_provider: Optional[Dict[str, Any]] = None,
) -> ServiceCollection:
    """
    Register services from a JSON string.

    Args:
        service_collection: Service collection
        json_str: JSON string with service configurations
        instance_provider: Optional dictionary of existing instances keyed by name

    Returns:
        Service collection

    Raises:
        ValueError: If configuration is invalid
        ImportError: If a type cannot be imported
        json.JSONDecodeError: If JSON is invalid
    """
    registrar = ConfigurationServiceRegistrar(instance_provider)
    registrar.register_services_from_json(service_collection, json_str)
    return service_collection
