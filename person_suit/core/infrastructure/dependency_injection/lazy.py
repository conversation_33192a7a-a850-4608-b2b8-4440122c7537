"""
Lazy initialization support for the Person Suit dependency injection system.

This module provides mechanisms for lazy service initialization, delaying the creation
of expensive services until they are first accessed, improving application startup time
and resource utilization.
"""

import datetime
import threading
import time
from typing import Any, Callable, Dict, Generic, List, Optional, Type, TypeVar, cast

from .container import (
    ServiceCollection,
    ServiceDescriptor,
)
from .provider import (
    ServiceProvider,
)
from ..error_handling import OperationalError

T = TypeVar("T")


class LazyServiceDescriptor(ServiceDescriptor):
    """Descriptor for a lazily initialized service."""

    def __init__(
        self,
        service_type: Type[T],
        implementation_type: Optional[Type] = None,
        implementation_factory: Optional[Callable[[ServiceProvider], T]] = None,
        lifetime: str = "transient",
    ):
        """
        Initialize lazy service descriptor.

        Args:
            service_type: Type of the service
            implementation_type: Type that implements the service
            implementation_factory: Factory function to create the service
            lifetime: Service lifetime ('transient', 'singleton', or 'scoped')
        """
        super().__init__(
            service_type=service_type,
            implementation_type=implementation_type,
            implementation_factory=implementation_factory,
            lifetime=lifetime,
        )
        self.is_lazy = True

    def get_implementation(self, provider: ServiceProvider) -> Any:
        """
        Get a lazy proxy for the service.

        Args:
            provider: Service provider to resolve dependencies

        Returns:
            Lazy proxy for the service
        """
        # Create a lazy proxy for the service
        return LazyServiceProxy(
            service_type=self.service_type, provider=provider, descriptor=self
        )


class LazyServiceProxy(Generic[T]):
    """Proxy for a lazily initialized service."""

    def __init__(
        self,
        service_type: Type[T],
        provider: ServiceProvider,
        descriptor: ServiceDescriptor,
    ):
        """
        Initialize lazy service proxy.

        Args:
            service_type: Type of the service
            provider: Service provider to resolve dependencies
            descriptor: Service descriptor
        """
        self._service_type = service_type
        self._provider = provider
        self._descriptor = descriptor
        self._instance: Optional[T] = None
        self._initialized = False
        self._lock = threading.RLock()

    def __getattr__(self, name: str) -> Any:
        """
        Get an attribute from the underlying service.

        Args:
            name: Attribute name

        Returns:
            Attribute value
        """
        # Initialize the service if it hasn't been initialized yet
        instance = self._get_instance()

        # Get the attribute from the instance
        try:
            return getattr(instance, name)
        except AttributeError:
            raise AttributeError(
                f"'{self._service_type.__name__}' object has no attribute '{name}'"
            )

    def _get_instance(self) -> T:
        """
        Get the underlying service instance, initializing it if necessary.

        Returns:
            Service instance
        """
        if not self._initialized:
            with self._lock:
                if not self._initialized:
                    self._instance = self._initialize()
                    self._initialized = True

        return cast(T, self._instance)

    def _initialize(self) -> T:
        """
        Initialize the underlying service.

        Returns:
            Service instance
        """
        # Track initialization time
        start_time = time.time()

        try:
            # Create the original descriptor without lazy initialization
            original_descriptor = ServiceDescriptor(
                service_type=self._descriptor.service_type,
                implementation_type=self._descriptor.implementation_type,
                implementation_factory=self._descriptor.implementation_factory,
                lifetime=self._descriptor.lifetime,
                instance=self._descriptor.instance,
            )

            # Get the actual implementation
            instance = original_descriptor.get_implementation(self._provider)

            # Track activation
            elapsed_time = (time.time() - start_time) * 1000  # Convert to milliseconds
            LazyActivationTracker.get_instance().track_activation(
                self._service_type, elapsed_time
            )

            return instance
        except Exception as e:
            # If initialization fails, wrap the exception with more context
            raise OperationalError(
                f"Failed to initialize lazy service '{self._service_type.__name__}': {str(e)}",
                cause=e,
            )


class LazyActivationTracker:
    """Tracks activation of lazy services."""

    _instance = None
    _lock = threading.Lock()

    @classmethod
    def get_instance(cls) -> "LazyActivationTracker":
        """
        Get the singleton instance of LazyActivationTracker.

        Returns:
            Singleton instance
        """
        with cls._lock:
            if cls._instance is None:
                cls._instance = LazyActivationTracker()
            return cls._instance

    def __init__(self):
        """Initialize lazy activation tracker."""
        self._activations: Dict[Type, List[Dict[str, Any]]] = {}
        self._metrics_cache: Dict[Type, Dict[str, Any]] = {}
        self._cache_valid = False
        self._metrics_lock = threading.RLock()

    def track_activation(self, service_type: Type, activation_time: float) -> None:
        """
        Track service activation.

        Args:
            service_type: Type of the service
            activation_time: Time taken to activate the service in milliseconds
        """
        with self._metrics_lock:
            if service_type not in self._activations:
                self._activations[service_type] = []

            # Record activation
            self._activations[service_type].append(
                {
                    "time": activation_time,
                    "timestamp": datetime.datetime.now().isoformat(),
                }
            )

            # Invalidate metrics cache
            self._cache_valid = False

    def get_activation_metrics(self) -> Dict[Type, Dict[str, Any]]:
        """
        Get activation metrics.

        Returns:
            Dictionary of activation metrics per service type
        """
        with self._metrics_lock:
            # If cache is valid, return cached metrics
            if self._cache_valid:
                return self._metrics_cache.copy()

            # Calculate metrics
            metrics: Dict[Type, Dict[str, Any]] = {}

            for service_type, activations in self._activations.items():
                if not activations:
                    continue

                count = len(activations)
                total_time = sum(a["time"] for a in activations)
                avg_time = total_time / count
                min_time = min(a["time"] for a in activations)
                max_time = max(a["time"] for a in activations)
                first_activation = activations[0]["timestamp"]
                last_activation = activations[-1]["timestamp"]

                metrics[service_type] = {
                    "count": count,
                    "avg_time": avg_time,
                    "min_time": min_time,
                    "max_time": max_time,
                    "first_activation": first_activation,
                    "last_activation": last_activation,
                }

            # Update cache
            self._metrics_cache = metrics
            self._cache_valid = True

            return metrics.copy()

    def reset_metrics(self) -> None:
        """Reset all activation metrics."""
        with self._metrics_lock:
            self._activations = {}
            self._metrics_cache = {}
            self._cache_valid = False


# Extension methods for ServiceCollection
def add_lazy_transient(
    service_collection: ServiceCollection,
    service_type: Type[T],
    implementation_type: Optional[Type] = None,
    implementation_factory: Optional[Callable[[ServiceProvider], T]] = None,
) -> ServiceCollection:
    """
    Register a lazily initialized transient service.

    Args:
        service_collection: Service collection
        service_type: Type of the service
        implementation_type: Type that implements the service
        implementation_factory: Factory function to create the service

    Returns:
        Service collection
    """
    descriptor = LazyServiceDescriptor(
        service_type=service_type,
        implementation_type=implementation_type,
        implementation_factory=implementation_factory,
        lifetime="transient",
    )
    return service_collection.add(descriptor)


def add_lazy_singleton(
    service_collection: ServiceCollection,
    service_type: Type[T],
    implementation_type: Optional[Type] = None,
    implementation_factory: Optional[Callable[[ServiceProvider], T]] = None,
) -> ServiceCollection:
    """
    Register a lazily initialized singleton service.

    Args:
        service_collection: Service collection
        service_type: Type of the service
        implementation_type: Type that implements the service
        implementation_factory: Factory function to create the service

    Returns:
        Service collection
    """
    descriptor = LazyServiceDescriptor(
        service_type=service_type,
        implementation_type=implementation_type,
        implementation_factory=implementation_factory,
        lifetime="singleton",
    )
    return service_collection.add(descriptor)


def add_lazy_scoped(
    service_collection: ServiceCollection,
    service_type: Type[T],
    implementation_type: Optional[Type] = None,
    implementation_factory: Optional[Callable[[ServiceProvider], T]] = None,
) -> ServiceCollection:
    """
    Register a lazily initialized scoped service.

    Args:
        service_collection: Service collection
        service_type: Type of the service
        implementation_type: Type that implements the service
        implementation_factory: Factory function to create the service

    Returns:
        Service collection
    """
    descriptor = LazyServiceDescriptor(
        service_type=service_type,
        implementation_type=implementation_type,
        implementation_factory=implementation_factory,
        lifetime="scoped",
    )
    return service_collection.add(descriptor)
