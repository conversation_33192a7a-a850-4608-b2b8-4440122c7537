"""
Container Access Utilities for Dependency Injection

Provides global access to the singleton DI container instance and related helpers.
"""

import logging
import threading
from typing import Optional

from .container import (
    ServiceCollection,
)
from .provider import (
    ServiceProvider,
)


def create_service_collection() -> ServiceCollection:
    """Create a new service collection."""
    return ServiceCollection()


# Global container instance (managed by get_container)
_global_container_instance: Optional[ServiceProvider] = None
_global_container_lock = threading.RLock()  # Use RLock for reentrancy if needed


def get_container() -> ServiceProvider:
    """Gets the singleton ServiceProvider instance.

    Initializes it with a default ServiceCollection if not already created.
    """
    global _global_container_instance
    with _global_container_lock:
        if _global_container_instance is None:
            # Create a default service collection
            collection = create_service_collection()
            # --- Config Manager Registration ---
            from person_suit.core.application.interfaces.config_interface import (
                IConfigManager,
            )
            from person_suit.core.infrastructure.configuration.manager import (
                JsonConfigManager,
            )

            config_file_path = "config.json"
            try:
                config_manager_instance = JsonConfigManager(
                    config_path=config_file_path
                )
                collection.add_singleton(
                    IConfigManager, instance=config_manager_instance
                )
                print(f"[DI] Registered IConfigManager (using {config_file_path}).")
            except Exception as e:
                print(
                    f"[DI] WARNING: Failed to initialize/register IConfigManager: {e}. Using default configs."
                )
            # --- End Config Registration ---
            # --- Add Core Infrastructure Service Registrations ---
            from person_suit.core.application.interfaces.communication import (
                IChannelManager,
            )
            from person_suit.core.application.interfaces.events_interface import (
                IEventManager,
            )
            from person_suit.core.application.interfaces.registration_interface import (
                IRegistry,
            )
            from person_suit.core.events import InfrastructureEventManager
            from person_suit.core.infrastructure.communication import ChannelManager
            from person_suit.core.infrastructure.registration import (
                InfrastructureRegistry,
            )

            collection.add_singleton(
                IRegistry, implementation_type=InfrastructureRegistry
            )
            collection.add_singleton(
                IChannelManager, implementation_type=ChannelManager
            )
            collection.add_singleton(
                IEventManager, implementation_type=InfrastructureEventManager
            )
            print(
                "[DI] Registered core infrastructure services (Registry, ChannelManager, EventManager)."
            )
            # --- Add Meta-System Service Registrations (Folded Mind - Sync/SEM) ---
            from person_suit.meta_systems.persona_core.folded_mind.SEM.neurochemical.system import (
                NeurochemicalSystem,
            )
            from person_suit.meta_systems.persona_core.folded_mind.sync.consistency import (
                ConsistencyChecker,
                ConsistencyCheckerConfig,
            )
            from person_suit.meta_systems.persona_core.folded_mind.sync.state_manager import (
                StateManager,
                StateManagerConfig,
            )
            from person_suit.meta_systems.persona_core.folded_mind.sync.synchronizer import (
                SynchronizationConfig,
                Synchronizer,
            )

            log = logging.getLogger(__name__)

            def _create_state_manager(provider: ServiceProvider) -> StateManager:
                try:
                    config_manager = provider.get_required_service(IConfigManager)
                    config_dict = config_manager.get_section("StateManager")
                    if config_dict:
                        try:
                            config = StateManagerConfig(**config_dict)
                            log.debug("Loaded StateManager config from source.")
                        except TypeError as e:
                            log.warning(
                                f"Error creating StateManagerConfig from dict: {e}. Using defaults."
                            )
                            config = StateManagerConfig()
                    else:
                        log.warning(
                            "'StateManager' config section not found. Using defaults."
                        )
                        config = StateManagerConfig()
                except Exception as e:
                    log.warning(
                        f"Failed to get IConfigManager for StateManager config: {e}. Using defaults."
                    )
                    config = StateManagerConfig()
                return StateManager(
                    event_manager=provider.get_required_service(IEventManager),
                    config=config,
                )

            def _create_consistency_checker(
                provider: ServiceProvider,
            ) -> ConsistencyChecker:
                try:
                    config_manager = provider.get_required_service(IConfigManager)
                    config_dict = config_manager.get_section("ConsistencyChecker")
                    if config_dict:
                        try:
                            config = ConsistencyCheckerConfig(**config_dict)
                            log.debug("Loaded ConsistencyChecker config from source.")
                        except TypeError as e:
                            log.warning(
                                f"Error creating ConsistencyCheckerConfig from dict: {e}. Using defaults."
                            )
                            config = ConsistencyCheckerConfig()
                    else:
                        log.warning(
                            "'ConsistencyChecker' config section not found. Using defaults."
                        )
                        config = ConsistencyCheckerConfig()
                except Exception as e:
                    log.warning(
                        f"Failed to get IConfigManager for ConsistencyChecker config: {e}. Using defaults."
                    )
                    config = ConsistencyCheckerConfig()
                return ConsistencyChecker(
                    event_manager=provider.get_required_service(IEventManager),
                    state_manager=provider.get_required_service(StateManager),
                    config=config,
                )

            def _create_synchronizer(provider: ServiceProvider) -> Synchronizer:
                try:
                    config_manager = provider.get_required_service(IConfigManager)
                    config_dict = config_manager.get_section("Synchronizer")
                    if config_dict:
                        try:
                            config = SynchronizationConfig(**config_dict)
                            log.debug("Loaded Synchronizer config from source.")
                        except TypeError as e:
                            log.warning(
                                f"Error creating SynchronizationConfig from dict: {e}. Using defaults."
                            )
                            config = SynchronizationConfig()
                    else:
                        log.warning(
                            "'Synchronizer' config section not found. Using defaults."
                        )
                        config = SynchronizationConfig()
                except Exception as e:
                    log.warning(
                        f"Failed to get IConfigManager for Synchronizer config: {e}. Using defaults."
                    )
                    config = SynchronizationConfig()
                return Synchronizer(
                    event_manager=provider.get_required_service(IEventManager),
                    state_manager=provider.get_required_service(StateManager),
                    consistency_checker=provider.get_required_service(
                        ConsistencyChecker
                    ),
                    config=config,
                )

            def _create_neurochemical_system(
                provider: ServiceProvider,
            ) -> NeurochemicalSystem:
                return NeurochemicalSystem(
                    event_manager=provider.get_required_service(IEventManager)
                )

            collection.add_singleton(
                StateManager,
                implementation_factory=lambda provider: _create_state_manager(provider),
            )
            collection.add_singleton(
                ConsistencyChecker,
                implementation_factory=lambda provider: _create_consistency_checker(
                    provider
                ),
            )
            collection.add_singleton(
                Synchronizer,
                implementation_factory=lambda provider: _create_synchronizer(provider),
            )
            collection.add_singleton(
                NeurochemicalSystem,
                implementation_factory=lambda provider: _create_neurochemical_system(
                    provider
                ),
            )
            print(
                "[DI] Registered Folded Mind services using factories (with config loading)."
            )
            _global_container_instance = collection.build_service_provider()
            print("[DI] Global ServiceProvider created.")
    return _global_container_instance
