"""
Fluent API for service registration in the Person Suit dependency injection system.

This module provides a more intuitive and chainable interface for registering services
in the dependency injection container.
"""

from typing import Callable, Generic, Optional, Type, TypeVar, cast

from .container import (
    ServiceCollection,
)
from .factory import (
    ServiceFactory,
    add_factory,
)
from .provider import (
    ServiceProvider,
)

T = TypeVar("T")
TService = TypeVar("TService")
TImplementation = TypeVar("TImplementation")


class FluentServiceRegistration(Generic[TService]):
    """Fluent API for service registration."""

    def __init__(
        self, service_collection: ServiceCollection, service_type: Type[TService]
    ):
        """
        Initialize fluent service registration.

        Args:
            service_collection: Service collection to register services in
            service_type: Type of the service to register
        """
        self.service_collection = service_collection
        self.service_type = service_type

    def with_implementation(
        self, implementation_type: Type[TImplementation]
    ) -> "FluentLifetimeRegistration[TService]":
        """
        Specify the implementation type for the service.

        Args:
            implementation_type: Type that implements the service

        Returns:
            Fluent lifetime registration for chaining
        """
        return FluentLifetimeRegistration(
            self.service_collection, self.service_type, implementation_type
        )

    def with_factory(
        self, factory_func: Callable[[ServiceProvider], TService]
    ) -> "FluentLifetimeRegistration[TService]":
        """
        Specify a factory function for the service.

        Args:
            factory_func: Factory function to create the service

        Returns:
            Fluent lifetime registration for chaining
        """
        return FluentLifetimeRegistration(
            self.service_collection, self.service_type, None, factory_func
        )

    def with_factory_instance(
        self, factory: ServiceFactory[TService]
    ) -> "FluentLifetimeRegistration[TService]":
        """
        Specify a factory instance for the service.

        Args:
            factory: Factory to create the service

        Returns:
            Fluent lifetime registration for chaining
        """
        return FluentLifetimeRegistration(
            self.service_collection, self.service_type, None, None, factory
        )

    def as_self(self) -> "FluentLifetimeRegistration[TService]":
        """
        Register the service as its own implementation.

        Returns:
            Fluent lifetime registration for chaining
        """
        return self.with_implementation(cast(Type[TImplementation], self.service_type))

    def as_instance(self, instance: TService) -> ServiceCollection:
        """
        Register an existing instance as a singleton service.

        Args:
            instance: Instance to register

        Returns:
            Service collection for further registration
        """
        return self.service_collection.add_singleton(
            self.service_type, instance=instance
        )


class FluentLifetimeRegistration(Generic[TService]):
    """Fluent API for service lifetime registration."""

    def __init__(
        self,
        service_collection: ServiceCollection,
        service_type: Type[TService],
        implementation_type: Optional[Type] = None,
        factory_func: Optional[Callable[[ServiceProvider], TService]] = None,
        factory: Optional[ServiceFactory[TService]] = None,
    ):
        """
        Initialize fluent lifetime registration.

        Args:
            service_collection: Service collection to register services in
            service_type: Type of the service to register
            implementation_type: Type that implements the service
            factory_func: Factory function to create the service
            factory: Factory to create the service
        """
        self.service_collection = service_collection
        self.service_type = service_type
        self.implementation_type = implementation_type
        self.factory_func = factory_func
        self.factory = factory

    def transient(self) -> ServiceCollection:
        """
        Register the service as transient.

        Returns:
            Service collection for further registration
        """
        if self.factory:
            return add_factory(
                self.service_collection, self.service_type, self.factory, "transient"
            )
        elif self.factory_func:
            return self.service_collection.add_transient(
                self.service_type, implementation_factory=self.factory_func
            )
        else:
            return self.service_collection.add_transient(
                self.service_type, self.implementation_type
            )

    def singleton(self) -> ServiceCollection:
        """
        Register the service as singleton.

        Returns:
            Service collection for further registration
        """
        if self.factory:
            return add_factory(
                self.service_collection, self.service_type, self.factory, "singleton"
            )
        elif self.factory_func:
            return self.service_collection.add_singleton(
                self.service_type, implementation_factory=self.factory_func
            )
        else:
            return self.service_collection.add_singleton(
                self.service_type, self.implementation_type
            )

    def scoped(self) -> ServiceCollection:
        """
        Register the service as scoped.

        Returns:
            Service collection for further registration
        """
        if self.factory:
            return add_factory(
                self.service_collection, self.service_type, self.factory, "scoped"
            )
        elif self.factory_func:
            return self.service_collection.add_scoped(
                self.service_type, implementation_factory=self.factory_func
            )
        else:
            return self.service_collection.add_scoped(
                self.service_type, self.implementation_type
            )


# Extension methods for ServiceCollection
def add_service(
    service_collection: ServiceCollection, service_type: Type[T]
) -> FluentServiceRegistration[T]:
    """
    Start fluent registration of a service.

    Args:
        service_collection: Service collection
        service_type: Type of the service to register

    Returns:
        Fluent service registration for chaining
    """
    return FluentServiceRegistration(service_collection, service_type)


# Add extension methods to ServiceCollection class
def add_fluent_api_to_service_collection() -> None:
    """
    Add fluent API extension methods to ServiceCollection class.

    This function adds the `add_service` method to the ServiceCollection class,
    enabling fluent service registration directly from the service collection.
    """
    setattr(
        ServiceCollection,
        "add_service",
        lambda self, service_type: add_service(self, service_type),
    )


# Add fluent API to ServiceCollection class by default
add_fluent_api_to_service_collection()
