"""
Dependency graph construction and cycle detection for the Person Suit dependency injection system.

This module provides tools for analyzing service dependencies, detecting circular dependencies,
and generating diagnostic information for dependency issues.
"""

import inspect
from typing import Any, Dict, List, Optional, Set, Type, TypeVar

from .container import (
    ServiceCollection,
    ServiceDescriptor,
)

# Remove direct import of ConfigurationError to break cycle
# from person_suit.core.infrastructure.error_handling import ConfigurationError

T = TypeVar("T")


# Define a specific exception for dependency cycles within this module
class DependencyCycleError(Exception):
    """Raised when a circular dependency is detected in service registrations."""

    pass


class DependencyNode:
    """Node in the dependency graph representing a service type."""

    def __init__(self, service_type: Type):
        """
        Initialize dependency node.

        Args:
            service_type: Service type
        """
        self.service_type = service_type
        self.dependencies: Set[Type] = set()
        self.descriptor: Optional[ServiceDescriptor] = None
        self.name: str = getattr(service_type, "__name__", str(service_type))

    def add_dependency(self, dependency_type: Type) -> None:
        """
        Add a dependency to this node.

        Args:
            dependency_type: Type of the dependency
        """
        self.dependencies.add(dependency_type)

    def __repr__(self) -> str:
        """String representation of the node."""
        return f"DependencyNode({self.name})"


class DependencyGraph:
    """Graph representing service dependencies."""

    def __init__(self):
        """Initialize dependency graph."""
        self.nodes: Dict[Type, DependencyNode] = {}

    def add_node(
        self, service_type: Type, descriptor: Optional[ServiceDescriptor] = None
    ) -> DependencyNode:
        """
        Add a node to the graph.

        Args:
            service_type: Service type
            descriptor: Service descriptor

        Returns:
            Created node
        """
        if service_type not in self.nodes:
            node = DependencyNode(service_type)
            node.descriptor = descriptor
            self.nodes[service_type] = node
        return self.nodes[service_type]

    def add_dependency(self, dependent_type: Type, dependency_type: Type) -> None:
        """
        Add a dependency edge to the graph.

        Args:
            dependent_type: Type that depends on another
            dependency_type: Type that is depended upon
        """
        # Ensure both nodes exist
        dependent_node = self.add_node(dependent_type)
        self.add_node(dependency_type)

        # Add dependency
        dependent_node.add_dependency(dependency_type)

    def detect_cycles(self) -> List[List[Type]]:
        """
        Detect cycles in the dependency graph using depth-first search.

        Returns:
            List of cycles, each represented as a list of types
        """
        cycles: List[List[Type]] = []
        visited: Set[Type] = set()
        recursion_stack: Dict[Type, bool] = {}

        def visit(service_type: Type, path: List[Type]) -> None:
            """
            Visit a node in the graph.

            Args:
                service_type: Type to visit
                path: Current path in the graph
            """
            # Mark node as visited and add to recursion stack
            visited.add(service_type)
            recursion_stack[service_type] = True
            path.append(service_type)

            # Visit dependencies
            node = self.nodes[service_type]
            for dependency_type in node.dependencies:
                if dependency_type not in visited:
                    # Recursive visit
                    visit(dependency_type, path.copy())
                elif dependency_type in recursion_stack:
                    # Found a cycle
                    cycle_start = path.index(dependency_type)
                    cycle = path[cycle_start:] + [dependency_type]
                    cycles.append(cycle)

            # Remove from recursion stack
            recursion_stack.pop(service_type, None)

        # Visit all nodes
        for service_type in list(self.nodes.keys()):
            if service_type not in visited:
                visit(service_type, [])

        return cycles

    def visualize_cycle(self, cycle: List[Type]) -> str:
        """
        Generate a visual representation of a dependency cycle.

        Args:
            cycle: List of types forming a cycle

        Returns:
            String visualization of the cycle
        """
        # Create a list of service names
        names = [self.nodes[t].name for t in cycle]

        # Create visualization
        result = " -> ".join(names)

        return result


class DependencyAnalyzer:
    """Analyzes service dependencies based on constructor parameters."""

    def __init__(self, service_collection: ServiceCollection):
        """
        Initialize dependency analyzer.

        Args:
            service_collection: Service collection to analyze
        """
        self.service_collection = service_collection
        self.graph = DependencyGraph()

    def analyze(self) -> DependencyGraph:
        """
        Analyze dependencies in the service collection.

        Returns:
            Constructed dependency graph
        """
        # Get all descriptors
        all_descriptors = self.service_collection.get_all_descriptors()

        # Add all services to the graph
        for service_type, descriptors in all_descriptors.items():
            for descriptor in descriptors:
                node = self.graph.add_node(service_type, descriptor)

                # If implementation type is available, analyze its dependencies
                if descriptor.implementation_type:
                    implementation_type = descriptor.implementation_type
                    dependencies = self._analyze_constructor_dependencies(
                        implementation_type
                    )

                    # Add dependencies to the graph
                    for dependency_type in dependencies:
                        self.graph.add_dependency(service_type, dependency_type)

        return self.graph

    def check_for_cycles(self) -> None:
        """
        Check for circular dependencies and raise an error if found.

        Raises:
            ConfigurationError: If circular dependencies are detected
        """
        # Analyze dependencies
        self.analyze()

        # Detect cycles
        cycles = self.graph.detect_cycles()

        # If cycles are detected, raise an error
        if cycles:
            error_message = "Circular dependencies detected in service registration:\n"

            # Add each cycle to the error message
            for i, cycle in enumerate(cycles):
                cycle_desc = self.graph.visualize_cycle(cycle)
                error_message += f"\nCycle {i + 1}: {cycle_desc}"

            # Add advice for resolving circular dependencies
            error_message += "\n\nTo resolve circular dependencies, consider:\n"
            error_message += (
                "1. Restructuring your classes to eliminate circular references\n"
            )
            error_message += (
                "2. Using factory-based registration for services in the cycle\n"
            )
            error_message += "3. Using property injection instead of constructor injection for some dependencies\n"
            error_message += (
                "4. Introducing an interface that both dependent services implement"
            )

            # Raise the local exception instead of the imported one
            raise DependencyCycleError(error_message)

    def _analyze_constructor_dependencies(
        self, implementation_type: Type
    ) -> List[Type]:
        """
        Analyze constructor dependencies of a type.

        Args:
            implementation_type: Type to analyze

        Returns:
            List of dependency types
        """
        constructor = self._get_constructor(implementation_type)
        if not constructor:
            return []

        # Get constructor parameters
        params = inspect.signature(constructor).parameters
        if not params:
            return []

        # Extract dependency types from parameters
        dependencies: List[Type] = []
        for param_name, param in params.items():
            # Skip 'self' parameter
            if param_name == "self":
                continue

            # Get parameter type hint if available
            param_type = param.annotation
            if param_type is inspect.Parameter.empty:
                # If no type hint, skip this parameter
                continue

            # Add dependency
            dependencies.append(param_type)

        return dependencies

    def _get_constructor(self, implementation_type: Type) -> Optional[Any]:
        """
        Get the constructor of a type.

        Args:
            implementation_type: Type to get constructor for

        Returns:
            Constructor or None if not found
        """
        # Get __init__ method if it exists
        constructor = getattr(implementation_type, "__init__", None)

        # Check if it's the default object.__init__
        if constructor is not None and constructor.__qualname__ != "object.__init__":
            return constructor

        return None
