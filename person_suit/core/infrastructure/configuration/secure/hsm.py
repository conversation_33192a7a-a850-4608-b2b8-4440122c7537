"""
Person Suit - Hardware Security Module (HSM) Support

This module provides support for hardware security modules (HSMs) for secure
credential storage and cryptographic operations in the Person Suit framework.

The HSM integration provides a secure way to store and manage cryptographic keys
and perform cryptographic operations without exposing sensitive key material.
"""

import base64
import json
import logging
import os
import threading
import uuid
from abc import ABC, abstractmethod
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from .credential_manager import (
    CredentialError,
    CredentialStorageBackend,
)

logger = logging.getLogger("person_suit.configuration.secure.hsm")

# Try to import cryptography for encryption support
try:
    from cryptography.hazmat.backends import default_backend
    from cryptography.hazmat.primitives import hashes, hmac
    from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

    ENCRYPTION_AVAILABLE = True
except ImportError:
    ENCRYPTION_AVAILABLE = False
    logger.warning(
        "Cryptography package not installed. HSM simulation will have limited functionality."
    )


class HSMError(Exception):
    """Base exception for HSM-related errors."""

    pass


class HSMInterface(ABC):
    """Abstract interface for HSM operations."""

    @abstractmethod
    def generate_key(self, key_type: str, key_id: Optional[str] = None) -> str:
        """Generate a new key in the HSM.

        Args:
            key_type: Type of key to generate (e.g., AES, RSA)
            key_id: Optional key ID (generated if not provided)

        Returns:
            Key ID for the generated key

        Raises:
            HSMError: If key generation fails
        """
        pass

    @abstractmethod
    def encrypt(self, key_id: str, data: bytes) -> bytes:
        """Encrypt data using a key in the HSM.

        Args:
            key_id: ID of the key to use
            data: Data to encrypt

        Returns:
            Encrypted data

        Raises:
            HSMError: If encryption fails
        """
        pass

    @abstractmethod
    def decrypt(self, key_id: str, data: bytes) -> bytes:
        """Decrypt data using a key in the HSM.

        Args:
            key_id: ID of the key to use
            data: Data to decrypt

        Returns:
            Decrypted data

        Raises:
            HSMError: If decryption fails
        """
        pass

    @abstractmethod
    def sign(self, key_id: str, data: bytes) -> bytes:
        """Sign data using a key in the HSM.

        Args:
            key_id: ID of the key to use
            data: Data to sign

        Returns:
            Signature

        Raises:
            HSMError: If signing fails
        """
        pass

    @abstractmethod
    def verify(self, key_id: str, data: bytes, signature: bytes) -> bool:
        """Verify a signature using a key in the HSM.

        Args:
            key_id: ID of the key to use
            data: Data that was signed
            signature: Signature to verify

        Returns:
            True if the signature is valid, False otherwise

        Raises:
            HSMError: If verification fails
        """
        pass

    @abstractmethod
    def list_keys(self) -> List[str]:
        """List all keys in the HSM.

        Returns:
            List of key IDs

        Raises:
            HSMError: If listing keys fails
        """
        pass

    @abstractmethod
    def delete_key(self, key_id: str) -> bool:
        """Delete a key from the HSM.

        Args:
            key_id: ID of the key to delete

        Returns:
            True if the key was deleted, False otherwise

        Raises:
            HSMError: If key deletion fails
        """
        pass


class SoftwareHSMSimulator(HSMInterface):
    """Software-based HSM simulator for testing and development.

    This class simulates an HSM using software-based cryptography. It is intended
    for testing and development purposes only, and should not be used in production.
    """

    def __init__(self, storage_path: Optional[str] = None):
        """Initialize the HSM simulator.

        Args:
            storage_path: Path to store HSM data (default: in-memory only)
        """
        self._keys: Dict[str, Dict[str, Any]] = {}
        self._storage_path = Path(storage_path) if storage_path else None
        self._lock = threading.RLock()

        # Load keys from storage if available
        if self._storage_path and self._storage_path.exists():
            self._load_keys()

        logger.info("Software HSM simulator initialized")

    def _load_keys(self) -> None:
        """Load keys from storage."""
        try:
            with open(self._storage_path, "r") as f:
                data = json.load(f)

            # Convert key material from base64
            for key_id, key_data in data.items():
                if "key_material" in key_data:
                    key_data["key_material"] = base64.b64decode(
                        key_data["key_material"]
                    )

            self._keys = data
            logger.debug(f"Loaded {len(self._keys)} keys from {self._storage_path}")
        except Exception as e:
            logger.error(f"Failed to load keys from {self._storage_path}: {e}")
            self._keys = {}

    def _save_keys(self) -> None:
        """Save keys to storage."""
        if not self._storage_path:
            return

        try:
            # Ensure directory exists
            os.makedirs(self._storage_path.parent, exist_ok=True)

            # Convert key material to base64 for storage
            data = {}
            for key_id, key_data in self._keys.items():
                data[key_id] = key_data.copy()
                if "key_material" in data[key_id]:
                    data[key_id]["key_material"] = base64.b64encode(
                        data[key_id]["key_material"]
                    ).decode("utf-8")

            # Write to file
            with open(self._storage_path, "w") as f:
                json.dump(data, f, indent=2)

            logger.debug(f"Saved {len(self._keys)} keys to {self._storage_path}")
        except Exception as e:
            logger.error(f"Failed to save keys to {self._storage_path}: {e}")

    def generate_key(self, key_type: str, key_id: Optional[str] = None) -> str:
        """Generate a new key in the HSM simulator.

        Args:
            key_type: Type of key to generate (AES, RSA, HMAC)
            key_id: Optional key ID (generated if not provided)

        Returns:
            Key ID for the generated key

        Raises:
            HSMError: If key generation fails
        """
        if not ENCRYPTION_AVAILABLE:
            raise HSMError("Cryptography package not installed. Cannot generate keys.")

        with self._lock:
            # Generate key ID if not provided
            if key_id is None:
                key_id = str(uuid.uuid4())

            # Generate key material based on type
            if key_type.upper() == "AES":
                # AES-256 key
                key_material = os.urandom(32)
            elif key_type.upper() == "HMAC":
                # HMAC key
                key_material = os.urandom(32)
            elif key_type.upper() == "RSA":
                # RSA key (not actually generated, just a placeholder)
                # In a real implementation, you would generate an actual RSA key
                key_material = os.urandom(32)
                logger.warning("RSA key generation not implemented in HSM simulator")
            else:
                raise HSMError(f"Unsupported key type: {key_type}")

            # Store the key
            self._keys[key_id] = {
                "key_type": key_type.upper(),
                "key_material": key_material,
                "created_at": datetime.now().isoformat(),
            }

            # Save keys to storage
            self._save_keys()

            logger.info(f"Generated {key_type.upper()} key: {key_id}")

            return key_id

    def encrypt(self, key_id: str, data: bytes) -> bytes:
        """Encrypt data using a key in the HSM simulator.

        Args:
            key_id: ID of the key to use
            data: Data to encrypt

        Returns:
            Encrypted data

        Raises:
            HSMError: If encryption fails
        """
        if not ENCRYPTION_AVAILABLE:
            raise HSMError("Cryptography package not installed. Cannot encrypt data.")

        with self._lock:
            # Get the key
            key_data = self._keys.get(key_id)
            if not key_data:
                raise HSMError(f"Key not found: {key_id}")

            # Check key type
            key_type = key_data["key_type"]
            key_material = key_data["key_material"]

            if key_type == "AES":
                # Generate a random IV
                iv = os.urandom(12)  # 12 bytes for GCM

                # Create an encryptor
                encryptor = Cipher(
                    algorithms.AES(key_material),
                    modes.GCM(iv),
                    backend=default_backend(),
                ).encryptor()

                # Encrypt the data
                ciphertext = encryptor.update(data) + encryptor.finalize()

                # Combine IV, tag, and ciphertext
                result = iv + encryptor.tag + ciphertext

                return result
            else:
                raise HSMError(f"Encryption not supported for key type: {key_type}")

    def decrypt(self, key_id: str, data: bytes) -> bytes:
        """Decrypt data using a key in the HSM simulator.

        Args:
            key_id: ID of the key to use
            data: Data to decrypt

        Returns:
            Decrypted data

        Raises:
            HSMError: If decryption fails
        """
        if not ENCRYPTION_AVAILABLE:
            raise HSMError("Cryptography package not installed. Cannot decrypt data.")

        with self._lock:
            # Get the key
            key_data = self._keys.get(key_id)
            if not key_data:
                raise HSMError(f"Key not found: {key_id}")

            # Check key type
            key_type = key_data["key_type"]
            key_material = key_data["key_material"]

            if key_type == "AES":
                # Extract IV and tag
                iv = data[:12]
                tag = data[12:28]
                ciphertext = data[28:]

                # Create a decryptor
                decryptor = Cipher(
                    algorithms.AES(key_material),
                    modes.GCM(iv, tag),
                    backend=default_backend(),
                ).decryptor()

                # Decrypt the data
                plaintext = decryptor.update(ciphertext) + decryptor.finalize()

                return plaintext
            else:
                raise HSMError(f"Decryption not supported for key type: {key_type}")

    def sign(self, key_id: str, data: bytes) -> bytes:
        """Sign data using a key in the HSM simulator.

        Args:
            key_id: ID of the key to use
            data: Data to sign

        Returns:
            Signature

        Raises:
            HSMError: If signing fails
        """
        if not ENCRYPTION_AVAILABLE:
            raise HSMError("Cryptography package not installed. Cannot sign data.")

        with self._lock:
            # Get the key
            key_data = self._keys.get(key_id)
            if not key_data:
                raise HSMError(f"Key not found: {key_id}")

            # Check key type
            key_type = key_data["key_type"]
            key_material = key_data["key_material"]

            if key_type == "HMAC":
                # Create an HMAC
                h = hmac.HMAC(key_material, hashes.SHA256(), backend=default_backend())

                # Update with data
                h.update(data)

                # Finalize
                signature = h.finalize()

                return signature
            else:
                raise HSMError(f"Signing not supported for key type: {key_type}")

    def verify(self, key_id: str, data: bytes, signature: bytes) -> bool:
        """Verify a signature using a key in the HSM simulator.

        Args:
            key_id: ID of the key to use
            data: Data that was signed
            signature: Signature to verify

        Returns:
            True if the signature is valid, False otherwise

        Raises:
            HSMError: If verification fails
        """
        if not ENCRYPTION_AVAILABLE:
            raise HSMError(
                "Cryptography package not installed. Cannot verify signature."
            )

        with self._lock:
            # Get the key
            key_data = self._keys.get(key_id)
            if not key_data:
                raise HSMError(f"Key not found: {key_id}")

            # Check key type
            key_type = key_data["key_type"]
            key_material = key_data["key_material"]

            if key_type == "HMAC":
                # Create an HMAC
                h = hmac.HMAC(key_material, hashes.SHA256(), backend=default_backend())

                # Update with data
                h.update(data)

                try:
                    # Verify
                    h.verify(signature)
                    return True
                except Exception:
                    return False
            else:
                raise HSMError(f"Verification not supported for key type: {key_type}")

    def list_keys(self) -> List[str]:
        """List all keys in the HSM simulator.

        Returns:
            List of key IDs
        """
        with self._lock:
            return list(self._keys.keys())

    def delete_key(self, key_id: str) -> bool:
        """Delete a key from the HSM simulator.

        Args:
            key_id: ID of the key to delete

        Returns:
            True if the key was deleted, False otherwise
        """
        with self._lock:
            if key_id in self._keys:
                del self._keys[key_id]
                self._save_keys()
                logger.info(f"Deleted key: {key_id}")
                return True
            return False


class HSMCredentialBackend(CredentialStorageBackend):
    """HSM-based credential storage backend.

    This backend uses a hardware security module (HSM) to securely store and
    retrieve credentials, providing hardware-level security for sensitive data.
    """

    def __init__(self, hsm: HSMInterface, master_key_id: Optional[str] = None):
        """Initialize with an HSM interface.

        Args:
            hsm: HSM interface to use
            master_key_id: ID of the master key to use (generated if not provided)

        Raises:
            CredentialError: If initialization fails
        """
        self._hsm = hsm
        self._credentials = {}
        self._metadata_file = None

        try:
            # Generate or use the master key
            if master_key_id:
                # Check if the key exists
                if master_key_id not in hsm.list_keys():
                    raise CredentialError(f"Master key not found: {master_key_id}")
                self._master_key_id = master_key_id
            else:
                # Generate a new master key
                self._master_key_id = hsm.generate_key("AES")

            logger.info(f"Using HSM with master key: {self._master_key_id}")
        except Exception as e:
            raise CredentialError(f"Failed to initialize HSM credential backend: {e}")

    def set_metadata_file(self, file_path: str) -> None:
        """Set a metadata file for storing credential metadata.

        The HSM is used to store the actual credential values, while the metadata
        file stores information about the credentials (e.g., key IDs, timestamps).

        Args:
            file_path: Path to the metadata file
        """
        self._metadata_file = Path(file_path)

        # Load metadata if file exists
        if self._metadata_file.exists():
            self._load_metadata()
        else:
            # Initialize empty metadata
            self._credentials = {}
            self._save_metadata()

    def _load_metadata(self) -> None:
        """Load credential metadata from file."""
        try:
            with open(self._metadata_file, "r") as f:
                self._credentials = json.load(f)
            logger.debug(f"Loaded metadata for {len(self._credentials)} credentials")
        except Exception as e:
            logger.error(f"Failed to load credential metadata: {e}")
            self._credentials = {}

    def _save_metadata(self) -> bool:
        """Save credential metadata to file.

        Returns:
            True if successful, False otherwise
        """
        if not self._metadata_file:
            return True

        try:
            # Ensure directory exists
            os.makedirs(self._metadata_file.parent, exist_ok=True)

            # Write metadata to file
            with open(self._metadata_file, "w") as f:
                json.dump(self._credentials, f, indent=2)

            logger.debug(f"Saved metadata for {len(self._credentials)} credentials")
            return True
        except Exception as e:
            logger.error(f"Failed to save credential metadata: {e}")
            return False

    def get_credential(self, key: str) -> Optional[str]:
        """Get a credential from HSM storage.

        Args:
            key: Credential key

        Returns:
            Credential value or None if not found
        """
        # Check if we have metadata for this credential
        if key not in self._credentials:
            return None

        try:
            # Get the credential key ID
            cred_data = self._credentials[key]
            cred_key_id = cred_data["key_id"]

            # Decrypt the credential value using the HSM
            encrypted_value = base64.b64decode(cred_data["value"])
            decrypted_value = self._hsm.decrypt(self._master_key_id, encrypted_value)

            return decrypted_value.decode("utf-8")
        except Exception as e:
            logger.error(f"Failed to get credential {key}: {e}")
            return None

    def set_credential(self, key: str, value: str) -> bool:
        """Set a credential in HSM storage.

        Args:
            key: Credential key
            value: Credential value

        Returns:
            True if successful, False otherwise
        """
        try:
            # Encrypt the credential value using the HSM
            encrypted_value = self._hsm.encrypt(
                self._master_key_id, value.encode("utf-8")
            )

            # Store metadata
            self._credentials[key] = {
                "key_id": self._master_key_id,
                "value": base64.b64encode(encrypted_value).decode("utf-8"),
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
            }

            # Save metadata
            return self._save_metadata()
        except Exception as e:
            logger.error(f"Failed to set credential {key}: {e}")
            return False

    def delete_credential(self, key: str) -> bool:
        """Delete a credential from HSM storage.

        Args:
            key: Credential key

        Returns:
            True if successful, False otherwise
        """
        if key not in self._credentials:
            return False

        try:
            # Remove metadata
            del self._credentials[key]

            # Save metadata
            return self._save_metadata()
        except Exception as e:
            logger.error(f"Failed to delete credential {key}: {e}")
            return False

    def list_credentials(self) -> List[str]:
        """List available credential keys from HSM storage.

        Returns:
            List of credential keys
        """
        return list(self._credentials.keys())


# Factory function to create an HSM interface
def create_hsm_interface(hsm_type: str, **kwargs) -> HSMInterface:
    """Create an HSM interface.

    Args:
        hsm_type: Type of HSM interface to create
        **kwargs: Additional arguments for the HSM interface

    Returns:
        HSM interface

    Raises:
        HSMError: If the HSM type is not supported
    """
    if hsm_type.lower() == "software":
        return SoftwareHSMSimulator(**kwargs)
    else:
        raise HSMError(f"Unsupported HSM type: {hsm_type}")
