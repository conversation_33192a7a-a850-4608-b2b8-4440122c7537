"""
Person Suit - Secure Configuration

This package provides secure configuration handling for the Person Suit framework,
including secure credential storage and retrieval.
"""

from .credential_manager import (
    CredentialError,
    CredentialManager,
    CredentialStorageBackend,
    EncryptedFileCredentialBackend,
    EnvironmentCredentialBackend,
    get_credential_manager,
)

__all__ = [
    "CredentialManager",
    "CredentialStorageBackend",
    "EnvironmentCredentialBackend",
    "EncryptedFileCredentialBackend",
    "CredentialError",
    "get_credential_manager",
]
