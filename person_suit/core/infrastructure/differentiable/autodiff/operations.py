"""
Basic Operations for Automatic Differentiation
===========================================

This module implements basic operations for automatic differentiation,
such as addition, multiplication, division, etc. These operations are
used to build computation graphs for gradient computation.

Related Files:
- engine.py: Automatic differentiation engine
- graph.py: Computation graph implementation
- ../core.py: Core types and classes

Dependencies:
- Python 3.8+
- NumPy
"""

import logging
import math
from typing import (
    Any,
    List,
)

import numpy as np

from ..core import (
    Constant,
    Differentiable,
)

# Configure logging
logger = logging.getLogger(__name__)


def _ensure_differentiable(x: Any) -> Differentiable:
    """
    Ensure that x is a differentiable object.

    Args:
        x: The object to ensure is differentiable

    Returns:
        A differentiable object
    """
    if isinstance(x, Differentiable):
        return x

    # Convert to constant
    return Constant(x)


def add(x: Any, y: Any) -> Differentiable:
    """
    Add two objects.

    Args:
        x: First object
        y: Second object

    Returns:
        A differentiable object representing x + y
    """
    # Ensure x and y are differentiable
    x = _ensure_differentiable(x)
    y = _ensure_differentiable(y)

    # Create output node
    from person_suit.core.infrastructure.differentiable.core import (
        Differentiable,
        DifferentiableType,
    )

    output = Differentiable(
        name=f"add_{x.name}_{y.name}",
        diff_type=DifferentiableType.OPERATION,
        requires_grad=x.requires_grad or y.requires_grad,
    )

    # Compute output value
    if isinstance(x.value, np.ndarray) and isinstance(y.value, np.ndarray):
        output.value = x.value + y.value
    else:
        output.value = x.value + y.value

    # Set up gradient function
    def grad_fn(grad: Any) -> List[Any]:
        """Compute gradients for addition."""
        return [grad, grad]

    output._grad_fn = grad_fn

    # Set up parent-child relationships
    output._parents = [x, y]
    x._children.append(output)
    y._children.append(output)

    return output


def subtract(x: Any, y: Any) -> Differentiable:
    """
    Subtract y from x.

    Args:
        x: First object
        y: Second object

    Returns:
        A differentiable object representing x - y
    """
    # Ensure x and y are differentiable
    x = _ensure_differentiable(x)
    y = _ensure_differentiable(y)

    # Create output node
    from person_suit.core.infrastructure.differentiable.core import (
        Differentiable,
        DifferentiableType,
    )

    output = Differentiable(
        name=f"subtract_{x.name}_{y.name}",
        diff_type=DifferentiableType.OPERATION,
        requires_grad=x.requires_grad or y.requires_grad,
    )

    # Compute output value
    if isinstance(x.value, np.ndarray) and isinstance(y.value, np.ndarray):
        output.value = x.value - y.value
    else:
        output.value = x.value - y.value

    # Set up gradient function
    def grad_fn(grad: Any) -> List[Any]:
        """Compute gradients for subtraction."""
        return [grad, -grad]

    output._grad_fn = grad_fn

    # Set up parent-child relationships
    output._parents = [x, y]
    x._children.append(output)
    y._children.append(output)

    return output


def multiply(x: Any, y: Any) -> Differentiable:
    """
    Multiply two objects.

    Args:
        x: First object
        y: Second object

    Returns:
        A differentiable object representing x * y
    """
    # Ensure x and y are differentiable
    x = _ensure_differentiable(x)
    y = _ensure_differentiable(y)

    # Create output node
    from person_suit.core.infrastructure.differentiable.core import (
        Differentiable,
        DifferentiableType,
    )

    output = Differentiable(
        name=f"multiply_{x.name}_{y.name}",
        diff_type=DifferentiableType.OPERATION,
        requires_grad=x.requires_grad or y.requires_grad,
    )

    # Compute output value
    if isinstance(x.value, np.ndarray) and isinstance(y.value, np.ndarray):
        output.value = x.value * y.value
    else:
        output.value = x.value * y.value

    # Set up gradient function
    def grad_fn(grad: Any) -> List[Any]:
        """Compute gradients for multiplication."""
        return [grad * y.value, grad * x.value]

    output._grad_fn = grad_fn

    # Set up parent-child relationships
    output._parents = [x, y]
    x._children.append(output)
    y._children.append(output)

    return output


def divide(x: Any, y: Any) -> Differentiable:
    """
    Divide x by y.

    Args:
        x: First object
        y: Second object

    Returns:
        A differentiable object representing x / y
    """
    # Ensure x and y are differentiable
    x = _ensure_differentiable(x)
    y = _ensure_differentiable(y)

    # Create output node
    from person_suit.core.infrastructure.differentiable.core import (
        Differentiable,
        DifferentiableType,
    )

    output = Differentiable(
        name=f"divide_{x.name}_{y.name}",
        diff_type=DifferentiableType.OPERATION,
        requires_grad=x.requires_grad or y.requires_grad,
    )

    # Compute output value
    if isinstance(x.value, np.ndarray) and isinstance(y.value, np.ndarray):
        output.value = x.value / y.value
    else:
        output.value = x.value / y.value

    # Set up gradient function
    def grad_fn(grad: Any) -> List[Any]:
        """Compute gradients for division."""
        return [grad / y.value, -grad * x.value / (y.value * y.value)]

    output._grad_fn = grad_fn

    # Set up parent-child relationships
    output._parents = [x, y]
    x._children.append(output)
    y._children.append(output)

    return output


def power(x: Any, y: Any) -> Differentiable:
    """
    Raise x to the power of y.

    Args:
        x: Base
        y: Exponent

    Returns:
        A differentiable object representing x^y
    """
    # Ensure x and y are differentiable
    x = _ensure_differentiable(x)
    y = _ensure_differentiable(y)

    # Create output node
    from person_suit.core.infrastructure.differentiable.core import (
        Differentiable,
        DifferentiableType,
    )

    output = Differentiable(
        name=f"power_{x.name}_{y.name}",
        diff_type=DifferentiableType.OPERATION,
        requires_grad=x.requires_grad or y.requires_grad,
    )

    # Compute output value
    if isinstance(x.value, np.ndarray) and isinstance(y.value, np.ndarray):
        output.value = np.power(x.value, y.value)
    else:
        output.value = x.value**y.value

    # Set up gradient function
    def grad_fn(grad: Any) -> List[Any]:
        """Compute gradients for power."""
        # d/dx(x^y) = y * x^(y-1)
        # d/dy(x^y) = x^y * ln(x)
        dx = grad * y.value * np.power(x.value, y.value - 1)
        dy = grad * output.value * np.log(x.value)
        return [dx, dy]

    output._grad_fn = grad_fn

    # Set up parent-child relationships
    output._parents = [x, y]
    x._children.append(output)
    y._children.append(output)

    return output


def exp(x: Any) -> Differentiable:
    """
    Compute the exponential of x.

    Args:
        x: The input

    Returns:
        A differentiable object representing e^x
    """
    # Ensure x is differentiable
    x = _ensure_differentiable(x)

    # Create output node
    from person_suit.core.infrastructure.differentiable.core import (
        Differentiable,
        DifferentiableType,
    )

    output = Differentiable(
        name=f"exp_{x.name}",
        diff_type=DifferentiableType.OPERATION,
        requires_grad=x.requires_grad,
    )

    # Compute output value
    if isinstance(x.value, np.ndarray):
        output.value = np.exp(x.value)
    else:
        output.value = math.exp(x.value)

    # Set up gradient function
    def grad_fn(grad: Any) -> List[Any]:
        """Compute gradients for exponential."""
        return [grad * output.value]

    output._grad_fn = grad_fn

    # Set up parent-child relationships
    output._parents = [x]
    x._children.append(output)

    return output


def log(x: Any) -> Differentiable:
    """
    Compute the natural logarithm of x.

    Args:
        x: The input

    Returns:
        A differentiable object representing ln(x)
    """
    # Ensure x is differentiable
    x = _ensure_differentiable(x)

    # Create output node
    from person_suit.core.infrastructure.differentiable.core import (
        Differentiable,
        DifferentiableType,
    )

    output = Differentiable(
        name=f"log_{x.name}",
        diff_type=DifferentiableType.OPERATION,
        requires_grad=x.requires_grad,
    )

    # Compute output value
    if isinstance(x.value, np.ndarray):
        output.value = np.log(x.value)
    else:
        output.value = math.log(x.value)

    # Set up gradient function
    def grad_fn(grad: Any) -> List[Any]:
        """Compute gradients for logarithm."""
        return [grad / x.value]

    output._grad_fn = grad_fn

    # Set up parent-child relationships
    output._parents = [x]
    x._children.append(output)

    return output


def sin(x: Any) -> Differentiable:
    """
    Compute the sine of x.

    Args:
        x: The input

    Returns:
        A differentiable object representing sin(x)
    """
    # Ensure x is differentiable
    x = _ensure_differentiable(x)

    # Create output node
    from person_suit.core.infrastructure.differentiable.core import (
        Differentiable,
        DifferentiableType,
    )

    output = Differentiable(
        name=f"sin_{x.name}",
        diff_type=DifferentiableType.OPERATION,
        requires_grad=x.requires_grad,
    )

    # Compute output value
    if isinstance(x.value, np.ndarray):
        output.value = np.sin(x.value)
    else:
        output.value = math.sin(x.value)

    # Set up gradient function
    def grad_fn(grad: Any) -> List[Any]:
        """Compute gradients for sine."""
        if isinstance(x.value, np.ndarray):
            return [grad * np.cos(x.value)]
        else:
            return [grad * math.cos(x.value)]

    output._grad_fn = grad_fn

    # Set up parent-child relationships
    output._parents = [x]
    x._children.append(output)

    return output


def cos(x: Any) -> Differentiable:
    """
    Compute the cosine of x.

    Args:
        x: The input

    Returns:
        A differentiable object representing cos(x)
    """
    # Ensure x is differentiable
    x = _ensure_differentiable(x)

    # Create output node
    from person_suit.core.infrastructure.differentiable.core import (
        Differentiable,
        DifferentiableType,
    )

    output = Differentiable(
        name=f"cos_{x.name}",
        diff_type=DifferentiableType.OPERATION,
        requires_grad=x.requires_grad,
    )

    # Compute output value
    if isinstance(x.value, np.ndarray):
        output.value = np.cos(x.value)
    else:
        output.value = math.cos(x.value)

    # Set up gradient function
    def grad_fn(grad: Any) -> List[Any]:
        """Compute gradients for cosine."""
        if isinstance(x.value, np.ndarray):
            return [grad * -np.sin(x.value)]
        else:
            return [grad * -math.sin(x.value)]

    output._grad_fn = grad_fn

    # Set up parent-child relationships
    output._parents = [x]
    x._children.append(output)

    return output


def tan(x: Any) -> Differentiable:
    """
    Compute the tangent of x.

    Args:
        x: The input

    Returns:
        A differentiable object representing tan(x)
    """
    # Ensure x is differentiable
    x = _ensure_differentiable(x)

    # Create output node
    from person_suit.core.infrastructure.differentiable.core import (
        Differentiable,
        DifferentiableType,
    )

    output = Differentiable(
        name=f"tan_{x.name}",
        diff_type=DifferentiableType.OPERATION,
        requires_grad=x.requires_grad,
    )

    # Compute output value
    if isinstance(x.value, np.ndarray):
        output.value = np.tan(x.value)
    else:
        output.value = math.tan(x.value)

    # Set up gradient function
    def grad_fn(grad: Any) -> List[Any]:
        """Compute gradients for tangent."""
        if isinstance(x.value, np.ndarray):
            return [grad * (1 + np.tan(x.value) ** 2)]
        else:
            return [grad * (1 + math.tan(x.value) ** 2)]

    output._grad_fn = grad_fn

    # Set up parent-child relationships
    output._parents = [x]
    x._children.append(output)

    return output


def tanh(x: Any) -> Differentiable:
    """
    Compute the hyperbolic tangent of x.

    Args:
        x: The input

    Returns:
        A differentiable object representing tanh(x)
    """
    # Ensure x is differentiable
    x = _ensure_differentiable(x)

    # Create output node
    from person_suit.core.infrastructure.differentiable.core import (
        Differentiable,
        DifferentiableType,
    )

    output = Differentiable(
        name=f"tanh_{x.name}",
        diff_type=DifferentiableType.OPERATION,
        requires_grad=x.requires_grad,
    )

    # Compute output value
    if isinstance(x.value, np.ndarray):
        output.value = np.tanh(x.value)
    else:
        output.value = math.tanh(x.value)

    # Set up gradient function
    def grad_fn(grad: Any) -> List[Any]:
        """Compute gradients for hyperbolic tangent."""
        return [grad * (1 - output.value**2)]

    output._grad_fn = grad_fn

    # Set up parent-child relationships
    output._parents = [x]
    x._children.append(output)

    return output


def sigmoid(x: Any) -> Differentiable:
    """
    Compute the sigmoid of x.

    Args:
        x: The input

    Returns:
        A differentiable object representing sigmoid(x)
    """
    # Ensure x is differentiable
    x = _ensure_differentiable(x)

    # Create output node
    from person_suit.core.infrastructure.differentiable.core import (
        Differentiable,
        DifferentiableType,
    )

    output = Differentiable(
        name=f"sigmoid_{x.name}",
        diff_type=DifferentiableType.OPERATION,
        requires_grad=x.requires_grad,
    )

    # Compute output value
    if isinstance(x.value, np.ndarray):
        output.value = 1 / (1 + np.exp(-x.value))
    else:
        output.value = 1 / (1 + math.exp(-x.value))

    # Set up gradient function
    def grad_fn(grad: Any) -> List[Any]:
        """Compute gradients for sigmoid."""
        return [grad * output.value * (1 - output.value)]

    output._grad_fn = grad_fn

    # Set up parent-child relationships
    output._parents = [x]
    x._children.append(output)

    return output


def relu(x: Any) -> Differentiable:
    """
    Compute the ReLU of x.

    Args:
        x: The input

    Returns:
        A differentiable object representing ReLU(x)
    """
    # Ensure x is differentiable
    x = _ensure_differentiable(x)

    # Create output node
    from person_suit.core.infrastructure.differentiable.core import (
        Differentiable,
        DifferentiableType,
    )

    output = Differentiable(
        name=f"relu_{x.name}",
        diff_type=DifferentiableType.OPERATION,
        requires_grad=x.requires_grad,
    )

    # Compute output value
    if isinstance(x.value, np.ndarray):
        output.value = np.maximum(0, x.value)
    else:
        output.value = max(0, x.value)

    # Set up gradient function
    def grad_fn(grad: Any) -> List[Any]:
        """Compute gradients for ReLU."""
        if isinstance(x.value, np.ndarray):
            return [grad * np.where(x.value > 0, 1, 0)]
        else:
            return [grad * (1 if x.value > 0 else 0)]

    output._grad_fn = grad_fn

    # Set up parent-child relationships
    output._parents = [x]
    x._children.append(output)

    return output


def softmax(x: Any) -> Differentiable:
    """
    Compute the softmax of x.

    Args:
        x: The input

    Returns:
        A differentiable object representing softmax(x)
    """
    # Ensure x is differentiable
    x = _ensure_differentiable(x)

    # Create output node
    from person_suit.core.infrastructure.differentiable.core import (
        Differentiable,
        DifferentiableType,
    )

    output = Differentiable(
        name=f"softmax_{x.name}",
        diff_type=DifferentiableType.OPERATION,
        requires_grad=x.requires_grad,
    )

    # Compute output value
    if isinstance(x.value, np.ndarray):
        # Shift for numerical stability
        shifted = x.value - np.max(x.value, axis=-1, keepdims=True)
        exp_x = np.exp(shifted)
        output.value = exp_x / np.sum(exp_x, axis=-1, keepdims=True)
    else:
        raise ValueError("Softmax requires a numpy array input")

    # Set up gradient function
    def grad_fn(grad: Any) -> List[Any]:
        """Compute gradients for softmax."""
        # This is a simplified version that assumes grad is a vector
        # For a full implementation, see:
        # https://www.ics.uci.edu/~pjsadows/notes/matrix-calculus/
        return [grad * output.value * (1 - output.value)]

    output._grad_fn = grad_fn

    # Set up parent-child relationships
    output._parents = [x]
    x._children.append(output)

    return output


def matmul(x: Any, y: Any) -> Differentiable:
    """
    Compute the matrix multiplication of x and y.

    Args:
        x: First matrix
        y: Second matrix

    Returns:
        A differentiable object representing x @ y
    """
    # Ensure x and y are differentiable
    x = _ensure_differentiable(x)
    y = _ensure_differentiable(y)

    # Create output node
    from person_suit.core.infrastructure.differentiable.core import (
        Differentiable,
        DifferentiableType,
    )

    output = Differentiable(
        name=f"matmul_{x.name}_{y.name}",
        diff_type=DifferentiableType.OPERATION,
        requires_grad=x.requires_grad or y.requires_grad,
    )

    # Compute output value
    if isinstance(x.value, np.ndarray) and isinstance(y.value, np.ndarray):
        output.value = np.matmul(x.value, y.value)
    else:
        raise ValueError("Matrix multiplication requires numpy array inputs")

    # Set up gradient function
    def grad_fn(grad: Any) -> List[Any]:
        """Compute gradients for matrix multiplication."""
        # d/dA(AB) = grad @ B.T
        # d/dB(AB) = A.T @ grad
        dx = np.matmul(grad, y.value.T)
        dy = np.matmul(x.value.T, grad)
        return [dx, dy]

    output._grad_fn = grad_fn

    # Set up parent-child relationships
    output._parents = [x, y]
    x._children.append(output)
    y._children.append(output)

    return output
