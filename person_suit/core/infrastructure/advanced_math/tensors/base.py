"""
Base Tensor Interfaces for PersonSuit
===================================

This module defines the base interfaces and abstract classes for tensor
implementations in the PersonSuit framework.

Tensors are multi-dimensional arrays that generalize vectors and matrices,
enabling representation of complex, multi-dimensional relationships and
transformations.
"""

import abc
from enum import Enum
from typing import Any, Dict, List, Optional, Sequence, Tuple, Type, TypeVar

import numpy as np

from ..base import (
    MathStructure,
    StructureMetadata,
    StructureRegistry,
    StructureType,
)
from ...effects import EffectType, effects


class TensorOrder(Enum):
    """Order of a tensor (number of dimensions)."""

    SCALAR = 0  # 0-order tensor (scalar)
    VECTOR = 1  # 1-order tensor (vector)
    MATRIX = 2  # 2-order tensor (matrix)
    THIRD_ORDER = 3  # 3-order tensor
    FOURTH_ORDER = 4  # 4-order tensor
    HIGHER_ORDER = 5  # Higher-order tensor (5+)


TensorShape = Tuple[int, ...]
T = TypeVar("T", bound="Tensor")


class TensorMetadata(StructureMetadata):
    """Metadata for tensor structures."""

    def __init__(
        self,
        structure_id: Optional[str] = None,
        shape: Optional[TensorShape] = None,
        order: Optional[TensorOrder] = None,
        properties: Optional[Dict[str, Any]] = None,
        tags: Optional[List[str]] = None,
    ):
        """
        Initialize tensor metadata.

        Args:
            structure_id: Unique identifier for the tensor
            shape: Shape of the tensor
            order: Order of the tensor
            properties: Additional properties of the tensor
            tags: Tags for categorizing the tensor
        """
        # Determine order from shape if not provided
        if order is None and shape is not None:
            order = (
                TensorOrder.SCALAR
                if len(shape) == 0
                else (
                    TensorOrder.VECTOR
                    if len(shape) == 1
                    else (
                        TensorOrder.MATRIX
                        if len(shape) == 2
                        else (
                            TensorOrder.THIRD_ORDER
                            if len(shape) == 3
                            else (
                                TensorOrder.FOURTH_ORDER
                                if len(shape) == 4
                                else TensorOrder.HIGHER_ORDER
                            )
                        )
                    )
                )
            )

        # Calculate dimension from shape if not provided
        dimension = None
        if shape is not None:
            dimension = 1
            for dim in shape:
                dimension *= dim

        super().__init__(
            structure_id=structure_id,
            structure_type=StructureType.TENSOR,
            dimension=dimension,
            properties=properties,
            tags=tags,
        )

        self.shape = shape
        self.order = order

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert metadata to a dictionary.

        Returns:
            Dictionary representation of the metadata
        """
        data = super().to_dict()
        data.update(
            {"shape": self.shape, "order": self.order.name if self.order else None}
        )
        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "TensorMetadata":
        """
        Create metadata from a dictionary.

        Args:
            data: Dictionary representation of the metadata

        Returns:
            Metadata created from the dictionary
        """
        order = (
            TensorOrder[data["order"]] if "order" in data and data["order"] else None
        )
        return cls(
            structure_id=data.get("structure_id"),
            shape=tuple(data["shape"]) if "shape" in data else None,
            order=order,
            properties=data.get("properties", {}),
            tags=data.get("tags", []),
        )


class Tensor(MathStructure, abc.ABC):
    """
    Base interface for tensor implementations.

    This abstract class defines the common interface that all tensor
    implementations must implement, providing a foundation for
    interoperability between different tensor types.
    """

    def __init__(self, metadata: Optional[TensorMetadata] = None):
        """
        Initialize the tensor.

        Args:
            metadata: Metadata for the tensor
        """
        super().__init__(metadata or TensorMetadata())

    @property
    def tensor_metadata(self) -> TensorMetadata:
        """
        Get the tensor metadata.

        Returns:
            The tensor metadata
        """
        return self.metadata

    @abc.abstractmethod
    def shape(self) -> TensorShape:
        """
        Get the shape of the tensor.

        Returns:
            The shape of the tensor
        """
        pass

    @abc.abstractmethod
    def order(self) -> TensorOrder:
        """
        Get the order of the tensor.

        Returns:
            The order of the tensor
        """
        pass

    def dimension(self) -> int:
        """
        Get the total number of elements in the tensor.

        Returns:
            The total number of elements
        """
        shape = self.shape()
        if not shape:
            return 1  # Scalar

        dim = 1
        for d in shape:
            dim *= d
        return dim

    @abc.abstractmethod
    def get_value(self, indices: Sequence[int]) -> float:
        """
        Get the value at the specified indices.

        Args:
            indices: The indices

        Returns:
            The value at the indices
        """
        pass

    @abc.abstractmethod
    def set_value(self, indices: Sequence[int], value: float) -> None:
        """
        Set the value at the specified indices.

        Args:
            indices: The indices
            value: The value to set
        """
        pass

    @abc.abstractmethod
    def to_numpy(self) -> np.ndarray:
        """
        Convert the tensor to a NumPy array.

        Returns:
            NumPy array representation of the tensor
        """
        pass

    @classmethod
    @abc.abstractmethod
    def from_numpy(
        cls: Type[T], array: np.ndarray, metadata: Optional[TensorMetadata] = None
    ) -> T:
        """
        Create a tensor from a NumPy array.

        Args:
            array: NumPy array representation of the tensor
            metadata: Metadata for the tensor

        Returns:
            Tensor created from the NumPy array
        """
        pass

    @effects([EffectType.COMPUTATION])
    @abc.abstractmethod
    def contract(
        self, other: "Tensor", axes: Optional[Tuple[List[int], List[int]]] = None
    ) -> "Tensor":
        """
        Contract this tensor with another tensor.

        Args:
            other: The other tensor
            axes: The axes to contract (if None, contract the last axis of this tensor with the first axis of the other tensor)

        Returns:
            The contracted tensor
        """
        pass

    @effects([EffectType.COMPUTATION])
    @abc.abstractmethod
    def transpose(self, axes: Optional[Sequence[int]] = None) -> "Tensor":
        """
        Transpose the tensor.

        Args:
            axes: The new order of axes (if None, reverse the axes)

        Returns:
            The transposed tensor
        """
        pass

    @effects([EffectType.COMPUTATION])
    @abc.abstractmethod
    def reshape(self, shape: TensorShape) -> "Tensor":
        """
        Reshape the tensor.

        Args:
            shape: The new shape

        Returns:
            The reshaped tensor
        """
        pass

    def to_vector(self) -> List[float]:
        """
        Convert the tensor to a vector representation.

        Returns:
            Vector representation of the tensor
        """
        # Flatten the tensor to a 1D array
        return self.to_numpy().flatten().tolist()

    @classmethod
    @abc.abstractmethod
    def from_vector(
        cls: Type[T],
        vector: List[float],
        shape: TensorShape,
        metadata: Optional[TensorMetadata] = None,
    ) -> T:
        """
        Create a tensor from a vector representation.

        Args:
            vector: Vector representation of the tensor
            shape: Shape of the tensor
            metadata: Metadata for the tensor

        Returns:
            Tensor created from the vector representation
        """
        pass

    @effects([EffectType.COMPUTATION])
    def transform(self, transformation: Any) -> "Tensor":
        """
        Apply a transformation to the tensor.

        Args:
            transformation: The transformation to apply

        Returns:
            The transformed tensor
        """
        # Default implementation for tensors
        if isinstance(transformation, Tensor) and transformation.order() == 2:
            # Matrix transformation
            return self.contract(transformation)
        else:
            raise ValueError(f"Unsupported transformation type: {type(transformation)}")

    @effects([EffectType.COMPUTATION])
    def combine(self, other: MathStructure) -> MathStructure:
        """
        Combine this tensor with another structure.

        Args:
            other: The other structure to combine with

        Returns:
            The combined structure
        """
        if isinstance(other, Tensor):
            # Tensor product
            return self.outer_product(other)
        else:
            raise ValueError(f"Cannot combine tensor with {type(other)}")

    @effects([EffectType.COMPUTATION])
    @abc.abstractmethod
    def outer_product(self, other: "Tensor") -> "Tensor":
        """
        Compute the outer product of this tensor with another tensor.

        Args:
            other: The other tensor

        Returns:
            The outer product tensor
        """
        pass

    @effects([EffectType.COMPUTATION])
    def distance(self, other: MathStructure) -> float:
        """
        Calculate the distance between this tensor and another structure.

        Args:
            other: The other structure

        Returns:
            The distance between the structures
        """
        if isinstance(other, Tensor):
            # Frobenius norm of the difference
            diff = self.subtract(other)
            return np.sqrt(np.sum(diff.to_numpy() ** 2))
        else:
            raise ValueError(
                f"Cannot calculate distance between tensor and {type(other)}"
            )

    @effects([EffectType.COMPUTATION])
    def similarity(self, other: MathStructure) -> float:
        """
        Calculate the similarity between this tensor and another structure.

        Args:
            other: The other structure

        Returns:
            The similarity between the structures (0.0 to 1.0)
        """
        if isinstance(other, Tensor):
            # Cosine similarity
            a_flat = self.to_numpy().flatten()
            b_flat = other.to_numpy().flatten()

            norm_a = np.linalg.norm(a_flat)
            norm_b = np.linalg.norm(b_flat)

            if norm_a == 0 or norm_b == 0:
                return 0.0

            return np.dot(a_flat, b_flat) / (norm_a * norm_b)
        else:
            raise ValueError(
                f"Cannot calculate similarity between tensor and {type(other)}"
            )

    @effects([EffectType.COMPUTATION])
    @abc.abstractmethod
    def add(self, other: "Tensor") -> "Tensor":
        """
        Add another tensor to this tensor.

        Args:
            other: The other tensor

        Returns:
            The sum tensor
        """
        pass

    @effects([EffectType.COMPUTATION])
    @abc.abstractmethod
    def subtract(self, other: "Tensor") -> "Tensor":
        """
        Subtract another tensor from this tensor.

        Args:
            other: The other tensor

        Returns:
            The difference tensor
        """
        pass

    @effects([EffectType.COMPUTATION])
    @abc.abstractmethod
    def multiply(self, scalar: float) -> "Tensor":
        """
        Multiply this tensor by a scalar.

        Args:
            scalar: The scalar

        Returns:
            The product tensor
        """
        pass

    @effects([EffectType.COMPUTATION])
    @abc.abstractmethod
    def element_wise_multiply(self, other: "Tensor") -> "Tensor":
        """
        Multiply this tensor element-wise by another tensor.

        Args:
            other: The other tensor

        Returns:
            The element-wise product tensor
        """
        pass

    @effects([EffectType.COMPUTATION])
    @abc.abstractmethod
    def norm(self) -> float:
        """
        Calculate the Frobenius norm of the tensor.

        Returns:
            The Frobenius norm
        """
        pass


# Register the tensor structure type
StructureRegistry.register(StructureType.TENSOR, Tensor)
