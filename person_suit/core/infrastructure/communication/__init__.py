"""
Person Suit - Core Infrastructure: Secure Communication Implementation
-------------------------------------------------------------

This package provides the concrete implementations for the secure communication
interfaces defined in `core.application.interfaces.communication`.

It includes:
- Secure Channel and ChannelManager classes with capability-based security.
- Encrypted Message structure implementations with cryptographic verification.
- Telemetry and effect tracking for all communication operations.
- Zero-trust architecture implementation with continuous verification.

Security Features:
- End-to-end encryption for all messages
- Capability-based access control for channels
- Message authentication and integrity verification
- Audit logging for all communication operations
- Protection against replay attacks
"""

# Import message types from application interfaces
from ...application.interfaces.communication import (
    ErrorMessage,
    EventMessage,
    Message,
    MessageHeader,
    MessageType,
    RequestMessage,
    ResponseMessage,
)

# Import secure channel implementations
from ..security.communication import (
    ChannelFactory,
    EncryptedChannel,
    SecureChannel,
    TLSChannel,
)

from .channel import Channel
from .channel_manager import ChannelManager
from .security_handlers import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
)

# Export implementations from modularized files
from .types import Capability, ChannelMetadataImpl, IChannel, IChannelManager

__all__ = [
    # From types.py
    "ChannelMetadataImpl",
    "IChannel",
    "IChannelManager",
    "Capability",
    # From channel.py and channel_manager.py
    "Channel",
    "ChannelManager",
    # From security_handlers.py
    "SecurityCheckHandler",
    "EncryptionHandler",
    "DecryptionHandler",
    # From security.communication
    "SecureChannel",
    "EncryptedChannel",
    "TLSChannel",
    "ChannelFactory",
    # Message types from application interfaces
    "MessageType",
    "MessageHeader",
    "Message",
    "RequestMessage",
    "ResponseMessage",
    "EventMessage",
    "ErrorMessage",
]
