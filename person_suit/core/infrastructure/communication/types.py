"""
Type definitions for the communication infrastructure.

This module contains the type definitions, enums, and dataclasses used by the
communication infrastructure, including channel metadata and message types.
"""

import time
import uuid
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, TypeVar

# Import from application interfaces
from ...application.interfaces.communication import (
    ChannelMetadata,
    ChannelState,
    ChannelType,
    Message,
    MessageType,
    RequestMessage,
    ResponseMessage,
)

# Type variable for generic payload
T = TypeVar("T")


# Placeholder for Capability type
class Capability(str):
    """String-based capability identifier."""

    pass


@dataclass
class ChannelMetadataImpl(ChannelMetadata):
    """
    Concrete metadata for a communication channel, enhanced with security placeholders.

    Attributes:
        channel_id: Unique identifier for the channel.
        channel_type: Type of the channel (DIRECT, BROADCAST, etc.).
        source_id: ID of the source component.
        target_id: ID of the target component (if applicable).
        created_at: Timestamp of channel creation.
        state: Current state of the channel (INIT, OPEN, CLOSED, etc.).
        security_level: Abstract security level (consider replacing with more specific attributes).
        max_message_size: Maximum allowed message size in bytes.
        tags: Arbitrary tags for grouping or filtering channels.
        properties: Additional channel-specific properties.
        # --- Security/Future Placeholders ---
        capability_required: Optional[Capability] # Capability needed to interact via this channel.
        is_encrypted: bool # Indicates if transport encryption is active (Crypto-Agility).
        encryption_algorithm: Optional[str] # Algorithm used (e.g., PQC-based).
        is_formally_verified: bool # Placeholder: Indicates if channel logic meets formal proofs.
        zero_trust_policy_id: Optional[str] # Placeholder: ID of applicable Zero-Trust policy.
        is_quantum_safe: bool # Placeholder: Indicates use of quantum-resistant protocols.
    """

    channel_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    channel_type: ChannelType = ChannelType.DIRECT
    source_id: str = ""
    target_id: Optional[str] = None
    created_at: float = field(default_factory=time.time)
    state: ChannelState = ChannelState.INIT
    security_level: int = 0  # TODO: Refine or replace with specific security attributes
    max_message_size: int = 1024 * 1024  # 1MB default
    tags: List[str] = field(default_factory=list)
    properties: Dict[str, Any] = field(default_factory=dict)
    # --- Security/Future Placeholders ---
    capability_required: Optional[Capability] = (
        None  # Placeholder for required capability
    )
    is_encrypted: bool = False  # Placeholder for Crypto-Agility
    encryption_algorithm: Optional[str] = (
        None  # Placeholder for Crypto-Agility (e.g., Kyber, Dilithium)
    )
    is_formally_verified: bool = False  # Placeholder for Formal Verification status
    zero_trust_policy_id: Optional[str] = None  # Placeholder for Zero-Trust context
    is_quantum_safe: bool = False  # Placeholder for Quantum Resistance status

    @property
    def age(self) -> float:
        """Get the age of the channel in seconds."""
        return time.time() - self.created_at


# Protocol definitions for interfaces
from typing import Awaitable, Callable, Protocol


class IChannel(Protocol):
    """Interface for asynchronous communication channels."""

    @property
    def metadata(self) -> ChannelMetadata:
        """Get the channel metadata."""
        ...

    @property
    def state(self) -> ChannelState:
        """Get the current state of the channel."""
        ...

    async def open(self) -> None:
        """Asynchronously open the channel."""
        ...

    async def close(self, timeout: Optional[float] = None) -> None:
        """Asynchronously close the channel."""
        ...

    async def send(self, message: Message) -> None:
        """Asynchronously send a message through the channel."""
        ...

    async def send_request(
        self, message: RequestMessage, timeout: Optional[float] = None
    ) -> ResponseMessage:
        """Asynchronously send a request and wait for a response."""
        ...

    async def add_handler(
        self, message_type: MessageType, handler: Callable[[Message], Awaitable[None]]
    ) -> None:
        """Register an async handler for a specific message type."""
        ...

    async def remove_handler(
        self, message_type: MessageType, handler: Callable[[Message], Awaitable[None]]
    ) -> None:
        """Remove an async message handler."""
        ...


class IChannelManager(Protocol):
    """Interface for managing asynchronous communication channels."""

    async def create_channel(
        self,
        source_id: str,
        target_id: Optional[str] = None,
        channel_type: ChannelType = ChannelType.DIRECT,
        auto_open: bool = True,
        **properties,
    ) -> IChannel:
        """Asynchronously create and return a new communication channel."""
        ...

    async def get_channel(self, channel_id: str) -> Optional[IChannel]:
        """Asynchronously get a channel by its ID."""
        ...

    async def close_channel(
        self, channel_id: str, timeout: Optional[float] = None
    ) -> bool:
        """Asynchronously close a channel by its ID."""
        ...

    async def close_component_channels(self, component_id: str) -> None:
        """Asynchronously close all channels associated with a component."""
        ...
