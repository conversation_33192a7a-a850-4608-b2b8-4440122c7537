"""
Adaptive Computational Fidelity (ACF) Manager
============================================

This module implements the ACF Manager that serves as the "resource thermostat"
for the CAW paradigm (Principle #3). It dynamically adjusts computational fidelity
based on context, resource availability, and performance feedback.

Key Features:
- Dynamic fidelity adjustment based on resource constraints
- Context-aware fidelity determination
- Performance feedback integration
- Multi-dimensional fidelity control (quality vs. speed vs. resources)
- Graceful degradation strategies

Related Files:
- person_suit/core/caw/context.py: DualContext integration
- person_suit/core/infrastructure/resource_optimization/: Resource monitoring
- person_suit/core/actors/: Actor integration

Dependencies:
- dataclasses: For structured data
- typing: For type annotations
- asyncio: For async operations
- logging: For monitoring and debugging
"""

import asyncio
import logging
import time
from dataclasses import dataclass, field
from enum import Enum, auto
from typing import Any, Dict, List, Optional, Tuple, Callable

from person_suit.core.context import UnifiedContext as DualContext, ACFParams, ResourceType

logger = logging.getLogger(__name__)


class FidelityLevel(Enum):
    """Predefined fidelity levels for common operations."""
    
    MINIMAL = 0.2      # Bare minimum functionality
    LOW = 0.4          # Reduced quality but functional
    MEDIUM = 0.6       # Balanced quality and performance
    HIGH = 0.8         # High quality with good performance
    MAXIMUM = 1.0      # Maximum quality regardless of cost


class AdaptationStrategy(Enum):
    """Strategies for adapting fidelity based on feedback."""
    
    CONSERVATIVE = auto()  # Slow, careful adjustments
    BALANCED = auto()      # Moderate adjustments
    AGGRESSIVE = auto()    # Fast, large adjustments
    PREDICTIVE = auto()    # Use prediction models


@dataclass
class FidelityProfile:
    """A profile defining fidelity settings for different operations."""
    
    name: str
    base_fidelity: float
    operation_modifiers: Dict[str, float] = field(default_factory=dict)
    resource_thresholds: Dict[ResourceType, float] = field(default_factory=dict)
    quality_requirements: Dict[str, float] = field(default_factory=dict)
    
    def get_fidelity_for_operation(self, operation: str, context: DualContext) -> float:
        """Get the appropriate fidelity level for a specific operation."""
        base = self.base_fidelity
        modifier = self.operation_modifiers.get(operation, 1.0)
        
        # Apply context modulation
        context_modulation = context.modulate(operation)
        context_modifier = context_modulation.get("fidelity_level", 1.0)
        
        # Calculate final fidelity
        final_fidelity = base * modifier * context_modifier
        
        # Apply quality requirements
        min_quality = self.quality_requirements.get(operation, 0.0)
        final_fidelity = max(final_fidelity, min_quality)
        
        return max(0.0, min(1.0, final_fidelity))


@dataclass
class PerformanceMetrics:
    """Metrics for tracking ACF performance."""
    
    operation: str
    fidelity_used: float
    quality_achieved: float
    resource_consumption: Dict[ResourceType, float]
    execution_time: float
    success: bool
    timestamp: float = field(default_factory=time.time)
    
    def efficiency_score(self) -> float:
        """Calculate efficiency as quality per resource unit."""
        total_resources = sum(self.resource_consumption.values())
        if total_resources == 0:
            return self.quality_achieved
        return self.quality_achieved / total_resources


class ACFManager:
    """
    Adaptive Computational Fidelity Manager.
    
    This class serves as the "resource thermostat" for the CAW paradigm,
    dynamically adjusting computational fidelity based on:
    - Current resource availability
    - Context requirements and constraints
    - Historical performance data
    - System load and priorities
    """
    
    def __init__(
        self,
        adaptation_strategy: AdaptationStrategy = AdaptationStrategy.BALANCED,
        monitoring_interval: float = 1.0,
        history_size: int = 1000,
    ):
        """
        Initialize the ACF Manager.
        
        Args:
            adaptation_strategy: Strategy for adapting fidelity
            monitoring_interval: How often to check and adapt (seconds)
            history_size: Number of performance metrics to keep
        """
        self.adaptation_strategy = adaptation_strategy
        self.monitoring_interval = monitoring_interval
        self.history_size = history_size
        
        # Performance tracking
        self.performance_history: List[PerformanceMetrics] = []
        self.operation_stats: Dict[str, List[PerformanceMetrics]] = {}
        
        # Fidelity profiles
        self.profiles: Dict[str, FidelityProfile] = {}
        self._create_default_profiles()
        
        # Current system state
        self.current_load: Dict[ResourceType, float] = {}
        self.adaptation_rates: Dict[str, float] = {}
        
        # Monitoring task
        self._monitoring_task: Optional[asyncio.Task] = None
        self._running = False
        
        logger.info(f"ACF Manager initialized with {adaptation_strategy.name} strategy")
    
    def _create_default_profiles(self) -> None:
        """Create default fidelity profiles for common operations."""
        
        # High-fidelity profile for critical operations
        self.profiles["critical"] = FidelityProfile(
            name="critical",
            base_fidelity=0.95,
            operation_modifiers={
                "memory_encoding": 1.0,
                "decision_making": 1.0,
                "analysis": 1.0,
            },
            resource_thresholds={
                ResourceType.CPU: 0.9,
                ResourceType.MEMORY: 0.9,
            },
            quality_requirements={
                "memory_encoding": 0.9,
                "decision_making": 0.85,
            }
        )
        
        # Balanced profile for normal operations
        self.profiles["balanced"] = FidelityProfile(
            name="balanced",
            base_fidelity=0.7,
            operation_modifiers={
                "memory_encoding": 1.1,
                "background_processing": 0.8,
                "analysis": 1.0,
                "prediction": 0.9,
            },
            resource_thresholds={
                ResourceType.CPU: 0.7,
                ResourceType.MEMORY: 0.7,
            },
            quality_requirements={
                "memory_encoding": 0.6,
                "analysis": 0.5,
            }
        )
        
        # Low-resource profile for background operations
        self.profiles["efficient"] = FidelityProfile(
            name="efficient",
            base_fidelity=0.4,
            operation_modifiers={
                "background_processing": 1.0,
                "cleanup": 0.8,
                "maintenance": 0.6,
                "logging": 0.3,
            },
            resource_thresholds={
                ResourceType.CPU: 0.3,
                ResourceType.MEMORY: 0.4,
            },
            quality_requirements={
                "background_processing": 0.3,
            }
        )
    
    async def start_monitoring(self) -> None:
        """Start the ACF monitoring and adaptation loop."""
        if self._running:
            return
        
        self._running = True
        self._monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info("ACF monitoring started")
    
    async def stop_monitoring(self) -> None:
        """Stop the ACF monitoring loop."""
        self._running = False
        if self._monitoring_task:
            self._monitoring_task.cancel()
            try:
                await self._monitoring_task
            except asyncio.CancelledError:
                pass
        logger.info("ACF monitoring stopped")
    
    async def _monitoring_loop(self) -> None:
        """Main monitoring and adaptation loop."""
        while self._running:
            try:
                await self._update_system_state()
                await self._adapt_fidelity_settings()
                await asyncio.sleep(self.monitoring_interval)
            except Exception as e:
                logger.error(f"Error in ACF monitoring loop: {e}")
                await asyncio.sleep(self.monitoring_interval)
    
    async def _update_system_state(self) -> None:
        """Update current system resource state."""
        # This would integrate with actual resource monitoring
        # For now, simulate some basic resource tracking
        
        # Calculate average resource usage from recent operations
        recent_metrics = self.performance_history[-10:] if self.performance_history else []
        
        for resource_type in ResourceType:
            if recent_metrics:
                avg_usage = sum(
                    metrics.resource_consumption.get(resource_type, 0.0)
                    for metrics in recent_metrics
                ) / len(recent_metrics)
                self.current_load[resource_type] = avg_usage
            else:
                self.current_load[resource_type] = 0.0
    
    async def _adapt_fidelity_settings(self) -> None:
        """Adapt fidelity settings based on current performance."""
        if not self.performance_history:
            return
        
        # Analyze recent performance
        recent_metrics = self.performance_history[-20:] if len(self.performance_history) >= 20 else self.performance_history
        
        # Calculate adaptation rates for different operations
        operation_performance = {}
        for metrics in recent_metrics:
            if metrics.operation not in operation_performance:
                operation_performance[metrics.operation] = []
            operation_performance[metrics.operation].append(metrics)
        
        # Adapt based on strategy
        for operation, metrics_list in operation_performance.items():
            if len(metrics_list) < 3:  # Need minimum data
                continue
            
            avg_quality = sum(m.quality_achieved for m in metrics_list) / len(metrics_list)
            avg_efficiency = sum(m.efficiency_score() for m in metrics_list) / len(metrics_list)
            
            # Determine if adaptation is needed
            adaptation_needed = False
            adaptation_direction = 0.0
            
            if avg_quality < 0.5:  # Quality too low
                adaptation_needed = True
                adaptation_direction = 0.1  # Increase fidelity
            elif avg_efficiency < 0.3:  # Efficiency too low
                adaptation_needed = True
                adaptation_direction = -0.05  # Decrease fidelity
            
            if adaptation_needed:
                current_rate = self.adaptation_rates.get(operation, 0.0)
                
                if self.adaptation_strategy == AdaptationStrategy.CONSERVATIVE:
                    new_rate = current_rate + adaptation_direction * 0.5
                elif self.adaptation_strategy == AdaptationStrategy.AGGRESSIVE:
                    new_rate = current_rate + adaptation_direction * 2.0
                else:  # BALANCED
                    new_rate = current_rate + adaptation_direction
                
                self.adaptation_rates[operation] = max(-0.3, min(0.3, new_rate))
                
                logger.debug(f"Adapted fidelity for {operation}: {new_rate:.3f}")
    
    def determine_fidelity(self, context: DualContext, operation: str) -> float:
        """
        Determine the appropriate fidelity level for an operation.
        
        Args:
            context: The context for the operation
            operation: The operation being performed
            
        Returns:
            Fidelity level between 0.0 and 1.0
        """
        # Select appropriate profile based on context priority
        profile_name = self._select_profile(context)
        profile = self.profiles.get(profile_name, self.profiles["balanced"])
        
        # Get base fidelity from profile
        base_fidelity = profile.get_fidelity_for_operation(operation, context)
        
        # Apply adaptation rate if available
        adaptation = self.adaptation_rates.get(operation, 0.0)
        adapted_fidelity = base_fidelity + adaptation
        
        # Apply resource constraints
        resource_constrained_fidelity = self._apply_resource_constraints(
            adapted_fidelity, context, operation
        )
        
        # Ensure within bounds
        final_fidelity = max(0.1, min(1.0, resource_constrained_fidelity))
        
        logger.debug(
            f"Determined fidelity for {operation}: {final_fidelity:.3f} "
            f"(base: {base_fidelity:.3f}, adapted: {adapted_fidelity:.3f})"
        )
        
        return final_fidelity
    
    def _select_profile(self, context: DualContext) -> str:
        """Select the appropriate fidelity profile based on context."""
        if context.priority in ["critical", "high"]:
            return "critical"
        elif context.priority == "low":
            return "efficient"
        else:
            return "balanced"
    
    def _apply_resource_constraints(
        self, fidelity: float, context: DualContext, operation: str
    ) -> float:
        """Apply resource constraints to limit fidelity if resources are scarce."""
        
        # Check available resources in context
        available_resources = context.get_available_resources()
        
        # Calculate resource pressure
        pressure_factors = []
        for resource_type, available in available_resources.items():
            current_load = self.current_load.get(resource_type, 0.0)
            if available > 0:
                pressure = current_load / available
                pressure_factors.append(pressure)
        
        if pressure_factors:
            max_pressure = max(pressure_factors)
            
            # Reduce fidelity if under pressure
            if max_pressure > 0.8:
                pressure_reduction = (max_pressure - 0.8) * 2.0  # Scale factor
                fidelity *= (1.0 - pressure_reduction)
            
        return fidelity
    
    def adapt_parameters(
        self, base_params: Dict[str, Any], fidelity: float
    ) -> Dict[str, Any]:
        """
        Adapt operation parameters based on fidelity level.
        
        Args:
            base_params: Base parameters for the operation
            fidelity: Fidelity level (0.0 to 1.0)
            
        Returns:
            Adapted parameters
        """
        adapted_params = base_params.copy()
        
        # Common parameter adaptations based on fidelity
        if "dimensions" in adapted_params:
            # Scale vector dimensions (following 2048 rule)
            base_dims = adapted_params["dimensions"]
            adapted_params["dimensions"] = int(base_dims * fidelity)
            adapted_params["dimensions"] = max(64, min(2048, adapted_params["dimensions"]))
        
        if "iterations" in adapted_params:
            # Scale iteration count
            base_iterations = adapted_params["iterations"]
            adapted_params["iterations"] = max(1, int(base_iterations * fidelity))
        
        if "batch_size" in adapted_params:
            # Scale batch size
            base_batch = adapted_params["batch_size"]
            adapted_params["batch_size"] = max(1, int(base_batch * fidelity))
        
        if "precision" in adapted_params:
            # Adjust numerical precision
            if fidelity < 0.3:
                adapted_params["precision"] = "float16"
            elif fidelity < 0.7:
                adapted_params["precision"] = "float32"
            else:
                adapted_params["precision"] = "float64"
        
        if "quality_threshold" in adapted_params:
            # Adjust quality thresholds
            base_threshold = adapted_params["quality_threshold"]
            adapted_params["quality_threshold"] = base_threshold * fidelity
        
        return adapted_params
    
    def record_performance(
        self,
        operation: str,
        fidelity_used: float,
        quality_achieved: float,
        resource_consumption: Dict[ResourceType, float],
        execution_time: float,
        success: bool,
    ) -> None:
        """
        Record performance metrics for an operation.
        
        Args:
            operation: Name of the operation
            fidelity_used: Fidelity level that was used
            quality_achieved: Quality score achieved (0.0 to 1.0)
            resource_consumption: Resources consumed
            execution_time: Time taken to execute
            success: Whether the operation succeeded
        """
        metrics = PerformanceMetrics(
            operation=operation,
            fidelity_used=fidelity_used,
            quality_achieved=quality_achieved,
            resource_consumption=resource_consumption,
            execution_time=execution_time,
            success=success,
        )
        
        # Add to global history
        self.performance_history.append(metrics)
        if len(self.performance_history) > self.history_size:
            self.performance_history.pop(0)
        
        # Add to operation-specific stats
        if operation not in self.operation_stats:
            self.operation_stats[operation] = []
        self.operation_stats[operation].append(metrics)
        if len(self.operation_stats[operation]) > 100:  # Keep last 100 per operation
            self.operation_stats[operation].pop(0)
        
        logger.debug(
            f"Recorded performance for {operation}: "
            f"fidelity={fidelity_used:.3f}, quality={quality_achieved:.3f}, "
            f"time={execution_time:.3f}s, success={success}"
        )
    
    def get_operation_statistics(self, operation: str) -> Dict[str, float]:
        """Get performance statistics for a specific operation."""
        if operation not in self.operation_stats:
            return {}
        
        metrics_list = self.operation_stats[operation]
        if not metrics_list:
            return {}
        
        successful_metrics = [m for m in metrics_list if m.success]
        if not successful_metrics:
            return {"success_rate": 0.0}
        
        return {
            "success_rate": len(successful_metrics) / len(metrics_list),
            "avg_quality": sum(m.quality_achieved for m in successful_metrics) / len(successful_metrics),
            "avg_fidelity": sum(m.fidelity_used for m in successful_metrics) / len(successful_metrics),
            "avg_execution_time": sum(m.execution_time for m in successful_metrics) / len(successful_metrics),
            "avg_efficiency": sum(m.efficiency_score() for m in successful_metrics) / len(successful_metrics),
            "total_operations": len(metrics_list),
        }
    
    def create_custom_profile(
        self,
        name: str,
        base_fidelity: float,
        operation_modifiers: Optional[Dict[str, float]] = None,
        resource_thresholds: Optional[Dict[ResourceType, float]] = None,
        quality_requirements: Optional[Dict[str, float]] = None,
    ) -> None:
        """Create a custom fidelity profile."""
        self.profiles[name] = FidelityProfile(
            name=name,
            base_fidelity=base_fidelity,
            operation_modifiers=operation_modifiers or {},
            resource_thresholds=resource_thresholds or {},
            quality_requirements=quality_requirements or {},
        )
        logger.info(f"Created custom ACF profile: {name}")
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get current ACF system status."""
        return {
            "running": self._running,
            "adaptation_strategy": self.adaptation_strategy.name,
            "current_load": self.current_load,
            "adaptation_rates": self.adaptation_rates,
            "total_operations": len(self.performance_history),
            "profiles": list(self.profiles.keys()),
            "recent_performance": {
                op: self.get_operation_statistics(op)
                for op in set(m.operation for m in self.performance_history[-50:])
            } if self.performance_history else {},
        } 