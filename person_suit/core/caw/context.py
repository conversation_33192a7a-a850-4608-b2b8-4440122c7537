"""
CAW Context Implementation
========================

This module provides the enhanced DualContext class that serves as the first-class
context object for the Contextual Adaptive Wave (CAW) paradigm. It integrates:

- Wave-particle duality representation
- Adaptive Computational Fidelity (ACF) settings
- Resource awareness and constraints
- Context propagation and composition
- CAW principle alignment

Related Files:
- person_suit/core/infrastructure/dual_wave/context.py: Original DualContext
- person_suit/core/caw/acf.py: ACF implementation
- person_suit/core/actors/base.py: Actor context integration

Dependencies:
- dataclasses: For structured data
- typing: For type annotations
- uuid: For unique identifiers
- time: For timestamps
"""

import logging
import time
import uuid
from dataclasses import dataclass, field
from enum import Enum, auto
from typing import Any, Dict, List, Optional, Union

logger = logging.getLogger(__name__)


class ObservationMode(Enum):
    """Observation modes for wave-particle duality."""
    
    WAVE_FOCUSED = auto()      # Emphasize wave aspects
    PARTICLE_FOCUSED = auto()  # Emphasize particle aspects
    BALANCED = auto()          # Balance both aspects
    ADAPTIVE = auto()          # Adapt based on context


class ResourceType(Enum):
    """Types of computational resources."""
    
    CPU = auto()
    MEMORY = auto()
    NETWORK = auto()
    STORAGE = auto()
    GPU = auto()


@dataclass
class ACFParams:
    """Adaptive Computational Fidelity parameters."""
    
    fidelity_level: float = 0.8  # 0.0 (minimal) to 1.0 (maximum)
    resource_budget: Dict[ResourceType, float] = field(default_factory=dict)
    quality_threshold: float = 0.7
    adaptation_rate: float = 0.1
    enable_degradation: bool = True
    
    def __post_init__(self):
        """Validate ACF parameters."""
        self.fidelity_level = max(0.0, min(1.0, self.fidelity_level))
        self.quality_threshold = max(0.0, min(1.0, self.quality_threshold))
        self.adaptation_rate = max(0.0, min(1.0, self.adaptation_rate))


@dataclass
class ContextConstraint:
    """A constraint that influences context behavior."""
    
    type: str  # e.g., "time_limit", "memory_limit", "quality_requirement"
    value: Any
    priority: float = 1.0  # Higher values = more important
    
    def is_satisfied(self, current_state: Dict[str, Any]) -> bool:
        """Check if the constraint is satisfied given current state."""
        # Implementation depends on constraint type
        if self.type == "time_limit":
            return current_state.get("elapsed_time", 0) <= self.value
        elif self.type == "memory_limit":
            return current_state.get("memory_usage", 0) <= self.value
        elif self.type == "quality_requirement":
            return current_state.get("quality_score", 0) >= self.value
        return True


@dataclass
class DualContext:
    """
    Enhanced context for CAW paradigm with ACF integration.
    
    This serves as the first-class context object that:
    - Propagates through all CAW operations
    - Carries ACF settings for adaptive fidelity
    - Manages resource constraints and budgets
    - Supports wave-particle duality modulation
    - Enables context composition and inheritance
    """
    
    # Core context properties
    domain: str
    priority: str = "normal"
    goals: List[str] = field(default_factory=list)
    
    # Resource and ACF management
    acf_setting: ACFParams = field(default_factory=ACFParams)
    resources: Dict[ResourceType, float] = field(default_factory=dict)
    constraints: List[ContextConstraint] = field(default_factory=list)
    
    # Wave-particle duality
    wave_particle_ratio: float = 0.5  # 0.0 = pure particle, 1.0 = pure wave
    observation_mode: ObservationMode = ObservationMode.BALANCED
    
    # Context metadata
    context_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    parent_context_id: Optional[str] = None
    propagation_path: List[str] = field(default_factory=list)
    creation_time: float = field(default_factory=time.time)
    properties: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Initialize and validate context after creation."""
        # Validate wave-particle ratio
        self.wave_particle_ratio = max(0.0, min(1.0, self.wave_particle_ratio))
        
        # Add self to propagation path
        if self.context_id not in self.propagation_path:
            self.propagation_path.append(self.context_id)
        
        logger.debug(f"Created DualContext {self.context_id} for domain '{self.domain}'")
    
    def compose(self, other: 'DualContext') -> 'DualContext':
        """
        Compose this context with another, creating a new merged context.
        
        Args:
            other: The other context to compose with
            
        Returns:
            A new composed context
        """
        # Merge domains
        new_domain = f"{self.domain}+{other.domain}"
        
        # Take higher priority
        priority_order = {"low": 1, "normal": 2, "high": 3, "critical": 4}
        self_priority = priority_order.get(self.priority, 2)
        other_priority = priority_order.get(other.priority, 2)
        new_priority = "high" if max(self_priority, other_priority) >= 3 else "normal"
        
        # Merge goals
        new_goals = list(set(self.goals + other.goals))
        
        # Merge ACF settings (take more restrictive)
        new_acf = ACFParams(
            fidelity_level=min(self.acf_setting.fidelity_level, other.acf_setting.fidelity_level),
            resource_budget={**self.acf_setting.resource_budget, **other.acf_setting.resource_budget},
            quality_threshold=max(self.acf_setting.quality_threshold, other.acf_setting.quality_threshold),
            adaptation_rate=(self.acf_setting.adaptation_rate + other.acf_setting.adaptation_rate) / 2,
            enable_degradation=self.acf_setting.enable_degradation and other.acf_setting.enable_degradation,
        )
        
        # Merge resources (take minimum available)
        new_resources = {}
        all_resource_types = set(self.resources.keys()) | set(other.resources.keys())
        for resource_type in all_resource_types:
            self_amount = self.resources.get(resource_type, float('inf'))
            other_amount = other.resources.get(resource_type, float('inf'))
            new_resources[resource_type] = min(self_amount, other_amount)
        
        # Merge constraints
        new_constraints = self.constraints + other.constraints
        
        # Average wave-particle ratio
        new_wave_particle_ratio = (self.wave_particle_ratio + other.wave_particle_ratio) / 2
        
        # Merge propagation paths
        new_propagation_path = self.propagation_path + other.propagation_path
        
        # Merge properties
        new_properties = {**self.properties, **other.properties}
        
        return DualContext(
            domain=new_domain,
            priority=new_priority,
            goals=new_goals,
            acf_setting=new_acf,
            resources=new_resources,
            constraints=new_constraints,
            wave_particle_ratio=new_wave_particle_ratio,
            observation_mode=self.observation_mode,  # Keep self's observation mode
            parent_context_id=self.context_id,
            propagation_path=new_propagation_path,
            properties=new_properties,
        )
    
    def restrict(self, domain_filter: str) -> 'DualContext':
        """
        Create a restricted version of this context for a specific domain.
        
        Args:
            domain_filter: The domain to restrict to
            
        Returns:
            A new restricted context
        """
        # Filter goals relevant to the domain
        filtered_goals = [goal for goal in self.goals if domain_filter in goal.lower()]
        
        # Reduce ACF fidelity for restricted context
        restricted_acf = ACFParams(
            fidelity_level=self.acf_setting.fidelity_level * 0.8,
            resource_budget={k: v * 0.8 for k, v in self.acf_setting.resource_budget.items()},
            quality_threshold=self.acf_setting.quality_threshold,
            adaptation_rate=self.acf_setting.adaptation_rate,
            enable_degradation=self.acf_setting.enable_degradation,
        )
        
        # Filter constraints relevant to the domain
        filtered_constraints = [c for c in self.constraints if domain_filter in c.type.lower()]
        
        return DualContext(
            domain=domain_filter,
            priority=self.priority,
            goals=filtered_goals,
            acf_setting=restricted_acf,
            resources=self.resources.copy(),
            constraints=filtered_constraints,
            wave_particle_ratio=self.wave_particle_ratio,
            observation_mode=self.observation_mode,
            parent_context_id=self.context_id,
            propagation_path=self.propagation_path + [f"restricted_{domain_filter}"],
            properties=self.properties.copy(),
        )
    
    def modulate(self, operation: str) -> Dict[str, Any]:
        """
        Modulate context parameters for a specific operation.
        
        Args:
            operation: The operation being performed
            
        Returns:
            Modulated parameters for the operation
        """
        modulation = {
            "fidelity_level": self.acf_setting.fidelity_level,
            "wave_particle_ratio": self.wave_particle_ratio,
            "priority_weight": {"low": 0.5, "normal": 1.0, "high": 1.5, "critical": 2.0}.get(self.priority, 1.0),
            "resource_multiplier": 1.0,
        }
        
        # Operation-specific modulation
        if operation in ["memory_encoding", "analysis"]:
            # Increase fidelity for critical operations
            modulation["fidelity_level"] *= 1.2
            modulation["resource_multiplier"] = 1.5
        elif operation in ["background_processing", "cleanup"]:
            # Reduce fidelity for background operations
            modulation["fidelity_level"] *= 0.7
            modulation["resource_multiplier"] = 0.5
        
        # Apply constraints
        for constraint in self.constraints:
            if constraint.type == "quality_requirement":
                modulation["fidelity_level"] = max(modulation["fidelity_level"], constraint.value)
            elif constraint.type == "resource_limit":
                modulation["resource_multiplier"] = min(modulation["resource_multiplier"], constraint.value)
        
        return modulation
    
    def get_available_resources(self) -> Dict[ResourceType, float]:
        """Get currently available resources."""
        return self.resources.copy()
    
    def consume_resources(self, consumption: Dict[ResourceType, float]) -> bool:
        """
        Attempt to consume resources from the context.
        
        Args:
            consumption: Resources to consume
            
        Returns:
            True if resources were successfully consumed
        """
        # Check if we have enough resources
        for resource_type, amount in consumption.items():
            available = self.resources.get(resource_type, 0.0)
            if available < amount:
                return False
        
        # Consume resources
        for resource_type, amount in consumption.items():
            self.resources[resource_type] -= amount
        
        return True
    
    def add_constraint(self, constraint: ContextConstraint) -> None:
        """Add a constraint to the context."""
        self.constraints.append(constraint)
    
    def check_constraints(self, current_state: Dict[str, Any]) -> List[ContextConstraint]:
        """
        Check which constraints are violated.
        
        Args:
            current_state: Current system state
            
        Returns:
            List of violated constraints
        """
        violated = []
        for constraint in self.constraints:
            if not constraint.is_satisfied(current_state):
                violated.append(constraint)
        return violated
    
    def adapt_fidelity(self, performance_feedback: Dict[str, float]) -> None:
        """
        Adapt ACF settings based on performance feedback.
        
        Args:
            performance_feedback: Metrics about recent performance
        """
        quality_score = performance_feedback.get("quality_score", 0.5)
        resource_usage = performance_feedback.get("resource_usage", 0.5)
        
        # Adapt fidelity based on feedback
        if quality_score < self.acf_setting.quality_threshold:
            # Increase fidelity if quality is too low
            adjustment = self.acf_setting.adaptation_rate * (self.acf_setting.quality_threshold - quality_score)
            self.acf_setting.fidelity_level = min(1.0, self.acf_setting.fidelity_level + adjustment)
        elif resource_usage > 0.8:
            # Decrease fidelity if resource usage is too high
            adjustment = self.acf_setting.adaptation_rate * (resource_usage - 0.8)
            self.acf_setting.fidelity_level = max(0.1, self.acf_setting.fidelity_level - adjustment)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert context to dictionary representation."""
        return {
            "context_id": self.context_id,
            "domain": self.domain,
            "priority": self.priority,
            "goals": self.goals,
            "acf_setting": {
                "fidelity_level": self.acf_setting.fidelity_level,
                "resource_budget": {rt.name: amount for rt, amount in self.acf_setting.resource_budget.items()},
                "quality_threshold": self.acf_setting.quality_threshold,
                "adaptation_rate": self.acf_setting.adaptation_rate,
                "enable_degradation": self.acf_setting.enable_degradation,
            },
            "resources": {rt.name: amount for rt, amount in self.resources.items()},
            "constraints": [{"type": c.type, "value": c.value, "priority": c.priority} for c in self.constraints],
            "wave_particle_ratio": self.wave_particle_ratio,
            "observation_mode": self.observation_mode.name,
            "parent_context_id": self.parent_context_id,
            "propagation_path": self.propagation_path,
            "creation_time": self.creation_time,
            "properties": self.properties,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DualContext':
        """Create context from dictionary representation."""
        # Reconstruct ACF settings
        acf_data = data.get("acf_setting", {})
        acf_setting = ACFParams(
            fidelity_level=acf_data.get("fidelity_level", 0.8),
            resource_budget={ResourceType[rt]: amount for rt, amount in acf_data.get("resource_budget", {}).items()},
            quality_threshold=acf_data.get("quality_threshold", 0.7),
            adaptation_rate=acf_data.get("adaptation_rate", 0.1),
            enable_degradation=acf_data.get("enable_degradation", True),
        )
        
        # Reconstruct resources
        resources = {ResourceType[rt]: amount for rt, amount in data.get("resources", {}).items()}
        
        # Reconstruct constraints
        constraints = [
            ContextConstraint(type=c["type"], value=c["value"], priority=c.get("priority", 1.0))
            for c in data.get("constraints", [])
        ]
        
        return cls(
            domain=data["domain"],
            priority=data.get("priority", "normal"),
            goals=data.get("goals", []),
            acf_setting=acf_setting,
            resources=resources,
            constraints=constraints,
            wave_particle_ratio=data.get("wave_particle_ratio", 0.5),
            observation_mode=ObservationMode[data.get("observation_mode", "BALANCED")],
            context_id=data.get("context_id", str(uuid.uuid4())),
            parent_context_id=data.get("parent_context_id"),
            propagation_path=data.get("propagation_path", []),
            creation_time=data.get("creation_time", time.time()),
            properties=data.get("properties", {}),
        )
    
    @classmethod
    def create_default(cls, domain: str, priority: str = "normal") -> 'DualContext':
        """Create a default context for a domain."""
        return cls(
            domain=domain,
            priority=priority,
            acf_setting=ACFParams(),
            resources={
                ResourceType.CPU: 1.0,
                ResourceType.MEMORY: 1.0,
                ResourceType.NETWORK: 1.0,
                ResourceType.STORAGE: 1.0,
            }
        )
    
    @classmethod
    def create_high_fidelity(cls, domain: str) -> 'DualContext':
        """Create a high-fidelity context for critical operations."""
        return cls(
            domain=domain,
            priority="high",
            acf_setting=ACFParams(
                fidelity_level=0.95,
                quality_threshold=0.9,
                enable_degradation=False,
            ),
            resources={
                ResourceType.CPU: 2.0,
                ResourceType.MEMORY: 2.0,
                ResourceType.NETWORK: 1.5,
                ResourceType.STORAGE: 1.5,
            }
        )
    
    @classmethod
    def create_low_resource(cls, domain: str) -> 'DualContext':
        """Create a low-resource context for background operations."""
        return cls(
            domain=domain,
            priority="low",
            acf_setting=ACFParams(
                fidelity_level=0.5,
                quality_threshold=0.4,
                enable_degradation=True,
            ),
            resources={
                ResourceType.CPU: 0.3,
                ResourceType.MEMORY: 0.3,
                ResourceType.NETWORK: 0.2,
                ResourceType.STORAGE: 0.5,
            }
        ) 