# -*- coding: utf-8 -*-
"""
Defines the DualInformation class, the primary container for CAW state.

This class encapsulates both the WaveState (potential, field-like) and
ParticleState (structured, hypergraph) aspects of information within the
Contextual Adaptive Wave (CAW) paradigm, along with state versioning metadata.

It is designed to be immutable, ensuring that operations produce new
instances rather than modifying the state in place.

Related Files:
- person_suit.core.caw.wave_state.py
- person_suit.core.caw.particle_state.py
- docs/CAW_REPRESENTATION_PLAN.md
- schemas.python_schema.py
- docs/design/DualInformation_Implementation_Design.md

CAW-Aligned Contextual Adaptation & Effect System Integration:
-------------------------------------------------------------
This module provides methods for context-dependent adaptation and effectful state transitions:

- collapse_to_particle(context, ...):
    Collapses the wave state into a new particle state using CAW context and modular wave-particle interaction logic.
    Applies generated effects to the particle state, returning a new DualInformation instance.

- propagate_as_wave(context):
    Applies context-dependent modulation to the wave state, supporting CAW's contextual computation.
    Returns a new DualInformation instance with the updated wave state.

- apply_effects_from_wave(context, ...):
    Generates a list of BaseEffect objects representing proposed state changes from the wave state acting on the particle state.
    Downstream systems (memory, event bus, effect engine) can consume these effects for further processing or logging.

- log_effect(effect):
    Logs or records an effect (placeholder for CAW effect/event log integration).

Effect application logic is modularized via the _apply_effects_to_particle_state static method, which interprets and applies common effect types to the immutable hypergraph.

Usage Notes for Downstream Consumers:
-------------------------------------
- The output of collapse_to_particle and propagate_as_wave is a new, fully updated DualInformation instance, ready for use by memory, event bus, or effect engine systems.
- The output of apply_effects_from_wave is a list of BaseEffect objects, which can be logged, persisted, or further processed by downstream systems.
- This design supports CAW principles of explicit effect management, context propagation, and compositional state evolution.
"""

import time
import uuid
from dataclasses import dataclass, field
from typing import Optional, Type, TypeVar, Callable, Dict, Any
import logging

from .particle_state import ImmutableHypergraph
from .schemas import StateRef, Context, BaseEffect

# Import the concrete WaveState implementation
from .wave_state import ImmutableTensorWaveState

# Type variable for the class itself
DI = TypeVar("DI", bound="DualInformation")

# Type variables
# Use the imported concrete type for ParticleState placeholder
ParticleState = ImmutableHypergraph # Type Alias for clarity in this module
# Use the imported concrete type for WaveState placeholder
WaveState = ImmutableTensorWaveState # Type Alias for clarity

# Helper to generate StateRef
def _generate_state_ref() -> StateRef:
    """Generates a unique StateRef, typically a UUID string."""
    return StateRef(str(uuid.uuid4()))


@dataclass(frozen=True)
class DualInformation:
    """
    Immutable container holding WaveState, ParticleState, and versioning metadata.

    Represents the core CAW concept of information having both wave-like
    (potential, continuous) and particle-like (actualized, discrete)
    properties.

    Instances are typically created via the `create_initial` or
    `create_next_version` factory methods.

    Attributes:
        wave_state: The wave aspect representation (e.g., tensor field). Can be
            None if the information is purely particle-based or if the wave
            aspect hasn't been computed yet. Defaults to an empty state if
            None is provided during creation.
        particle_state: The particle aspect representation (e.g., hypergraph).
            Must be provided.
        state_ref: A unique reference (e.g., UUID string) identifying this
            immutable version of the state.
        previous_state_ref: Optional reference to the StateRef of the state
            version from which this one was derived. None for initial states.
        timestamp: The time (seconds since epoch) when this state version
            was created.
    """

    # Core state components
    wave_state: Optional[WaveState]  # Holds the potential/field aspect
    particle_state: ParticleState  # Holds the structured/actualized aspect

    # Metadata managed by factory methods
    state_ref: StateRef
    previous_state_ref: Optional[StateRef] = field(default=None, compare=False)
    timestamp: Optional[float] = field(default=None, compare=False)

    # --- Factory Methods --- #

    @classmethod
    def create_initial(
        cls: Type[DI],
        particle_state: ParticleState,
        wave_state: Optional[WaveState] = None,
    ) -> DI:
        """
        Factory method to create the first version of a DualInformation state.

        Generates a unique state_ref and sets the timestamp.

        Args:
            cls: The class itself.
            particle_state: The initial ParticleState (immutable hypergraph).
                Cannot be None.
            wave_state: The initial WaveState (immutable tensor). If None,
                an empty WaveState (`ConcreteWaveState.empty()`) will be used.

        Returns:
            A new, immutable DualInformation instance representing the initial state.

        Raises:
            ValueError: If particle_state is None.
        """
        current_time = time.time()
        state_ref = _generate_state_ref()
        actual_wave_state = (
            wave_state if wave_state is not None else WaveState.empty()
        )

        if particle_state is None:
            # Or default: particle_state = ConcreteParticleState.empty()
            raise ValueError("Initial particle_state cannot be None")

        return cls(
            wave_state=actual_wave_state,
            particle_state=particle_state,
            state_ref=state_ref,
            previous_state_ref=None,
            timestamp=current_time,
        )

    @classmethod
    def create_next_version(
        cls: Type[DI],
        previous_state: "DualInformation",
        new_particle_state: ParticleState,
        new_wave_state: Optional[WaveState],
    ) -> DI:
        """
        Factory method to create the next immutable version of a DualInformation state.

        Used by state management logic (e.g., Central State Actor) after applying
        an Effect. Generates a new state_ref, links to the previous state, and
        sets a new timestamp.

        Assumes the provided new states potentially share structure with their
        predecessors via copy-on-write mechanisms within their respective
        implementations for performance.

        Args:
            cls: The class itself.
            previous_state: The immediately preceding DualInformation instance.
            new_particle_state: The newly computed immutable ParticleState.
                Cannot be None.
            new_wave_state: The newly computed immutable WaveState. If None,
                an empty WaveState (`ConcreteWaveState.empty()`) will be used.

        Returns:
            A new, immutable DualInformation instance representing the updated state.

        Raises:
            ValueError: If new_particle_state is None.
        """
        current_time = time.time()
        new_state_ref = _generate_state_ref()

        actual_new_wave_state = (
            new_wave_state if new_wave_state is not None else WaveState.empty()
        )

        if new_particle_state is None:
            raise ValueError("New particle_state cannot be None in create_next_version")

        return cls(
            wave_state=actual_new_wave_state,
            particle_state=new_particle_state,
            state_ref=new_state_ref,
            previous_state_ref=previous_state.state_ref,
            timestamp=current_time,
        )

    # --- Accessors ---
    # Dataclass(frozen=True) provides default read-only accessors.

    # --- Update Methods Removed ---
    # State updates should primarily occur via `create_next_version`
    # def with_wave_state(...)
    # def with_particle_state(...)

    # --- CAW-Aligned Methods for Contextual Adaptation and Effects --- #

    def collapse_to_particle(
        self,
        context: Context,
        node_to_index: Optional[dict] = None,
    ) -> "DualInformation":
        """
        Collapse the wave state into a new particle state using CAW context and interaction logic.

        Args:
            context: The context in which to perform the collapse.
            node_to_index: Optional mapping from NodeID to tensor index (required for wave-particle interaction).

        Returns:
            A new DualInformation instance with the updated particle state and a new version.
        """
        from . import wave_particle_interaction
        if self.wave_state is None:
            # No wave state to collapse; return self
            return self
        if node_to_index is None:
            # Default: identity mapping if possible
            node_to_index = {nid: i for i, nid in enumerate(self.particle_state.nodes.keys())}
        # Generate effects from wave->particle
        effects = wave_particle_interaction.apply_wave_potential_to_particle(
            self.wave_state, self.particle_state, context, context.acf_setting.__dict__, node_to_index
        )
        # Apply effects to particle state using the utility function
        new_particle_state = _apply_effects_to_particle_state(self.particle_state, effects)
        # Return a new DualInformation with updated particle state
        return DualInformation.create_next_version(
            previous_state=self,
            new_particle_state=new_particle_state,
            new_wave_state=self.wave_state,
        )

    # --- Refactored Effect Application --- #

    def propagate_as_wave(
        self,
        context: Context,
    ) -> "DualInformation":
        """
        Apply context-dependent modulation to the wave state (CAW principle).

        Args:
            context: The context in which to propagate the wave.

        Returns:
            A new DualInformation instance with the updated wave state and a new version.
        """
        if self.wave_state is None:
            return self
        new_wave_state = self.wave_state.apply_contextual_modulation(context)
        return DualInformation.create_next_version(
            previous_state=self,
            new_particle_state=self.particle_state,
            new_wave_state=new_wave_state,
        )

    def apply_effects_from_wave(
        self,
        context: Context,
        node_to_index: Optional[dict] = None,
    ) -> list:
        """
        Generate effects from the wave state acting on the particle state in a given context.

        Args:
            context: The context for effect generation.
            node_to_index: Optional mapping from NodeID to tensor index.

        Returns:
            List of BaseEffect objects representing proposed state changes.
        """
        from . import wave_particle_interaction
        if self.wave_state is None:
            return []
        if node_to_index is None:
            node_to_index = {nid: i for i, nid in enumerate(self.particle_state.nodes.keys())}
        effects = wave_particle_interaction.apply_wave_potential_to_particle(
            self.wave_state, self.particle_state, context, context.acf_setting.__dict__, node_to_index
        )
        return effects

    def log_effect(self, effect: BaseEffect) -> None:
        """
        Log or record an effect (placeholder for CAW effect/event log integration).

        Args:
            effect: The effect to log.
        """
        logging.info(f"Effect logged: {effect}")

    # --- New Enhanced Methods for Wave-Particle Duality --- #
    
    def interfere_with(self, other: "DualInformation", context: Context) -> "DualInformation":
        """
        Create a new DualInformation instance resulting from the interference of this one with another.
        
        This implements the wave-particle interference pattern from CAW, where two information entities
        can combine to produce a new entity with emergent properties.
        
        Args:
            other: Another DualInformation instance to interfere with.
            context: The context in which the interference occurs.
            
        Returns:
            A new DualInformation instance resulting from the interference.
        """
        from . import wave_particle_interaction
        
        # Interfere wave states if both exist
        new_wave_state = None
        if self.wave_state is not None and other.wave_state is not None:
            # Delegate to wave state interference logic
            new_wave_state = self.wave_state.interfere_with(other.wave_state, context)
        elif self.wave_state is not None:
            new_wave_state = self.wave_state
        elif other.wave_state is not None:
            new_wave_state = other.wave_state
            
        # Generate combined particle state from interference
        # First, we combine both particle states 
        combined_particle = wave_particle_interaction.combine_particle_states(
            self.particle_state, other.particle_state, context
        )
        
        # Apply interference effects from the wave states to the combined particle state
        effects = wave_particle_interaction.generate_interference_effects(
            new_wave_state, combined_particle, context
        )
        
        # Apply the effects to get the final particle state
        new_particle_state = _apply_effects_to_particle_state(combined_particle, effects)
        
        # Create new DualInformation instance
        return DualInformation.create_next_version(
            previous_state=self,  # Using self as previous state, could also track both parents
            new_particle_state=new_particle_state,
            new_wave_state=new_wave_state
        )
    
    def expand_to_wave(self, context: Context) -> "DualInformation":
        """
        Expand a particle-focused state into a wave-focused state.
        
        This is the reverse of collapse_to_particle, converting discrete structural information
        into a distributed potential field representation.
        
        Args:
            context: The context in which to perform the expansion.
            
        Returns:
            A new DualInformation instance with enhanced wave state.
        """
        from . import wave_particle_interaction
        
        # If already has significant wave state, just return self
        if self.wave_state is not None and self.wave_state.is_significant():
            return self
            
        # Generate wave state from particle state
        new_wave_state = wave_particle_interaction.generate_wave_from_particle(
            self.particle_state, context
        )
        
        # Return new state with enhanced wave representation
        return DualInformation.create_next_version(
            previous_state=self,
            new_particle_state=self.particle_state,
            new_wave_state=new_wave_state
        )
    
    def transform_with_context(self, context: Context, transform_type: str = "adaptive") -> "DualInformation":
        """
        Apply a context-dependent transformation to both wave and particle states.
        
        This implements Contextual Computation (CAW Principle 2) by allowing context to 
        actively modulate the information representation.
        
        Args:
            context: The context driving the transformation.
            transform_type: Type of transformation to apply ("adaptive", "focus_wave", "focus_particle")
            
        Returns:
            A new DualInformation instance transformed by the context.
        """
        from . import wave_particle_interaction
        
        # Apply Adaptive Computational Fidelity based on context and transform type
        acf_factors = context.acf_setting.__dict__ if hasattr(context, 'acf_setting') else {}
        
        # Adjust wave-particle balance based on transform type
        if transform_type == "focus_wave":
            # Enhance wave aspects
            new_wave_state = wave_particle_interaction.enhance_wave_state(
                self.wave_state, context, acf_factors, particle_state=self.particle_state
            )
            # Maintain particle state
            new_particle_state = self.particle_state
            
        elif transform_type == "focus_particle":
            # Maintain or minimize wave state
            new_wave_state = self.wave_state
            # Enhance particle aspects
            effects = wave_particle_interaction.generate_contextual_particle_effects(
                self.particle_state, context, acf_factors
            )
            new_particle_state = _apply_effects_to_particle_state(self.particle_state, effects)
            
        else:  # "adaptive" (default)
            # Balance wave and particle based on context
            new_wave_state = wave_particle_interaction.adapt_wave_state(
                self.wave_state, context, acf_factors, particle_state=self.particle_state
            )
            effects = wave_particle_interaction.generate_contextual_particle_effects(
                self.particle_state, context, acf_factors, wave_state=new_wave_state
            )
            new_particle_state = _apply_effects_to_particle_state(self.particle_state, effects)
        
        # Create new transformed state
        return DualInformation.create_next_version(
            previous_state=self,
            new_particle_state=new_particle_state,
            new_wave_state=new_wave_state
        )
    
    def calculate_coherence(self, context: Context) -> float:
        """
        Calculate the coherence between wave and particle representations.
        
        This measures how well the wave and particle states align with each other,
        reflecting the overall consistency of the dual representation.
        
        Args:
            context: The context in which to evaluate coherence.
            
        Returns:
            A coherence score between 0.0 (completely incoherent) and 1.0 (perfectly coherent).
        """
        from . import wave_particle_interaction
        
        if self.wave_state is None:
            # No wave state, so coherence is undefined
            return 0.0
            
        return wave_particle_interaction.calculate_state_coherence(
            self.wave_state, self.particle_state, context
        )
    
    def entangle_with(self, other: "DualInformation", context: Context) -> tuple["DualInformation", "DualInformation"]:
        """
        Create entangled versions of two DualInformation instances.
        
        In the CAW paradigm, entanglement creates a non-local connection between
        two information entities, such that operations on one affect the other.
        
        Args:
            other: Another DualInformation instance to entangle with.
            context: The context in which entanglement occurs.
            
        Returns:
            A tuple of two new DualInformation instances that are entangled.
        """
        from . import wave_particle_interaction
        
        # Create entangled wave states
        entangled_wave_1, entangled_wave_2 = wave_particle_interaction.create_entangled_wave_states(
            self.wave_state, other.wave_state, context
        )
        
        # Mark particle states as entangled through metadata
        entangled_particle_1 = self.particle_state.with_entanglement_metadata(
            other.particle_state, context
        )
        entangled_particle_2 = other.particle_state.with_entanglement_metadata(
            self.particle_state, context
        )
        
        # Create new entangled DualInformation instances
        entangled_1 = DualInformation.create_next_version(
            previous_state=self,
            new_particle_state=entangled_particle_1,
            new_wave_state=entangled_wave_1
        )
        
        entangled_2 = DualInformation.create_next_version(
            previous_state=other,
            new_particle_state=entangled_particle_2,
            new_wave_state=entangled_wave_2
        )
        
        return entangled_1, entangled_2

# No need for the ConcreteDualInformation alias, DualInformation serves as the concrete type.

def _apply_effects_to_particle_state(
    particle_state: ParticleState, effects: list
) -> ParticleState:
    """
    Apply a list of BaseEffect objects to a particle state, returning the new state.
    Uses the EffectHandlerRegistry for modular, extensible effect handling.

    Args:
        particle_state: The original particle state.
        effects: List of BaseEffect objects to apply.

    Returns:
        The new ConcreteParticleState after applying all effects.
    """
    new_state = particle_state
    for effect in effects:
        handler = EffectHandlerRegistry.get_handler(effect.effect_type)
        if handler is not None:
            try:
                new_state = handler(new_state, effect)
            except Exception as e:
                logging.warning(f"Effect handler for {effect.effect_type} failed: {e}")
        else:
            logging.warning(f"No handler registered for effect type: {effect.effect_type}")
    return new_state

# --- Effect Handler Registry for Particle State Effects --- #
class EffectHandlerRegistry:
    """
    Registry for effect handlers used in DualInformation effectful state transitions.
    Handlers are registered by effect_type (str) and must be callables of the form:
        handler(particle_state: ConcreteParticleState, effect: BaseEffect) -> ConcreteParticleState
    """
    _handlers: Dict[str, Callable[[Any, Any], Any]] = {}

    @classmethod
    def register_handler(cls, effect_type: str, handler: Callable[[Any, Any], Any]) -> None:
        cls._handlers[effect_type] = handler

    @classmethod
    def get_handler(cls, effect_type: str) -> Callable[[Any, Any], Any]:
        return cls._handlers.get(effect_type)

    @classmethod
    def has_handler(cls, effect_type: str) -> bool:
        return effect_type in cls._handlers

# --- Default Effect Handlers --- #
def update_node_metadata_handler(particle_state, effect):
    node_id = effect.parameters.get("node_id")
    metadata_update = effect.parameters.get("metadata_update", {})
    if node_id is not None and metadata_update:
        try:
            node = particle_state.get_node(node_id)
            if node is not None:
                updated_metadata = dict(getattr(node, "metadata", {}))
                updated_metadata.update(metadata_update)
                return particle_state.update_node_metadata(node_id, updated_metadata)
        except Exception as e:
            logging.warning(f"Failed to update node metadata for {node_id}: {e}")
    return particle_state

def remove_node_handler(particle_state, effect):
    node_id = effect.parameters.get("node_id")
    if node_id is not None:
        return particle_state.remove_node(node_id)
    return particle_state

def add_node_handler(particle_state, effect):
    node_data = effect.parameters.get("node")
    if node_data is not None:
        try:
            from person_suit.core.caw.schemas import BaseNode
            node = BaseNode(**node_data) if not isinstance(node_data, BaseNode) else node_data
            return particle_state.add_node(node)
        except Exception as e:
            logging.warning(f"Failed to add node: {e}")
    return particle_state

def update_edge_metadata_handler(particle_state, effect):
    edge_id = effect.parameters.get("edge_id")
    metadata_update = effect.parameters.get("metadata_update", {})
    if edge_id is not None and metadata_update:
        try:
            edge = particle_state.get_edge(edge_id)
            if edge is not None:
                updated_metadata = dict(getattr(edge, "metadata", {}))
                updated_metadata.update(metadata_update)
                return particle_state.update_edge_metadata(edge_id, updated_metadata)
        except Exception as e:
            logging.warning(f"Failed to update edge metadata for {edge_id}: {e}")
    return particle_state

def add_edge_handler(particle_state, effect):
    edge_data = effect.parameters.get("edge")
    if edge_data is not None:
        try:
            from person_suit.core.caw.schemas import BaseEdge
            edge = BaseEdge(**edge_data) if not isinstance(edge_data, BaseEdge) else edge_data
            return particle_state.add_edge(edge)
        except Exception as e:
            logging.warning(f"Failed to add edge: {e}")
    return particle_state

# Register default handlers
EffectHandlerRegistry.register_handler("UPDATE_NODE_METADATA", update_node_metadata_handler)
EffectHandlerRegistry.register_handler("REMOVE_NODE", remove_node_handler)
EffectHandlerRegistry.register_handler("ADD_NODE", add_node_handler)
EffectHandlerRegistry.register_handler("UPDATE_EDGE_METADATA", update_edge_metadata_handler)
EffectHandlerRegistry.register_handler("ADD_EDGE", add_edge_handler)
