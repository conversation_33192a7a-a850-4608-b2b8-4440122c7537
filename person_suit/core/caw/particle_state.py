# -*- coding: utf-8 -*-
"""
Implementation of the ParticleState component using immutable data structures.

This class represents the structured particle aspect of DualInformation using
an immutable, attributed hypergraph based on the pyrsistent library.
It supports storing CAW Ontology nodes and primitive data types.

Related Files:
- person_suit.core.caw.schemas
- docs/design/ParticleState_Implementation_Design.md
- docs/CAW_REPRESENTATION_PLAN.md

This module provides the concrete implementation for the ParticleState component
of the CAW paradigm, using immutable data structures based on the pyrsistent
library. It represents the structured, localized aspect of information as an
attributed hypergraph.
"""

from dataclasses import dataclass, field, fields  # Explicitly import fields
from typing import Dict, Iterable, Optional, Sequence, Set, Type, TypeVar, Any

# Import immutable collections from pyrsistent
from pyrsistent import PMap, PSet, pmap, pset

# Import schema definitions
from .schemas import (
    BaseEdge,
    BaseNode,
    EdgeID,
    FlexMetadata,
    NodeID,
    NodeType,
)

# import uuid # No longer needed as default IDs are handled by schemas


# Type variable for the class itself
IHG = TypeVar("IHG", bound="ImmutableHypergraph")


@dataclass(frozen=True)  # Main class is immutable
class ImmutableHypergraph:
    """
    Represents the immutable, attributed hypergraph for ParticleState.

    Represents the immutable, attributed hypergraph for the ParticleState.

    This class encapsulates the structured, localized ("particle") aspect of
    information within the CAW paradigm. It uses pyrsistent collections
    (PMap, PSet) internally to ensure immutability and enable efficient
    structural sharing (copy-on-write) for all modification operations.

    Attributes:
        nodes: An immutable map (pyrsistent.PMap) storing nodes, keyed by NodeID.
        edges: An immutable map (pyrsistent.PMap) storing hyperedges, keyed by
            EdgeID.
        node_incidence: An immutable map (pyrsistent.PMap) where keys are NodeIDs
            and values are immutable sets (pyrsistent.PSet) of EdgeIDs incident
            to that node. Used for efficient neighbor/edge lookups starting from
            a node.
        nodes_by_type: An immutable map (pyrsistent.PMap) where keys are NodeType
            enums and values are immutable sets (pyrsistent.PSet) of NodeIDs
            belonging to that type. Used for efficient type-based node lookups.
    """

    nodes: PMap[NodeID, BaseNode] = field(default_factory=pmap)
    edges: PMap[EdgeID, BaseEdge] = field(default_factory=pmap)
    node_incidence: PMap[NodeID, PSet[EdgeID]] = field(default_factory=pmap)
    nodes_by_type: PMap[NodeType, PSet[NodeID]] = field(default_factory=pmap)

    # --- Factory Methods ---
    @classmethod
    def empty(cls: Type[IHG]) -> IHG:
        """
        Creates and returns an empty ImmutableHypergraph instance.

        Returns:
            An empty ImmutableHypergraph with default empty PMaps/PSets.
        """
        return cls()

    # --- Add Operations (Return New Instances) ---
    def add_node(self: IHG, node: BaseNode) -> IHG:
        """
        Adds a node, returning a new hypergraph instance with the change.

        Args:
            self: The current hypergraph instance.
            node: The BaseNode object (or subclass instance) to add.

        Returns:
            A new ImmutableHypergraph instance with the node added.

        Raises:
            ValueError: If a node with the same ID already exists.
            TypeError: If the input is not a BaseNode.
        """
        if not isinstance(node, BaseNode):
            raise TypeError(f"Input must be a BaseNode subclass, got {type(node)}")
        if node.node_id in self.nodes:
            raise ValueError(f"Node with ID {node.node_id} already exists.")

        new_nodes = self.nodes.set(node.node_id, node)
        # Add to type index
        current_type_set = self.nodes_by_type.get(node.node_type, pset())
        new_type_set = current_type_set.add(node.node_id)
        new_nodes_by_type = self.nodes_by_type.set(node.node_type, new_type_set)
        # Initialize empty incidence set for the new node
        new_node_incidence = self.node_incidence.set(node.node_id, pset())

        return self.copy(
            nodes=new_nodes,
            nodes_by_type=new_nodes_by_type,
            node_incidence=new_node_incidence,
        )

    def add_edge(self: IHG, edge: BaseEdge) -> IHG:
        """
        Adds a hyperedge, returning a new hypergraph instance with the change.

        Validates that all nodes listed in `edge.connected_nodes` exist in the
        current hypergraph instance before adding the edge.

        Args:
            self: The current hypergraph instance.
            edge: The BaseEdge object (or subclass instance) to add.

        Returns:
            A new ImmutableHypergraph instance including the added edge and
            updated node incidence information.

        Raises:
            ValueError: If an edge with the same ID already exists, or if any
                      connected node does not exist in the graph.
            TypeError: If the input is not a BaseEdge.
        """
        if not isinstance(edge, BaseEdge):
            raise TypeError(f"Input must be a BaseEdge subclass, got {type(edge)}")
        if edge.edge_id in self.edges:
            raise ValueError(f"Edge with ID {edge.edge_id} already exists.")

        # Validate connected nodes exist
        missing_nodes = [nid for nid in edge.connected_nodes if nid not in self.nodes]
        if missing_nodes:
            raise ValueError(
                f"Cannot add edge {edge.edge_id}: Connected nodes not found: {missing_nodes}"
            )

        new_edges = self.edges.set(edge.edge_id, edge)

        # Update incidence map for all connected nodes
        new_node_incidence = self.node_incidence
        for node_id in edge.connected_nodes:
            current_incidence = new_node_incidence.get(node_id, pset())
            updated_incidence = current_incidence.add(edge.edge_id)
            new_node_incidence = new_node_incidence.set(node_id, updated_incidence)

        return self.copy(edges=new_edges, node_incidence=new_node_incidence)

    # --- Update Operations (Return New Instances) ---
    def update_node_metadata(
        self: IHG, node_id: NodeID, new_metadata: FlexMetadata
    ) -> IHG:
        """
        Updates a node's metadata, returning a new hypergraph instance.

        Replaces the entire metadata dictionary for the specified node. The new
        metadata is converted to an immutable PMap.

        Args:
            self: The current hypergraph instance.
            node_id: The ID of the node whose metadata should be updated.
            new_metadata: The complete dictionary of new metadata for the node.

        Returns:
            A new ImmutableHypergraph instance containing the node with updated
            metadata.

        Raises:
            KeyError: If the node_id does not exist.
        """
        original_node = self.nodes.get(node_id)
        if original_node is None:
            raise KeyError(f"Node with ID {node_id} not found for metadata update.")

        # Build keyword arguments for the node's constructor, excluding metadata
        node_kwargs = {
            f.name: getattr(original_node, f.name)
            for f in fields(original_node)
            if f.name != "metadata"
        }
        # Add the new metadata
        node_kwargs["metadata"] = pmap(new_metadata)  # Ensure metadata is immutable

        # Recreate the node using its specific type with updated args
        updated_node = type(original_node)(**node_kwargs)

        new_nodes = self.nodes.set(node_id, updated_node)
        return self.copy(nodes=new_nodes)

    def update_edge_metadata(
        self: IHG, edge_id: EdgeID, new_metadata: FlexMetadata
    ) -> IHG:
        """
        Updates an edge's metadata, returning a new hypergraph instance.

        Replaces the entire metadata dictionary for the specified edge. The new
        metadata is converted to an immutable PMap.

        Args:
            self: The current hypergraph instance.
            edge_id: The ID of the edge whose metadata should be updated.
            new_metadata: The complete dictionary of new metadata for the edge.

        Returns:
            A new ImmutableHypergraph instance containing the edge with updated
            metadata.

        Raises:
            KeyError: If the edge_id does not exist.
        """
        original_edge = self.edges.get(edge_id)
        if original_edge is None:
            raise KeyError(f"Edge with ID {edge_id} not found for metadata update.")

        # Build keyword arguments for the edge's constructor, excluding metadata
        edge_kwargs = {
            f.name: getattr(original_edge, f.name)
            for f in fields(original_edge)
            if f.name != "metadata"
        }
        # Add the new metadata
        edge_kwargs["metadata"] = pmap(new_metadata)

        # Recreate the edge using its specific type with updated args
        updated_edge = type(original_edge)(**edge_kwargs)

        new_edges = self.edges.set(edge_id, updated_edge)
        return self.copy(edges=new_edges)

    # --- Remove Operations (Return New Instances) ---
    def remove_node(self: IHG, node_id: NodeID) -> IHG:
        """
        Removes a node and its incident edges, returning a new instance.

        If the node does not exist, the original instance is returned unchanged.
        Otherwise, the node is removed from the `nodes` map and relevant indexes
        (`node_incidence`, `nodes_by_type`). All edges incident to the removed
        node are also discarded from the `edges` map, and the `node_incidence`
        maps for the *other* nodes participating in those removed edges are updated.

        Args:
            self: The current hypergraph instance.
            node_id: The ID of the node to remove.

        Returns:
            A new ImmutableHypergraph instance without the specified node and
            any edges that were incident to it. Returns `self` if the node_id
            was not found.
        """
        node_to_remove = self.nodes.get(node_id)
        if node_to_remove is None:
            return self  # Node doesn't exist, return self

        incident_edges_ids = self.node_incidence.get(node_id, pset())

        # 1. Remove the node itself and from indexes
        new_nodes = self.nodes.discard(node_id)
        new_node_incidence = self.node_incidence.discard(node_id)
        type_set = self.nodes_by_type.get(node_to_remove.node_type, pset())
        new_type_set = type_set.discard(node_id)
        if not new_type_set:
            new_nodes_by_type = self.nodes_by_type.discard(node_to_remove.node_type)
        else:
            new_nodes_by_type = self.nodes_by_type.set(
                node_to_remove.node_type, new_type_set
            )

        # 2. Remove incident edges
        new_edges = self.edges
        edges_to_update_incidence = {}  # Store nodes affected by edge removal
        for edge_id in incident_edges_ids:
            edge = new_edges.get(edge_id)
            if (
                edge
            ):  # Check if edge still exists (might have been removed via another node)
                new_edges = new_edges.discard(edge_id)
                # Track nodes whose incidence needs updating
                for participant_node_id in edge.connected_nodes:
                    if participant_node_id != node_id:
                        # Use setdefault for cleaner initialization
                        edges_to_update_incidence.setdefault(
                            participant_node_id, pset()
                        ).add(edge_id)

        # 3. Update incidence of other nodes connected to the removed edges
        for participant_node_id, removed_edge_ids in edges_to_update_incidence.items():
            if participant_node_id in new_node_incidence:
                current_incidence = new_node_incidence[participant_node_id]
                # Use difference for efficient removal of multiple edges
                updated_incidence = current_incidence.difference(removed_edge_ids)
                new_node_incidence = new_node_incidence.set(
                    participant_node_id, updated_incidence
                )

        return self.copy(
            nodes=new_nodes,
            edges=new_edges,
            node_incidence=new_node_incidence,
            nodes_by_type=new_nodes_by_type,
        )

    def remove_edge(self: IHG, edge_id: EdgeID) -> IHG:
        """
        Removes an edge, returning a new hypergraph instance.

        If the edge does not exist, the original instance is returned unchanged.
        Otherwise, the edge is removed from the `edges` map, and the
        `node_incidence` maps for all nodes connected by that edge are updated
        to remove the reference to the removed edge.

        Args:
            self: The current hypergraph instance.
            edge_id: The ID of the edge to remove.

        Returns:
            A new ImmutableHypergraph instance without the specified edge.
            Returns `self` if the edge_id was not found.
        """
        edge_to_remove = self.edges.get(edge_id)
        if edge_to_remove is None:
            return self  # Edge doesn't exist, return self

        new_edges = self.edges.discard(edge_id)

        # Update incidence map for connected nodes
        new_node_incidence = self.node_incidence
        for node_id in edge_to_remove.connected_nodes:
            if node_id in new_node_incidence:
                current_incidence = new_node_incidence[node_id]
                updated_incidence = current_incidence.discard(edge_id)
                # If incidence becomes empty, consider removing node_id key?
                # For now, keep it simple and leave empty sets.
                new_node_incidence = new_node_incidence.set(node_id, updated_incidence)

        return self.copy(edges=new_edges, node_incidence=new_node_incidence)

    # --- Query Operations (Read-only) ---
    def get_node(self, node_id: NodeID) -> Optional[BaseNode]:
        """
        Retrieves a node by its ID.

        Args:
            self: The current hypergraph instance.
            node_id: The ID of the node to retrieve.

        Returns:
            The BaseNode object if found, otherwise None.
        """
        return self.nodes.get(node_id)

    def get_edge(self, edge_id: EdgeID) -> Optional[BaseEdge]:
        """
        Retrieves an edge by its ID.

        Args:
            self: The current hypergraph instance.
            edge_id: The ID of the edge to retrieve.

        Returns:
            The BaseEdge object if found, otherwise None.
        """
        return self.edges.get(edge_id)

    def get_nodes(self, node_ids: Iterable[NodeID]) -> Dict[NodeID, BaseNode]:
        """
        Retrieves multiple nodes by their IDs.

        Args:
            self: The current hypergraph instance.
            node_ids: An iterable of NodeIDs to retrieve.

        Returns:
            A dictionary mapping found NodeIDs to their corresponding BaseNode
            objects. IDs not found in the graph are omitted.
        """
        # Use dict comprehension for potentially better performance than get
        return {nid: self.nodes[nid] for nid in node_ids if nid in self.nodes}

    def get_edges(self, edge_ids: Iterable[EdgeID]) -> Dict[EdgeID, BaseEdge]:
        """
        Retrieves multiple edges by their IDs.

        Args:
            self: The current hypergraph instance.
            edge_ids: An iterable of EdgeIDs to retrieve.

        Returns:
            A dictionary mapping found EdgeIDs to their corresponding BaseEdge
            objects. IDs not found in the graph are omitted.
        """
        return {eid: self.edges[eid] for eid in edge_ids if eid in self.edges}

    def get_nodes_by_type(self, node_type: NodeType) -> Set[NodeID]:
        """
        Retrieves the set of node IDs for a given node type.

        Args:
            self: The current hypergraph instance.
            node_type: The NodeType enum value to filter by.

        Returns:
            A standard Python set containing the NodeIDs of all nodes matching
            the specified type. Returns an empty set if the type is not found.
        """
        # Return a standard Python set for external use
        return set(self.nodes_by_type.get(node_type, pset()))

    def get_incident_edges(self, node_id: NodeID) -> Set[EdgeID]:
        """
        Retrieves the set of edge IDs incident to a given node.

        Args:
            self: The current hypergraph instance.
            node_id: The ID of the node whose incident edges are required.

        Returns:
            A standard Python set containing the EdgeIDs of all edges connected
            to the specified node. Returns an empty set if the node is not found
            or has no incident edges.
        """
        # Return a standard Python set
        return set(self.node_incidence.get(node_id, pset()))

    def get_edge_participants(self, edge_id: EdgeID) -> Optional[Sequence[NodeID]]:
        """
        Retrieves the sequence of node IDs participating in a given edge.

        Args:
            self: The current hypergraph instance.
            edge_id: The ID of the edge whose participants are required.

        Returns:
            An immutable sequence (e.g., pyrsistent.PVector) of NodeIDs
            connected by the edge if the edge exists, otherwise None. The order
            matches the `connected_nodes` attribute of the edge.
        """
        edge = self.get_edge(edge_id)
        # Return a standard Python tuple/list if needed, but Sequence allows PVector
        return edge.connected_nodes if edge else None

    def get_neighbors(self, node_id: NodeID) -> Set[NodeID]:
        """
        Retrieves the set of neighboring node IDs (nodes sharing any edge).

        Args:
            self: The current hypergraph instance.
            node_id: The ID of the node whose neighbors are required.

        Returns:
            A standard Python set containing the NodeIDs of all nodes that share
            at least one edge with the specified node (excluding the node itself).
        """
        neighbors: Set[NodeID] = set()
        incident_edges_ids = self.get_incident_edges(node_id)
        for edge_id in incident_edges_ids:
            participants = self.get_edge_participants(edge_id)
            if participants:
                for participant_id in participants:
                    if participant_id != node_id:
                        neighbors.add(participant_id)
        return neighbors

    # --- Helper for Copying --- #
    def copy(self: IHG, **kwargs) -> IHG:
        """
        Internal helper to create a modified copy of the instance.
        Uses the current instance's fields as defaults and overrides
        with provided kwargs to create a new instance. This is the core mechanism
        for achieving copy-on-write semantics with the immutable dataclass.

        Args:
            self: The current hypergraph instance.
            **kwargs: Keyword arguments where keys are field names of the
                      dataclass (`nodes`, `edges`, `node_incidence`,
                      `nodes_by_type`) and values are the new immutable
                      collections (PMap/PSet) for those fields in the copied
                      instance.

        Returns:
            A new ImmutableHypergraph instance with specified fields updated.
        """
        current_fields = {f.name: getattr(self, f.name) for f in fields(self)}
        current_fields.update(kwargs)
        return self.__class__(**current_fields)

    def analyze_topology(self, method: str = "persistent_homology", **kwargs) -> Any:
        """
        Analyze the topological structure of the particle state using TDA methods.

        Args:
            method: The TDA method to use (currently only 'persistent_homology' is supported).
            **kwargs: Additional parameters for the analysis.

        Returns:
            Analysis result (structure depends on method, e.g., persistence diagrams).
        Raises:
            ImportError: If giotto-tda is not installed.
            ValueError: If the method is not supported.
        """
        try:
            from gtda.homology import VietorisRipsPersistence
            import numpy as np
        except ImportError:
            raise ImportError("giotto-tda is required for TDA analysis. Please install with 'pip install giotto-tda'.")

        if method != "persistent_homology":
            raise ValueError(f"Unsupported TDA method: {method}")

        # Convert hypergraph to adjacency matrix (simple undirected version)
        node_ids = list(self.nodes.keys())
        node_index = {nid: i for i, nid in enumerate(node_ids)}
        n = len(node_ids)
        adj = np.zeros((n, n))
        for edge in self.edges.values():
            for i in range(len(edge.connected_nodes)):
                for j in range(i + 1, len(edge.connected_nodes)):
                    idx_i = node_index[edge.connected_nodes[i]]
                    idx_j = node_index[edge.connected_nodes[j]]
                    adj[idx_i, idx_j] = 1
                    adj[idx_j, idx_i] = 1

        # Vietoris-Rips persistence on the adjacency matrix as a distance matrix
        vr = VietorisRipsPersistence(metric="precomputed")
        # 1 - adj: treat as distance (0 for connected, 1 for not connected)
        diagrams = vr.fit_transform([1 - adj])
        return diagrams[0]  # Return the diagram for this hypergraph


# No need for the ConcreteParticleState alias, ImmutableHypergraph serves as the concrete type.
