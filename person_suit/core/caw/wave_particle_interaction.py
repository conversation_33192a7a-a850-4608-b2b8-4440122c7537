# -*- coding: utf-8 -*-
"""
File: person_suit/core/caw/wave_particle_interaction.py
Purpose: Functions implementing the influence of WaveState on ParticleState.

NOTE: This file was moved from core/caw/dynamics/ during flattening.
      Imports may need adjustment in files that used the old path.

This module contains the logic where wave-like properties (potentials,
probabilities, fields derived from WaveState) induce changes or biases
in the particle-like hypergraph structure (ParticleState).

Related Files:
- docs/CAW_REPRESENTATION_PLAN.md (Sec 3.2)
- person_suit.core.caw.schemas.py
- person_suit.core.caw.wave_state.py
- person_suit.core.caw.particle_state.py
- person_suit.core.effects.transformation_logic.py (Calls functions here)
"""

import logging
import random
from typing import Any, Dict, List
from uuid import uuid4  # To generate new NodeIDs

import torch  # Import torch

# Concrete Types for operation
from .particle_state import ConcreteParticleState

# Core CAW Types
# Adjust imports based on new location
from .schemas import (
    BaseEffect,  # Added EffectCategory; Interface types
    ConceptNode,
    Context,
    <PERSON>tomNode,
    EffectCategory,
    InfonNode,
    NodeID,
    RelationalEdge,  # For connecting new nodes
)
from .wave_state import ConcreteWaveState

# Effect Type constants for generated effects
# TODO: Define these centrally, perhaps in schemas or a dedicated effects_types.py
# For now, keep local string definitions consistent with usage
UPDATE_NODE_METADATA_EFFECT = "UPDATE_NODE_METADATA"
ADD_NODE_EFFECT = "ADD_NODE"
UPDATE_EDGE_METADATA_EFFECT = "UPDATE_EDGE_METADATA"
REMOVE_NODE_EFFECT = "REMOVE_NODE"
ADD_EDGE_EFFECT = "ADD_EDGE"

# Feature indices (must match wave_function.py)
FEATURE_IS_CONCEPT = 0
FEATURE_IS_DATOM = 1
FEATURE_IS_INFON = 2
FEATURE_MASS = 3
FEATURE_CHARGE = 4
FEATURE_NEIGHBOR_AVG_MASS = 5
FEATURE_DEGREE_CENTRALITY = 6
FEATURE_WEIGHTED_DEGREE = 7
FEATURE_NEIGHBOR_AVG_CHARGE = 8
FEATURE_LOCAL_CLUSTERING_COEFF = 9
NUM_FEATURES = 10

# Define subset of features for similarity calculation
SIMILARITY_FEATURE_INDICES = [
    FEATURE_MASS,
    FEATURE_CHARGE,
    FEATURE_NEIGHBOR_AVG_MASS,
    FEATURE_DEGREE_CENTRALITY,
    FEATURE_WEIGHTED_DEGREE,
]

# Initialize cosine similarity
# Use torch.nn.functional for efficiency if available and makes sense
_cosine_similarity = torch.nn.CosineSimilarity(dim=0, eps=1e-6)

logger = logging.getLogger(__name__)


async def apply_wave_potential_to_particle(
    wave_state: ConcreteWaveState,
    particle_state: ConcreteParticleState,
    context: Context,
    acf_params: Dict[str, Any],
    node_to_index: Dict[NodeID, int],
) -> List[BaseEffect]:
    """
    Calculates effects on ParticleState based on WaveState tensor features.

    Current Logic:
    - Uses avg neighbor mass & charge from wave tensor to influence node mass/charge.
    - Adjusts edge weights based on feature similarity (incl. weighted degree).
    - Proposes deletion of nodes with low centrality, low neighbor mass, AND low clustering coeff.
    - Proposes creation of new ConceptNodes near existing nodes with high neighbor mass but low centrality/clustering.

    Relies on a stable node_to_index mapping being provided or accessible that matches
    the mapping used during the WaveState tensor calculation.

    Args:
        wave_state: The current wave state representation (tensor required).
        particle_state: The current particle state hypergraph.
        context: The current context.
        acf_params: Interpreted ACF parameters.
        node_to_index: Mapping from NodeID to tensor row index.

    Returns:
        A list of BaseEffect objects to be applied to the particle state.
    """
    generated_effects: List[BaseEffect] = []
    logging.debug(
        f"Applying wave effects using tensor. Shape: {wave_state.tensor.shape if wave_state and wave_state.tensor is not None else 'N/A'}, ACF: {acf_params}"
    )

    if not wave_state or wave_state.tensor is None:
        logging.warning("Cannot apply wave effects: WaveState or tensor is missing.")
        return []

    tensor = wave_state.tensor
    if len(tensor.shape) != 2 or tensor.shape[1] != NUM_FEATURES:
        logging.error(
            f"Wave tensor has unexpected shape {tensor.shape}. Expected [N, {NUM_FEATURES}]. Cannot apply wave effects."
        )
        return []

    node_count = len(particle_state.nodes)
    if abs(tensor.shape[0] - len(node_to_index)) > 1:
        logging.warning(
            f"Wave tensor row count ({tensor.shape[0]}) or node_to_index map size ({len(node_to_index)}) seems inconsistent with particle state node count ({node_count}). Effects may be partial."
        )
    if not node_to_index:
        logging.error("Node-to-index map is empty. Cannot apply wave effects.")
        return []

    # --- Define Thresholds & Factors (Potentially ACF Modulated) --- #
    neighbor_mass_threshold = 0.5
    mass_increase_factor = 1.05
    charge_diff_threshold = 0.2
    charge_adjustment_factor = 0.5
    similarity_threshold_strong = 0.85
    similarity_threshold_weak = 0.3
    edge_weight_increase_factor = 1.1
    edge_weight_decrease_factor = 0.9
    deletion_threshold_centrality = 0.05
    deletion_threshold_neighbor_mass = 0.05
    deletion_threshold_clustering = 0.1
    creation_threshold_neighbor_mass = 0.75
    creation_threshold_centrality_max = 0.2
    creation_threshold_clustering_max = 0.3
    node_creation_probability = 0.1

    if acf_params.get("force_computation_level") == "none":
        return []
    if acf_params.get("force_computation_level") == "simplified":
        neighbor_mass_threshold = 0.7
        mass_increase_factor = 1.02
        charge_diff_threshold = 0.3
        charge_adjustment_factor = 0.25
        similarity_threshold_strong = 0.9
        similarity_threshold_weak = 0.4
        edge_weight_increase_factor = 1.05
        edge_weight_decrease_factor = 0.95
        deletion_threshold_centrality = 0.02
        deletion_threshold_neighbor_mass = 0.02
        deletion_threshold_clustering = 0.05
        creation_threshold_neighbor_mass = 0.85
        creation_threshold_centrality_max = 0.15
        creation_threshold_clustering_max = 0.2
        node_creation_probability = 0.05
        logging.debug("Wave->Particle using simplified thresholds/factors due to ACF.")

    # === Stage 1: Process Nodes for Node-Specific Effects ===
    processed_nodes = 0
    nodes_to_potentially_delete = set()

    for node_id, node in particle_state.nodes.items():
        if not isinstance(node, (ConceptNode, DatomNode, InfonNode)):
            continue
        processed_nodes += 1

        target_index = node_to_index.get(node_id)
        if target_index is None:
            logging.debug(
                f"Node {node_id} not found in provided node_to_index map. Skipping wave effects for this node."
            )
            continue

        # --- Read Features from Tensor using the obtained index --- #
        try:
            neighbor_avg_mass = tensor[target_index, FEATURE_NEIGHBOR_AVG_MASS].item()
            wave_charge = tensor[target_index, FEATURE_CHARGE].item()
            degree_centrality = tensor[target_index, FEATURE_DEGREE_CENTRALITY].item()
            weighted_degree = tensor[target_index, FEATURE_WEIGHTED_DEGREE].item()
            avg_neighbor_charge = tensor[
                target_index, FEATURE_NEIGHBOR_AVG_CHARGE
            ].item()
            local_clustering_coeff = tensor[
                target_index, FEATURE_LOCAL_CLUSTERING_COEFF
            ].item()

            neighbor_avg_mass = max(0.0, neighbor_avg_mass)
            degree_centrality = max(0.0, min(1.0, degree_centrality))
            weighted_degree = max(0.0, weighted_degree)
            local_clustering_coeff = max(0.0, min(1.0, local_clustering_coeff))

            logging.debug(
                f"Node {node_id}: Avg Neighbor Charge = {avg_neighbor_charge:.3f}"
            )

        except IndexError:
            logging.warning(
                f"Index {target_index} out of bounds for tensor shape {tensor.shape} (Node {node_id}). Skipping node effects."
            )
            continue
        except Exception as tensor_err:
            logging.exception(
                f"Error accessing tensor for node {node_id}: {tensor_err}. Skipping node effects."
            )
            continue
        # --- End Reading Features --- #

        # --- Effect 1: Mass increase based on neighbor mass --- #
        if isinstance(node, (ConceptNode, DatomNode)):
            if neighbor_avg_mass > neighbor_mass_threshold:
                current_mass = float(
                    getattr(node, "computational_mass", 0.1)
                )  # Use default
                new_mass = current_mass * mass_increase_factor
                # Avoid tiny changes or infinite loops
                if abs(new_mass - current_mass) > 1e-6 and new_mass < 1e6:
                    logging.info(
                        f"Wave effect (Neighbor Mass): High avg neighbor mass ({neighbor_avg_mass:.3f} > {neighbor_mass_threshold:.2f}) for Node {node_id} (Index {target_index}). Proposing mass increase {current_mass:.3f} -> {new_mass:.3f}"
                    )
                    # Need to ensure EffectType instances are available or use strings consistently
                    effect = BaseEffect(
                        effect_type=UPDATE_NODE_METADATA_EFFECT,
                        category=EffectCategory.STATE_MANIPULATION,
                        target_entity_id=str(node_id),
                        parameters={
                            "node_id": node_id,
                            "metadata_update": {"computational_mass": new_mass},
                        },
                        intent=f"Increase node mass due to high avg neighbor mass ({neighbor_avg_mass:.3f}) in wave state",
                    )
                    generated_effects.append(effect)

        # --- Effect 2: Charge adjustment for Infons --- #
        if isinstance(node, InfonNode):
            current_charge = float(getattr(node, "lepton_charge", 0.0))
            charge_diff = wave_charge - current_charge
            final_adjustment_factor = charge_adjustment_factor

            if abs(charge_diff) > charge_diff_threshold:
                new_charge = current_charge + charge_diff * final_adjustment_factor
                if abs(new_charge - current_charge) > 1e-6:
                    logging.info(
                        f"Wave effect (Charge): Wave charge ({wave_charge:.3f}) differs significantly from particle charge ({current_charge:.3f}) for Infon {node_id} (Index {target_index}). Proposing charge update -> {new_charge:.3f}"
                    )
                    effect = BaseEffect(
                        effect_type=UPDATE_NODE_METADATA_EFFECT,
                        category=EffectCategory.STATE_MANIPULATION,
                        target_entity_id=str(node_id),
                        parameters={
                            "node_id": node_id,
                            "metadata_update": {"lepton_charge": new_charge},
                        },
                        intent=f"Adjust Infon charge towards wave state value ({wave_charge:.3f})",
                    )
                    generated_effects.append(effect)

        # --- Effect 3: Node Deletion based on low centrality, low neighbor mass, and low clustering coefficient --- #
        if isinstance(node, (ConceptNode, DatomNode)):
            if (
                degree_centrality < deletion_threshold_centrality
                and neighbor_avg_mass < deletion_threshold_neighbor_mass
                and local_clustering_coeff < deletion_threshold_clustering
            ):
                logging.info(
                    f"Wave effect (Deletion): Node {node_id} has low Cent.({degree_centrality:.2f}), low N.Mass({neighbor_avg_mass:.2f}), AND low Clust.({local_clustering_coeff:.2f}). Proposing deletion."
                )
                effect = BaseEffect(
                    effect_type=REMOVE_NODE_EFFECT,
                    category=EffectCategory.STATE_MANIPULATION,
                    target_entity_id=str(node_id),
                    parameters={"node_id": node_id},
                    intent="Remove node due to low centrality, low neighbor mass, and low clustering coefficient in wave state",
                )
                generated_effects.append(effect)
                nodes_to_potentially_delete.add(node_id)

        # --- Effect 4: Node Creation based on high neighbor mass, low centrality, low clustering, and probability --- #
        if isinstance(node, (ConceptNode, DatomNode)):
            if (
                neighbor_avg_mass > creation_threshold_neighbor_mass
                and degree_centrality < creation_threshold_centrality_max
                and local_clustering_coeff < creation_threshold_clustering_max
                and random.random() < node_creation_probability
            ):
                new_node_id_str = f"concept_{uuid4()}"
                new_node_id = NodeID(new_node_id_str)
                initial_mass = max(0.01, neighbor_avg_mass * 0.5)  # Heuristic

                # Create the new ConceptNode instance to be added
                # Assumes ConceptNode doesn't require label at init
                new_concept = ConceptNode(
                    node_id=new_node_id,
                    # label=f"GeneratedConcept_{str(node_id)[:4]}_{new_node_id_str[:4]}", # Example label
                    computational_mass=initial_mass,
                    metadata={  # Add some initial metadata
                        "origin": "wave_effect_creation",
                        "parent_node": str(node_id),
                        "trigger_neighbor_mass": neighbor_avg_mass,
                        "trigger_centrality": degree_centrality,
                        "trigger_clustering": local_clustering_coeff,
                    },
                )

                logging.info(
                    f"Wave effect (Creation): Proposing creation of new ConceptNode {new_node_id} near {node_id} due to high N.Mass({neighbor_avg_mass:.2f}), low Cent.({degree_centrality:.2f}), low Clust.({local_clustering_coeff:.2f})"
                )

                # 1. Effect to add the new node
                # Convert node to dict for parameters if BaseEffect doesn't handle objects
                node_data_param = (
                    {
                        k: getattr(new_concept, k)
                        for k in [f.name for f in fields(new_concept)]
                    }
                    if hasattr(BaseEffect, "parameters")
                    and isinstance(BaseEffect.parameters, dict)
                    else new_concept
                )

                add_node_effect = BaseEffect(
                    effect_type=ADD_NODE_EFFECT,
                    category=EffectCategory.STATE_MANIPULATION,
                    target_entity_id=str(new_node_id),  # Target is the new node
                    parameters={
                        "node": node_data_param  # Pass node data as dict or object
                    },
                    intent=f"Create new concept node influenced by wave state near {node_id}",
                )
                generated_effects.append(add_node_effect)

                # 2. Effect to add an edge connecting the new node to the trigger node
                new_edge_id_str = f"edge_{str(node_id)[:4]}_{new_node_id_str[:4]}"
                new_edge_id = EdgeID(new_edge_id_str)

                # Define the edge connecting them (Assuming RelationalEdge schema)
                connecting_edge = RelationalEdge(
                    edge_id=new_edge_id,
                    connected_nodes=[node_id, new_node_id],
                    force_type="generated_from",  # Example type, use RelationalForceType Enum if available
                    strength=0.5,  # Default weight
                    metadata={"origin": "wave_effect_creation_link"},
                )
                edge_data_param = (
                    {
                        k: getattr(connecting_edge, k)
                        for k in [f.name for f in fields(connecting_edge)]
                    }
                    if hasattr(BaseEffect, "parameters")
                    and isinstance(BaseEffect.parameters, dict)
                    else connecting_edge
                )

                add_edge_effect = BaseEffect(
                    effect_type=ADD_EDGE_EFFECT,
                    category=EffectCategory.STATE_MANIPULATION,
                    target_entity_id=new_edge_id_str,  # Target is the new edge
                    parameters={
                        "edge": edge_data_param  # Pass edge data as dict or object
                    },
                    intent=f"Connect newly generated node {new_node_id} to parent {node_id}",
                )
                generated_effects.append(add_edge_effect)

    if processed_nodes == 0:
        logging.debug("No relevant nodes found for Stage 1 (node effects).")

    # === Stage 2: Process Edges for Edge-Specific Effects ===
    processed_edges = 0
    for edge_id, edge in particle_state.edges.items():
        if not hasattr(edge, "connected_nodes") or len(edge.connected_nodes) != 2:
            # Skip non-binary edges for similarity check for now
            continue
        processed_edges += 1

        node_id_a, node_id_b = tuple(edge.connected_nodes)

        if (
            node_id_a in nodes_to_potentially_delete
            or node_id_b in nodes_to_potentially_delete
        ):
            logging.debug(
                f"Skipping similarity check for edge {edge_id} as one/both nodes ({node_id_a}, {node_id_b}) are proposed for deletion."
            )
            continue

        index_a = node_to_index.get(node_id_a)
        index_b = node_to_index.get(node_id_b)

        if index_a is None or index_b is None:
            logging.warning(
                f"Could not find one or both nodes ({node_id_a}, {node_id_b}) for Edge {edge_id} in index map. Skipping similarity check."
            )
            continue

        # --- Calculate Feature Similarity (Using Updated Subset) --- #
        try:
            features_a = tensor[index_a, SIMILARITY_FEATURE_INDICES]
            features_b = tensor[index_b, SIMILARITY_FEATURE_INDICES]

            similarity = _cosine_similarity(features_a, features_b).item()

        except Exception as sim_err:
            logging.exception(
                f"Error calculating similarity for Edge {edge_id} ({node_id_a} <-> {node_id_b}): {sim_err}"
            )
            continue
        # --- End Similarity Calculation --- #

        # --- Apply Effect Based on Similarity --- #
        # Use metadata.get for safer access to weight
        current_weight = float(edge.metadata.get("weight", 1.0))
        new_weight = current_weight
        intent = ""

        if similarity > similarity_threshold_strong:
            new_weight = current_weight * edge_weight_increase_factor
            intent = f"Strengthen edge weight due to high feature similarity ({similarity:.3f}) in wave state"
        elif similarity < similarity_threshold_weak:
            new_weight = current_weight * edge_weight_decrease_factor
            intent = f"Weaken edge weight due to low feature similarity ({similarity:.3f}) in wave state"

        if abs(new_weight - current_weight) > 1e-6 and 1e-6 < new_weight < 1e6:
            logging.info(
                f"Wave effect (Similarity): Edge {edge_id} ({node_id_a} <-> {node_id_b}) similarity {similarity:.3f}. Proposing weight change {current_weight:.3f} -> {new_weight:.3f}"
            )
            effect = BaseEffect(
                effect_type=UPDATE_EDGE_METADATA_EFFECT,
                category=EffectCategory.STATE_MANIPULATION,
                target_entity_id=str(edge_id),
                parameters={
                    "edge_id": edge_id,
                    "metadata_update": {"weight": new_weight},
                },
                intent=intent,
            )
            generated_effects.append(effect)
        # --- End Apply Effect --- #

    if processed_edges == 0:
        logging.debug("No relevant edges found for Stage 2 (similarity effects).")

    logging.debug(
        f"Generated {len(generated_effects)} total effects from wave state tensor features (node & edge stages)."
    )
    return generated_effects


def combine_particle_states(
    particle_a: ConcreteParticleState,
    particle_b: ConcreteParticleState,
    context: Context
) -> ConcreteParticleState:
    """
    Combine two particle states into a new, merged particle state.
    Merges nodes and edges, aggregates attributes, and annotates provenance.
    """
    # Simple merge: union of nodes/edges, averaging attributes if duplicate
    merged_nodes = {**particle_a.nodes}
    for node_id, node in particle_b.nodes.items():
        if node_id in merged_nodes:
            # Average attributes (example: mass)
            node_a = merged_nodes[node_id]
            avg_mass = (getattr(node_a, 'computational_mass', 1.0) + getattr(node, 'computational_mass', 1.0)) / 2
            node_a.computational_mass = avg_mass
            merged_nodes[node_id] = node_a
        else:
            merged_nodes[node_id] = node
    merged_edges = {**particle_a.edges, **particle_b.edges}
    # TODO: Add provenance/metadata if needed
    return ConcreteParticleState(nodes=merged_nodes, edges=merged_edges)


def generate_interference_effects(
    wave_state: ConcreteWaveState,
    particle_state: ConcreteParticleState,
    context: Context
) -> list:
    """
    Generate a list of BaseEffect objects representing the consequences of wave-particle interference.

    This function analyzes the overlap between the wave_state (potential field) and the particle_state (hypergraph).
    For nodes in the particle_state that correspond to high-amplitude regions in the wave_state, it generates effects such as:
      - Mass/charge adjustment
      - Edge weight changes
      - Node/edge creation or deletion (if interference is constructive/destructive)
    The context is used to modulate thresholds and effect strengths (e.g., via ACF or focus parameters).

    Args:
        wave_state: The current wave state (must have a vector/tensor attribute).
        particle_state: The current particle state (must have nodes with vector attributes).
        context: The current context (may include thresholds, ACF, etc.).

    Returns:
        List of BaseEffect objects representing proposed state changes.
    """
    import numpy as np
    import torch
    effects = []
    if wave_state is None or not hasattr(wave_state, 'vector') or wave_state.vector is None:
        return effects
    if not hasattr(particle_state, 'nodes') or not particle_state.nodes:
        return effects

    # Parameters (can be modulated by context)
    amplitude_threshold = getattr(context, 'interference_amplitude_threshold', 0.7)
    mass_adjustment_factor = getattr(context, 'interference_mass_factor', 1.15)
    charge_adjustment_factor = getattr(context, 'interference_charge_factor', 1.1)
    destructive_threshold = getattr(context, 'interference_destructive_threshold', 0.1)
    constructive_threshold = getattr(context, 'interference_constructive_threshold', 0.9)

    # Normalize wave vector
    wave_vec = np.array(wave_state.vector)
    if np.linalg.norm(wave_vec) > 0:
        wave_vec = wave_vec / np.linalg.norm(wave_vec)

    # For each node, compute overlap with wave vector (cosine similarity)
    for node_id, node in particle_state.nodes.items():
        node_vec = getattr(node, 'vector', None)
        if node_vec is None:
            continue
        node_vec = np.array(node_vec)
        if np.linalg.norm(node_vec) == 0:
            continue
        node_vec = node_vec / np.linalg.norm(node_vec)
        similarity = float(np.dot(wave_vec, node_vec))

        # Constructive interference: high similarity
        if similarity > constructive_threshold:
            # Propose mass and charge increase
            new_mass = getattr(node, 'computational_mass', 1.0) * mass_adjustment_factor
            new_charge = getattr(node, 'lepton_charge', 1.0) * charge_adjustment_factor
            effects.append(BaseEffect(
                effect_type=UPDATE_NODE_METADATA_EFFECT,
                category=EffectCategory.STATE_MANIPULATION,
                target_entity_id=str(node_id),
                parameters={
                    "node_id": node_id,
                    "metadata_update": {"computational_mass": new_mass, "lepton_charge": new_charge},
                },
                intent=f"Increase mass/charge due to constructive interference (similarity={similarity:.2f})",
            ))
            logger.info(f"Constructive interference for node {node_id}: similarity={similarity:.2f}, mass→{new_mass:.2f}, charge→{new_charge:.2f}")

        # Destructive interference: low similarity
        elif similarity < destructive_threshold:
            # Propose node deletion
            effects.append(BaseEffect(
                effect_type=REMOVE_NODE_EFFECT,
                category=EffectCategory.STATE_MANIPULATION,
                target_entity_id=str(node_id),
                parameters={"node_id": node_id},
                intent=f"Remove node due to destructive interference (similarity={similarity:.2f})",
            ))
            logger.info(f"Destructive interference for node {node_id}: similarity={similarity:.2f}, proposing removal.")

        # Moderate overlap: propose edge weight adjustment if node is connected
        else:
            # For each edge connected to this node, adjust weight based on similarity
            for edge_id, edge in particle_state.edges.items():
                if hasattr(edge, 'connected_nodes') and node_id in edge.connected_nodes:
                    current_weight = float(getattr(edge, 'weight', 1.0))
                    # Increase weight for positive similarity, decrease for negative
                    if similarity > 0:
                        new_weight = current_weight * (1 + 0.1 * similarity)
                    else:
                        new_weight = current_weight * (1 + 0.1 * similarity)  # will decrease
                    effects.append(BaseEffect(
                        effect_type=UPDATE_EDGE_METADATA_EFFECT,
                        category=EffectCategory.STATE_MANIPULATION,
                        target_entity_id=str(edge_id),
                        parameters={
                            "edge_id": edge_id,
                            "metadata_update": {"weight": new_weight},
                        },
                        intent=f"Adjust edge weight due to moderate interference (similarity={similarity:.2f})",
                    ))
                    logger.info(f"Edge {edge_id} connected to node {node_id}: similarity={similarity:.2f}, weight→{new_weight:.2f}")
    return effects


def generate_wave_from_particle(
    particle_state: ConcreteParticleState,
    context: Context
) -> ConcreteWaveState:
    """
    Produce a new ConcreteWaveState from a given ConcreteParticleState and context.
    """
    # Stub: Create a random or averaged vector from particle node features
    import numpy as np
    node_count = len(particle_state.nodes)
    dim = getattr(context, 'embedding_dim', 2048)
    vector = np.random.normal(0, 1, dim) if node_count == 0 else np.mean([
        getattr(node, 'vector', np.random.normal(0, 1, dim)) for node in particle_state.nodes.values()
    ], axis=0)
    return ConcreteWaveState(vector=vector)


def enhance_wave_state(
    wave_state: ConcreteWaveState,
    context: Context,
    acf_factors: dict,
    particle_state: ConcreteParticleState = None
) -> ConcreteWaveState:
    """
    Contextually enhance or modulate a wave state, possibly using information from the particle state.
    """
    # Stub: Increase amplitude or coherence based on context
    if wave_state is None:
        return None
    new_amplitude = getattr(wave_state, 'amplitude', 1.0) * 1.1
    return ConcreteWaveState(
        vector=wave_state.vector,
        amplitude=new_amplitude,
        phase=getattr(wave_state, 'phase', 0.0),
        frequency=getattr(wave_state, 'frequency', 1.0),
        wavelength=getattr(wave_state, 'wavelength', 1.0),
        coherence=min(1.0, getattr(wave_state, 'coherence', 1.0) * 1.05),
        polarization=getattr(wave_state, 'polarization', None),
        uncertainty=getattr(wave_state, 'uncertainty', 0.1),
    )


def generate_contextual_particle_effects(
    particle_state: ConcreteParticleState,
    context: Context,
    acf_factors: dict,
    wave_state: ConcreteWaveState = None
) -> list:
    """
    Generate a list of effects to apply to a particle state, based on context and optionally the current wave state.
    """
    # Stub: Return an empty list or a simple effect
    return []


def adapt_wave_state(
    wave_state: ConcreteWaveState,
    context: Context,
    acf_factors: dict,
    particle_state: ConcreteParticleState = None
) -> ConcreteWaveState:
    """
    Adapt the wave state to context and particle state, supporting ACF and context-driven computation.
    """
    # Stub: Reduce or increase vector dimensionality based on ACF
    if wave_state is None:
        return None
    # Example: If ACF says to reduce precision, zero out part of the vector
    if acf_factors.get('low_precision_wave', False) and hasattr(wave_state, 'vector'):
        import numpy as np
        v = wave_state.vector.copy()
        v[int(len(v)/2):] = 0
        return ConcreteWaveState(vector=v)
    return wave_state


def calculate_state_coherence(
    wave_state: ConcreteWaveState,
    particle_state: ConcreteParticleState,
    context: Context
) -> float:
    """
    Compute a scalar coherence score (0.0–1.0) representing the alignment between wave and particle states.
    """
    # Stub: Use cosine similarity between wave vector and mean of particle node vectors
    import numpy as np
    if wave_state is None or not hasattr(wave_state, 'vector') or wave_state.vector is None:
        return 0.0
    node_vectors = [getattr(node, 'vector', None) for node in particle_state.nodes.values() if hasattr(node, 'vector') and node.vector is not None]
    if not node_vectors:
        return 0.0
    mean_particle_vector = np.mean(node_vectors, axis=0)
    dot = np.dot(wave_state.vector, mean_particle_vector)
    norm = np.linalg.norm(wave_state.vector) * np.linalg.norm(mean_particle_vector)
    return float(dot / norm) if norm > 0 else 0.0


def create_entangled_wave_states(
    wave_a: ConcreteWaveState,
    wave_b: ConcreteWaveState,
    context: Context
) -> tuple:
    """
    Produce a pair of entangled wave states from two input wave states and context.
    """
    # Stub: Mix vectors and phases
    import numpy as np
    if wave_a is None or wave_b is None:
        return wave_a, wave_b
    mixed_vector = (wave_a.vector + wave_b.vector) / 2
    entangled_a = ConcreteWaveState(vector=mixed_vector, phase=getattr(wave_a, 'phase', 0.0))
    entangled_b = ConcreteWaveState(vector=mixed_vector, phase=getattr(wave_b, 'phase', 0.0))
    return entangled_a, entangled_b
