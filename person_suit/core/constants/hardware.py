"""
Hardware-Specific Constants

This module defines hardware-related constants for the Person Suit framework.
These constants provide information about the hardware environment and
configuration for hardware-specific optimizations.

Constants defined here include:
- CPU information
- Memory limits
- GPU availability and configuration
- Neural Engine availability and configuration
- Hardware-specific optimization flags

Usage:
```python
from .hardware import CPU_COUNT
```
"""

import multiprocessing
import os
import platform
from typing import Any, Dict, Final, Optional

import psutil

# CPU information
CPU_COUNT: Final[int] = multiprocessing.cpu_count()
CPU_ARCHITECTURE: Final[str] = platform.machine()
CPU_MODEL: Final[str] = platform.processor()
CPU_FREQUENCY: Final[Optional[float]] = (
    psutil.cpu_freq().current if hasattr(psutil.cpu_freq(), "current") else None
)

# Memory information
TOTAL_MEMORY: Final[int] = psutil.virtual_memory().total
MEMORY_LIMIT: Final[int] = int(TOTAL_MEMORY * 0.8)  # 80% of total memory
PAGE_SIZE: Final[int] = os.sysconf("SC_PAGE_SIZE") if hasattr(os, "sysconf") else 4096

# Disk information
DISK_SPACE_TOTAL: Final[int] = psutil.disk_usage("/").total
DISK_SPACE_LIMIT: Final[int] = int(DISK_SPACE_TOTAL * 0.9)  # 90% of total disk space

# GPU information
GPU_AVAILABLE: Final[bool] = False  # This should be determined dynamically
GPU_COUNT: Final[int] = 0  # This should be determined dynamically
GPU_MEMORY: Final[int] = 0  # This should be determined dynamically
GPU_MODEL: Final[str] = ""  # This should be determined dynamically

# Apple-specific hardware
IS_APPLE_SILICON: Final[bool] = (
    platform.system() == "Darwin" and platform.machine() == "arm64"
)
NEURAL_ENGINE_AVAILABLE: Final[bool] = IS_APPLE_SILICON
METAL_AVAILABLE: Final[bool] = platform.system() == "Darwin"

# Apple Silicon details
APPLE_SILICON_MODEL: Final[Optional[str]] = (
    None  # This should be determined dynamically
)
APPLE_SILICON_CORES: Final[Dict[str, int]] = {
    "performance": 0,
    "efficiency": 0,
    "total": CPU_COUNT,
}

# Hardware optimization flags
VECTORIZATION_SUPPORTED: Final[bool] = True  # This should be determined dynamically
AVX_SUPPORTED: Final[bool] = False  # This should be determined dynamically
AVX2_SUPPORTED: Final[bool] = False  # This should be determined dynamically
AVX512_SUPPORTED: Final[bool] = False  # This should be determined dynamically
NEON_SUPPORTED: Final[bool] = IS_APPLE_SILICON  # ARM NEON instructions

# Hardware-specific optimization configuration
HARDWARE_OPTIMIZATIONS: Final[Dict[str, bool]] = {
    "vectorization": VECTORIZATION_SUPPORTED,
    "avx": AVX_SUPPORTED,
    "avx2": AVX2_SUPPORTED,
    "avx512": AVX512_SUPPORTED,
    "neon": NEON_SUPPORTED,
    "metal": METAL_AVAILABLE,
    "neural_engine": NEURAL_ENGINE_AVAILABLE,
    "gpu": GPU_AVAILABLE,
}

# Energy efficiency
POWER_EFFICIENT_MODE: Final[bool] = False
ENERGY_HARVESTING_AVAILABLE: Final[bool] = False
BATTERY_POWERED: Final[bool] = (
    psutil.sensors_battery() is not None
    if hasattr(psutil, "sensors_battery")
    else False
)


# Hardware capability levels
class HardwareCapabilityLevel(int):
    """Hardware capability levels for the Person Suit framework."""

    MINIMAL = 1  # Basic functionality only
    STANDARD = 2  # Standard functionality
    ENHANCED = 3  # Enhanced functionality with some optimizations
    HIGH = 4  # High-performance with most optimizations
    MAXIMUM = 5  # Maximum performance with all optimizations


# Determine hardware capability level
def _determine_hardware_capability_level() -> HardwareCapabilityLevel:
    """Determine the hardware capability level based on the hardware environment."""
    if CPU_COUNT >= 16 and TOTAL_MEMORY >= 32 * 1024 * 1024 * 1024 and GPU_AVAILABLE:
        return HardwareCapabilityLevel.MAXIMUM
    elif CPU_COUNT >= 8 and TOTAL_MEMORY >= 16 * 1024 * 1024 * 1024:
        return HardwareCapabilityLevel.HIGH
    elif CPU_COUNT >= 4 and TOTAL_MEMORY >= 8 * 1024 * 1024 * 1024:
        return HardwareCapabilityLevel.ENHANCED
    elif CPU_COUNT >= 2 and TOTAL_MEMORY >= 4 * 1024 * 1024 * 1024:
        return HardwareCapabilityLevel.STANDARD
    else:
        return HardwareCapabilityLevel.MINIMAL


HARDWARE_CAPABILITY_LEVEL: Final[HardwareCapabilityLevel] = (
    _determine_hardware_capability_level()
)

# Hardware-specific configuration
HARDWARE_CONFIG: Final[Dict[str, Any]] = {
    "cpu_count": CPU_COUNT,
    "cpu_architecture": CPU_ARCHITECTURE,
    "cpu_model": CPU_MODEL,
    "cpu_frequency": CPU_FREQUENCY,
    "total_memory": TOTAL_MEMORY,
    "memory_limit": MEMORY_LIMIT,
    "disk_space_total": DISK_SPACE_TOTAL,
    "disk_space_limit": DISK_SPACE_LIMIT,
    "gpu_available": GPU_AVAILABLE,
    "gpu_count": GPU_COUNT,
    "gpu_memory": GPU_MEMORY,
    "gpu_model": GPU_MODEL,
    "is_apple_silicon": IS_APPLE_SILICON,
    "neural_engine_available": NEURAL_ENGINE_AVAILABLE,
    "metal_available": METAL_AVAILABLE,
    "apple_silicon_model": APPLE_SILICON_MODEL,
    "apple_silicon_cores": APPLE_SILICON_CORES,
    "vectorization_supported": VECTORIZATION_SUPPORTED,
    "avx_supported": AVX_SUPPORTED,
    "avx2_supported": AVX2_SUPPORTED,
    "avx512_supported": AVX512_SUPPORTED,
    "neon_supported": NEON_SUPPORTED,
    "hardware_optimizations": HARDWARE_OPTIMIZATIONS,
    "power_efficient_mode": POWER_EFFICIENT_MODE,
    "energy_harvesting_available": ENERGY_HARVESTING_AVAILABLE,
    "battery_powered": BATTERY_POWERED,
    "hardware_capability_level": HARDWARE_CAPABILITY_LEVEL,
}
