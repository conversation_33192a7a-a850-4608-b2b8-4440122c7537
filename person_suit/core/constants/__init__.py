"""
Person Suit - Core Constants
---------------------------

This module provides system-wide constants for the Person Suit framework,
organized in a three-tier structure:

1. System-Wide Constants: Defined in this package, affecting multiple meta-systems
2. Meta-System Constants: Defined in meta-system packages
3. Component-Specific Constants: Defined within component directories

Constants are organized by domain:
- system.py: System-wide operational constants
- security.py: Security-related constants
- performance.py: Performance tuning constants
- hardware.py: Hardware-specific constants

Usage:
```python
from . import DEFAULT_OPERATION_TIMEOUT
from .system import MAX_CONCURRENT_OPERATIONS
from .security import DEFAULT_CAPABILITY_LIFETIME
```

For meta-system constants:
```python
from person_suit.meta_systems.persona_core.constants import WORKING_MEMORY_CAPACITY
```
"""

from .hardware import (
    CPU_COUNT,
    DISK_SPACE_LIMIT,
    GPU_AVAILABLE,
    MEMORY_LIMIT,
    NEURAL_ENGINE_AVAILABLE,
)
from .performance import (
    DEFAULT_BATCH_SIZE,
    DEFAULT_CACHE_SIZE,
    DEFAULT_CHUNK_SIZE,
    DEFAULT_PROCESS_POOL_SIZE,
    DEFAULT_THREAD_POOL_SIZE,
)
from .security import (
    DEFAULT_CAPABILITY_LIFETIME,
    DEFAULT_ENCRYPTION_ALGORITHM,
    DEFAULT_HASH_ALGORITHM,
    DEFAULT_TOKEN_LIFETIME,
    SECURITY_LEVEL,
)

# Re-export key constants for convenience
from .system import (
    DEFAULT_OPERATION_TIMEOUT,
    EXTENDED_OPERATION_TIMEOUT,
    IS_APPLE_SILICON,
    MAX_CONCURRENT_OPERATIONS,
    MAX_DISK_OPERATIONS_PER_SECOND,
    MAX_VECTOR_OPERATIONS_PER_SECOND,
    VECTOR_SEARCH_TIMEOUT,
)

__all__ = [
    # System
    "DEFAULT_OPERATION_TIMEOUT",
    "EXTENDED_OPERATION_TIMEOUT",
    "VECTOR_SEARCH_TIMEOUT",
    "MAX_CONCURRENT_OPERATIONS",
    "MAX_VECTOR_OPERATIONS_PER_SECOND",
    "MAX_DISK_OPERATIONS_PER_SECOND",
    "IS_APPLE_SILICON",
    # Security
    "DEFAULT_CAPABILITY_LIFETIME",
    "DEFAULT_TOKEN_LIFETIME",
    "DEFAULT_ENCRYPTION_ALGORITHM",
    "DEFAULT_HASH_ALGORITHM",
    "SECURITY_LEVEL",
    # Performance
    "DEFAULT_BATCH_SIZE",
    "DEFAULT_CHUNK_SIZE",
    "DEFAULT_CACHE_SIZE",
    "DEFAULT_THREAD_POOL_SIZE",
    "DEFAULT_PROCESS_POOL_SIZE",
    # Hardware
    "CPU_COUNT",
    "MEMORY_LIMIT",
    "DISK_SPACE_LIMIT",
    "GPU_AVAILABLE",
    "NEURAL_ENGINE_AVAILABLE",
]
