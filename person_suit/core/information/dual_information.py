# -*- coding: utf-8 -*-
"""
Defines the DualInformation class, the primary container for dual wave-particle information.

This class encapsulates both the wave (potential, field-like) and
particle (structured, hypergraph) aspects of information, along with state versioning metadata.

It is designed to be immutable, ensuring that operations produce new
instances rather than modifying the state in place.

This implementation follows the principles of contextual adaptive wave programming
without using paradigm names in the code organization.

Related Files:
- person_suit.core.information.wave_state
- person_suit.core.information.particle_state
- docs/design/DualInformation_Implementation_Design.md
"""

import time
import uuid
from dataclasses import dataclass, field
from typing import Optional, Type, TypeVar

# Assuming StateRef is defined within context schemas now
from ..context.schemas import StateRef
from .particle_state import ConcreteParticleState

# Import the concrete state implementations and schema types from new locations
from .wave_state import ConcreteWaveState

# Type variable for the class itself
DI = TypeVar("DI", bound="DualInformation")


# Helper to generate StateRef
def _generate_state_ref() -> StateRef:
    """Generates a unique StateRef, typically a UUID string."""
    return StateRef(str(uuid.uuid4()))


@dataclass(frozen=True)
class DualInformation:
    """
    Immutable container holding WaveState, ParticleState, and versioning metadata.

    Represents information having both wave-like (potential, continuous) and
    particle-like (actualized, discrete) properties.

    Instances are typically created via the `create_initial` or
    `create_next_version` factory methods.

    Attributes:
        wave_state: The wave aspect representation.
        particle_state: The particle aspect representation.
        state_ref: Reference identifying this immutable version.
        previous_state_ref: Optional reference to the preceding state version.
        timestamp: Optional timestamp of this state version's creation.
    """

    # Core state components
    wave_state: Optional[ConcreteWaveState]
    particle_state: ConcreteParticleState

    # Metadata managed by factory methods
    state_ref: StateRef
    previous_state_ref: Optional[StateRef] = field(default=None, compare=False)
    timestamp: Optional[float] = field(default=None, compare=False)

    # --- Factory Methods --- #

    @classmethod
    def create_initial(
        cls: Type[DI],
        particle_state: ConcreteParticleState,
        wave_state: Optional[ConcreteWaveState] = None,
    ) -> DI:
        """
        Factory method to create the first version of a DualInformation state.

        Generates a unique state_ref and sets the timestamp.

        Args:
            particle_state: The initial ParticleState (immutable hypergraph).
            wave_state: The initial WaveState (immutable tensor). If None, an
                empty WaveState is typically assumed or created if necessary.
                If WaveState can truly be absent, adjust type hint.

        Returns:
            A new, immutable DualInformation instance representing the initial state.
        """
        current_time = time.time()
        state_ref = _generate_state_ref()
        # Handle case where wave_state might be None but the attribute requires a ConcreteWaveState
        # Option 1: Default to empty if None is passed (as shown)
        # Option 2: Change attribute type hint to Optional[ConcreteWaveState]
        actual_wave_state = (
            wave_state if wave_state is not None else ConcreteWaveState.empty()
        )

        # Ensure particle_state is not None
        if particle_state is None:
            # Or default: particle_state = ConcreteParticleState.empty()
            raise ValueError("Initial particle_state cannot be None")

        return cls(
            wave_state=actual_wave_state,
            particle_state=particle_state,
            state_ref=state_ref,
            previous_state_ref=None,
            timestamp=current_time,
        )

    @classmethod
    def create_next_version(
        cls: Type[DI],
        previous_state: "DualInformation",
        new_particle_state: ConcreteParticleState,
        new_wave_state: Optional[ConcreteWaveState],
    ) -> DI:
        """
        Factory method to create the next immutable version of a DualInformation state.

        Used by state management logic (e.g., Central State Actor) after applying
        an Effect. Generates a new state_ref, links to the previous state, and
        sets a new timestamp.

        Relies on the provided new_particle_state and new_wave_state potentially
        sharing structure with their predecessors via copy-on-write for performance.

        Args:
            previous_state: The immediately preceding DualInformation instance.
            new_particle_state: The newly computed immutable ParticleState.
            new_wave_state: The newly computed immutable WaveState.

        Returns:
            A new, immutable DualInformation instance representing the updated state.
        """
        current_time = time.time()
        new_state_ref = _generate_state_ref()

        actual_new_wave_state = (
            new_wave_state if new_wave_state is not None else ConcreteWaveState.empty()
        )

        if new_particle_state is None:
            raise ValueError("New particle_state cannot be None in create_next_version")

        return cls(
            wave_state=actual_new_wave_state,
            particle_state=new_particle_state,
            state_ref=new_state_ref,
            previous_state_ref=previous_state.state_ref,
            timestamp=current_time,
        )

    # --- Accessors ---
    # Dataclass provides default accessors


# Define a concrete type alias if needed for clarity elsewhere
ConcreteDualInformation: Type = DualInformation
