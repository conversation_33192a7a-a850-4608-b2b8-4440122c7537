"""Dependency Injection Registration for Application Subsystem.

Registers application context, factory, and related services with the DI container.
Aligns with v0.3 unified paradigm and design documentation for modular, context-aware application orchestration.

References:
- docs/future/unified_paradigm/v0.3/unified_paradigm_v0.3.md
- docs/design/Context_Management_Design.md
"""

from .context import DefaultApplicationContext
from .factory import create_application_context
from .interfaces.context import ApplicationContext
from ..infrastructure.dependency_injection import (
    ServiceLifetime,
    ServiceProvider,
)


def register_with_container(container: ServiceProvider) -> None:
    """Register application subsystem components with the DI container.

    Args:
        container: The DI service provider/container.
    """
    # Register ApplicationContext (singleton)
    container.register(
        ApplicationContext,
        DefaultApplicationContext,
        lifetime=ServiceLifetime.SINGLETON,
    )
    # Register application context factory (singleton)
    container.register(
        create_application_context,
        create_application_context,
        lifetime=ServiceLifetime.SINGLETON,
    )
    # TODO: Register additional application-level services as needed
    # TODO: Support context/capability injection for advanced CAW scenarios
