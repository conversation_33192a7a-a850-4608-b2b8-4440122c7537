"""Application Context.

This module defines the application context interface for managing
the application lifecycle and core services.
"""

import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, Optional

from ...effects.core.runtime import EffectRuntime
from ...infrastructure import dependency_injection as di
from ...infrastructure import monitoring as monitor

logger = logging.getLogger(__name__)


class ApplicationContext(ABC):
    """Interface for application context.

    Manages:
    - Application lifecycle
    - Core services
    - System state
    """

    def __init__(self):
        """Initialize the context."""
        self._container = di.get_container()
        self._effect_runtime = self._container.resolve(EffectRuntime)
        self._monitoring = monitor.get_monitoring_service()
        self._state: Dict[str, Any] = {}

    @abstractmethod
    async def start(self) -> None:
        """Start the application.

        This method should be implemented by concrete contexts to
        initialize and start application-specific components.
        """
        pass

    @abstractmethod
    async def stop(self) -> None:
        """Stop the application.

        This method should be implemented by concrete contexts to
        properly shutdown application-specific components.
        """
        pass

    def get_state(self, key: str) -> Optional[Any]:
        """Get application state value.

        Args:
            key: State key

        Returns:
            State value if exists
        """
        return self._state.get(key)

    def set_state(self, key: str, value: Any) -> None:
        """Set application state value.

        Args:
            key: State key
            value: State value
        """
        self._state[key] = value

    def clear_state(self, key: str) -> None:
        """Clear application state value.

        Args:
            key: State key
        """
        self._state.pop(key, None)
