# -*- coding: utf-8 -*-
"""
File: person_suit/core/application/interfaces/tests/test_events_interface.py
Purpose: Unit tests for event data classes in events_interface.py, focusing on BottleneckEventData.

Tests instantiation, field validation, and usage patterns for BottleneckEventData.
Related Files: events_interface.py
Dependencies: pytest
"""

from datetime import datetime, timezone

import pytest

from ..events_interface import (
    BottleneckEventData,
)


# Test fixtures
@pytest.fixture
def minimal_event():
    """Create a minimal BottleneckEventData instance."""
    return BottleneckEventData(bottleneck_type="CPU")


@pytest.fixture
def full_event():
    """Create a fully populated BottleneckEventData instance."""
    now = datetime(2024, 1, 1, tzinfo=timezone.utc)
    return BottleneckEventData(
        source_component="ResourceMonitor",
        bottleneck_type="Memory",
        impacted_components=["InferenceEngine", "MemoryManager"],
        analysis_details={"mem_usage": 99.1, "duration": 10.5},
        severity="critical",
        resource_type="Memory",
        metric_value=99.1,
        threshold=95.0,
        details={"note": "Sustained high memory usage."},
        timestamp=now,
    )


def test_bottleneck_event_data_minimal(minimal_event):
    """Test minimal instantiation of BottleneckEventData."""
    assert isinstance(minimal_event, BottleneckEventData)
    assert minimal_event.bottleneck_type == "CPU"
    assert minimal_event.impacted_components == []
    assert minimal_event.analysis_details == {}
    assert minimal_event.severity is None
    assert minimal_event.resource_type is None
    assert minimal_event.metric_value is None
    assert minimal_event.threshold is None
    assert minimal_event.details == {}
    assert isinstance(minimal_event.timestamp, datetime)


def test_bottleneck_event_data_full(full_event):
    """Test instantiation with all fields populated."""
    now = datetime(2024, 1, 1, tzinfo=timezone.utc)
    assert full_event.source_component == "ResourceMonitor"
    assert full_event.bottleneck_type == "Memory"
    assert full_event.impacted_components == ["InferenceEngine", "MemoryManager"]
    assert full_event.analysis_details["mem_usage"] == 99.1
    assert full_event.severity == "critical"
    assert full_event.resource_type == "Memory"
    assert full_event.metric_value == 99.1
    assert full_event.threshold == 95.0
    assert full_event.details["note"] == "Sustained high memory usage."
    assert full_event.timestamp == now


def test_bottleneck_event_data_type_enforcement():
    """Test that type errors are raised for invalid field types."""
    with pytest.raises(TypeError):
        BottleneckEventData(bottleneck_type=123)  # Should be str
    with pytest.raises(TypeError):
        BottleneckEventData(bottleneck_type="CPU", impacted_components="notalist")
    with pytest.raises(TypeError):
        BottleneckEventData(bottleneck_type="CPU", analysis_details="notadict")


def test_bottleneck_event_data_example_usage():
    """Test the example usage from the docstring."""
    event = BottleneckEventData(
        source_component="ResourceMonitor",
        bottleneck_type="CPU",
        impacted_components=["InferenceEngine", "MemoryManager"],
        analysis_details={"cpu_usage": 98.5, "duration": 12.3},
        severity="critical",
        resource_type="CPU",
        metric_value=98.5,
        threshold=95.0,
        details={"note": "Sustained high CPU usage detected."},
    )
    assert event.bottleneck_type == "CPU"
    assert event.impacted_components == ["InferenceEngine", "MemoryManager"]
    assert event.analysis_details["cpu_usage"] == 98.5
    assert event.severity == "critical"
    assert event.resource_type == "CPU"
    assert event.metric_value == 98.5
    assert event.threshold == 95.0
    assert event.details["note"] == "Sustained high CPU usage detected."
