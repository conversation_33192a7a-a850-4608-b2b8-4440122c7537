"""
Unified Context Implementation for Person Suit
==============================================

This module provides THE single, unified Context implementation for the entire
Person Suit system, combining features from:

1. CAW DualContext (person_suit/core/caw/context.py)
2. Schemas Context (person_suit/core/caw/schemas.py)
3. Legacy dual_wave DualContext (person_suit/core/infrastructure/dual_wave/context.py)

This unified context enables full CAW alignment across all components.

File Purpose: Unified context for CAW paradigm implementation
Related Files: 
- person_suit/core/caw/acf.py - ACF integration
- person_suit/core/actors/base.py - Actor system integration
- person_suit/core/security/capabilities/types.py - Capability integration
Dependencies: dataclasses, typing, uuid, time, enum, logging

TO BE REMOVED AFTER MIGRATION:
- person_suit/core/caw/context.py
- person_suit/core/caw/schemas.py (Context class only)
- person_suit/core/infrastructure/dual_wave/context.py
"""

import logging
import time
import uuid
from dataclasses import dataclass, field, asdict
from enum import Enum, auto
from typing import Any, Dict, List, Optional, Set, Union
from datetime import datetime

logger = logging.getLogger(__name__)


# === Enums and Types ===

class ObservationMode(Enum):
    """Observation modes for wave-particle duality."""
    WAVE_FOCUSED = auto()      # Emphasize wave aspects
    PARTICLE_FOCUSED = auto()  # Emphasize particle aspects
    BALANCED = auto()          # Balance both aspects
    ADAPTIVE = auto()          # Adapt based on context


class ResourceType(Enum):
    """Types of computational resources."""
    CPU = auto()
    MEMORY = auto()
    NETWORK = auto()
    STORAGE = auto()
    GPU = auto()


# === Supporting Classes ===

@dataclass
class ACFParams:
    """
    Adaptive Computational Fidelity parameters.
    Unified from both CAW and schemas implementations.
    """
    # From CAW implementation
    fidelity_level: float = 0.8  # 0.0 (minimal) to 1.0 (maximum)
    resource_budget: Dict[ResourceType, float] = field(default_factory=dict)
    quality_threshold: float = 0.7
    adaptation_rate: float = 0.1
    enable_degradation: bool = True
    
    # From schemas implementation (compatibility)
    suppress_wave_state: bool = False
    low_precision_wave: bool = False
    sparse_wave: bool = False
    target_wave_dim: Optional[int] = None  # None means use default (e.g., 2048)
    particle_processing_depth: Optional[str] = None  # e.g., "full", "datom_level"
    force_computation_level: Optional[str] = None  # e.g., "full", "simplified", "none"
    low_fidelity_gravity: bool = False
    
    def __post_init__(self):
        """Validate ACF parameters."""
        self.fidelity_level = max(0.0, min(1.0, self.fidelity_level))
        self.quality_threshold = max(0.0, min(1.0, self.quality_threshold))
        self.adaptation_rate = max(0.0, min(1.0, self.adaptation_rate))


@dataclass
class ContextConstraint:
    """A constraint that influences context behavior."""
    type: str  # e.g., "time_limit", "memory_limit", "quality_requirement"
    value: Any
    priority: float = 1.0  # Higher values = more important
    
    def is_satisfied(self, current_state: Dict[str, Any]) -> bool:
        """Check if the constraint is satisfied given current state."""
        if self.type == "time_limit":
            return current_state.get("elapsed_time", 0) <= self.value
        elif self.type == "memory_limit":
            return current_state.get("memory_usage", 0) <= self.value
        elif self.type == "quality_requirement":
            return current_state.get("quality_score", 0) >= self.value
        return True


# Forward references for capability types
# These will be imported properly when circular imports are resolved
Capability = Any  # Will be: from person_suit.core.security.capabilities.types import Capability
CapabilityContext = Any  # Will be: from person_suit.core.security.capabilities.types import CapabilityContext


# === Unified Context ===

@dataclass
class UnifiedContext:
    """
    Unified context for the entire Person Suit system.
    
    This combines ALL features from:
    - CAW DualContext: Full CAW paradigm support
    - Schemas Context: Actor system and capability support
    - Legacy DualContext: Backward compatibility
    
    This serves as the first-class context object that:
    - Propagates through all CAW operations
    - Carries ACF settings for adaptive fidelity
    - Manages resource constraints and budgets
    - Supports wave-particle duality modulation
    - Enables context composition and inheritance
    - Integrates with capability-based security
    """
    
    # === Core Identity ===
    context_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: float = field(default_factory=time.time)
    
    # === Domain and Priority ===
    domain: str = "general"
    priority: str = "normal"  # "low", "normal", "high", "critical"
    goals: List[str] = field(default_factory=list)
    
    # === Resource and ACF Management ===
    acf_setting: ACFParams = field(default_factory=ACFParams)
    resources: Dict[ResourceType, float] = field(default_factory=dict)
    resource_consumption: Dict[ResourceType, float] = field(default_factory=dict)
    constraints: List[ContextConstraint] = field(default_factory=list)
    
    # === Wave-Particle Duality ===
    wave_particle_ratio: float = 0.5  # 0.0 = pure particle, 1.0 = pure wave
    observation_mode: ObservationMode = ObservationMode.BALANCED
    
    # === Context Propagation ===
    parent_context_id: Optional[str] = None
    propagation_path: List[str] = field(default_factory=list)
    creation_time: float = field(default_factory=time.time)
    
    # === Capability Support (from schemas) ===
    current_capability: Optional[Capability] = None
    capability_context: Optional[CapabilityContext] = None
    
    # === Active State (from schemas) ===
    active_goals: List[str] = field(default_factory=list)
    emotional_state_analog: Optional[Any] = None
    neurochemical_analog: Optional[Any] = None
    neutrino_field_analog: Optional[Any] = None
    
    # === Metadata and Properties ===
    properties: Dict[str, Any] = field(default_factory=dict)
    custom_context: Dict[str, Any] = field(default_factory=dict)
    
    # === Legacy Compatibility ===
    causal_context_id: Optional[str] = None  # From schemas
    observer_id: Optional[str] = None  # From dual_wave
    
    def __post_init__(self):
        """Initialize and validate context after creation."""
        # Validate wave-particle ratio
        self.wave_particle_ratio = max(0.0, min(1.0, self.wave_particle_ratio))
        
        # Add self to propagation path
        if self.context_id not in self.propagation_path:
            self.propagation_path.append(self.context_id)
        
        # Merge active_goals with goals (remove duplicates)
        all_goals = set(self.goals + self.active_goals)
        self.goals = list(all_goals)
        self.active_goals = self.goals.copy()
        
        logger.debug(f"Created UnifiedContext {self.context_id} for domain '{self.domain}'")
    
    # === Context Operations ===
    
    def compose(self, other: Optional['UnifiedContext']) -> 'UnifiedContext':
        """
        Compose this context with another, creating a new merged context.
        
        Args:
            other: The other context to compose with
            
        Returns:
            A new composed context
        """
        if not other:
            return self._copy()
        
        # Merge domains
        new_domain = f"{self.domain}+{other.domain}"
        
        # Take higher priority
        priority_order = {"low": 1, "normal": 2, "high": 3, "critical": 4}
        self_priority = priority_order.get(self.priority, 2)
        other_priority = priority_order.get(other.priority, 2)
        new_priority = "high" if max(self_priority, other_priority) >= 3 else "normal"
        
        # Merge goals
        new_goals = list(set(self.goals + other.goals))
        
        # Merge ACF settings (take more restrictive)
        new_acf = ACFParams(
            fidelity_level=min(self.acf_setting.fidelity_level, other.acf_setting.fidelity_level),
            resource_budget={**self.acf_setting.resource_budget, **other.acf_setting.resource_budget},
            quality_threshold=max(self.acf_setting.quality_threshold, other.acf_setting.quality_threshold),
            adaptation_rate=(self.acf_setting.adaptation_rate + other.acf_setting.adaptation_rate) / 2,
            enable_degradation=self.acf_setting.enable_degradation and other.acf_setting.enable_degradation,
            # Merge other ACF fields
            suppress_wave_state=self.acf_setting.suppress_wave_state or other.acf_setting.suppress_wave_state,
            low_precision_wave=self.acf_setting.low_precision_wave or other.acf_setting.low_precision_wave,
            sparse_wave=self.acf_setting.sparse_wave or other.acf_setting.sparse_wave,
            target_wave_dim=min(
                self.acf_setting.target_wave_dim or 2048,
                other.acf_setting.target_wave_dim or 2048
            ),
        )
        
        # Merge resources (take minimum available)
        new_resources = {}
        all_resource_types = set(self.resources.keys()) | set(other.resources.keys())
        for resource_type in all_resource_types:
            self_amount = self.resources.get(resource_type, float('inf'))
            other_amount = other.resources.get(resource_type, float('inf'))
            new_resources[resource_type] = min(self_amount, other_amount)
        
        # Merge constraints
        new_constraints = self.constraints + other.constraints
        
        # Average wave-particle ratio
        new_wave_particle_ratio = (self.wave_particle_ratio + other.wave_particle_ratio) / 2
        
        # Merge propagation paths
        new_propagation_path = self.propagation_path + other.propagation_path
        
        # Merge properties and custom context
        new_properties = {**self.properties, **other.properties}
        new_custom = {**self.custom_context, **other.custom_context}
        
        # Take most restrictive capability
        new_capability = self.current_capability or other.current_capability
        new_capability_context = self.capability_context or other.capability_context
        
        return UnifiedContext(
            domain=new_domain,
            priority=new_priority,
            goals=new_goals,
            acf_setting=new_acf,
            resources=new_resources,
            constraints=new_constraints,
            wave_particle_ratio=new_wave_particle_ratio,
            observation_mode=self.observation_mode,  # Keep self's observation mode
            parent_context_id=self.context_id,
            propagation_path=new_propagation_path,
            properties=new_properties,
            custom_context=new_custom,
            current_capability=new_capability,
            capability_context=new_capability_context,
            emotional_state_analog=self.emotional_state_analog or other.emotional_state_analog,
            neurochemical_analog=self.neurochemical_analog or other.neurochemical_analog,
            neutrino_field_analog=self.neutrino_field_analog or other.neutrino_field_analog,
        )
    
    def restrict(self, domain_filter: str) -> 'UnifiedContext':
        """
        Create a restricted version of this context for a specific domain.
        
        Args:
            domain_filter: The domain to restrict to
            
        Returns:
            A new restricted context
        """
        # Filter goals relevant to the domain
        filtered_goals = [goal for goal in self.goals if domain_filter.lower() in goal.lower()]
        
        # Reduce ACF fidelity for restricted context
        restricted_acf = ACFParams(
            fidelity_level=self.acf_setting.fidelity_level * 0.8,
            resource_budget={k: v * 0.8 for k, v in self.acf_setting.resource_budget.items()},
            quality_threshold=self.acf_setting.quality_threshold,
            adaptation_rate=self.acf_setting.adaptation_rate,
            enable_degradation=self.acf_setting.enable_degradation,
            # Copy other fields
            suppress_wave_state=self.acf_setting.suppress_wave_state,
            low_precision_wave=self.acf_setting.low_precision_wave,
            sparse_wave=self.acf_setting.sparse_wave,
            target_wave_dim=self.acf_setting.target_wave_dim,
            particle_processing_depth=self.acf_setting.particle_processing_depth,
            force_computation_level=self.acf_setting.force_computation_level,
            low_fidelity_gravity=self.acf_setting.low_fidelity_gravity,
        )
        
        # Filter constraints relevant to the domain
        filtered_constraints = [c for c in self.constraints if domain_filter.lower() in c.type.lower()]
        
        return UnifiedContext(
            domain=domain_filter,
            priority=self.priority,
            goals=filtered_goals,
            acf_setting=restricted_acf,
            resources=self.resources.copy(),
            resource_consumption=self.resource_consumption.copy(),
            constraints=filtered_constraints,
            wave_particle_ratio=self.wave_particle_ratio,
            observation_mode=self.observation_mode,
            parent_context_id=self.context_id,
            propagation_path=self.propagation_path + [f"restricted_{domain_filter}"],
            properties=self.properties.copy(),
            custom_context=self.custom_context.copy(),
            current_capability=self.current_capability,
            capability_context=self.capability_context,
            emotional_state_analog=self.emotional_state_analog,
            neurochemical_analog=self.neurochemical_analog,
            neutrino_field_analog=self.neutrino_field_analog,
        )
    
    def modulate(self, operation: str) -> Dict[str, Any]:
        """
        Modulate context parameters for a specific operation.
        
        Args:
            operation: The operation being performed
            
        Returns:
            Modulated parameters for the operation
        """
        modulation = {
            "fidelity_level": self.acf_setting.fidelity_level,
            "wave_particle_ratio": self.wave_particle_ratio,
            "priority_weight": {"low": 0.5, "normal": 1.0, "high": 1.5, "critical": 2.0}.get(self.priority, 1.0),
            "resource_multiplier": 1.0,
        }
        
        # Operation-specific modulation
        if operation in ["memory_encoding", "analysis"]:
            # Increase fidelity for critical operations
            modulation["fidelity_level"] *= 1.2
            modulation["resource_multiplier"] = 1.5
        elif operation in ["background_processing", "cleanup"]:
            # Reduce fidelity for background operations
            modulation["fidelity_level"] *= 0.7
            modulation["resource_multiplier"] = 0.5
        
        # Apply constraints
        for constraint in self.constraints:
            if constraint.type == "quality_requirement":
                modulation["fidelity_level"] = max(modulation["fidelity_level"], constraint.value)
            elif constraint.type == "resource_limit":
                modulation["resource_multiplier"] = min(modulation["resource_multiplier"], constraint.value)
        
        return modulation
    
    # === Resource Management ===
    
    def get_available_resources(self) -> Dict[ResourceType, float]:
        """Get currently available resources."""
        available = {}
        for resource_type in self.resources:
            consumed = self.resource_consumption.get(resource_type, 0.0)
            available[resource_type] = max(0.0, self.resources[resource_type] - consumed)
        return available
    
    def consume_resources(self, consumption: Dict[ResourceType, float]) -> bool:
        """
        Attempt to consume resources from the context.
        
        Args:
            consumption: Resources to consume
            
        Returns:
            True if resources were successfully consumed
        """
        # Check if we have enough resources
        available = self.get_available_resources()
        for resource_type, amount in consumption.items():
            if available.get(resource_type, 0.0) < amount:
                return False
        
        # Consume resources
        for resource_type, amount in consumption.items():
            current = self.resource_consumption.get(resource_type, 0.0)
            self.resource_consumption[resource_type] = current + amount
        
        return True
    
    def release_resources(self, release: Dict[ResourceType, float]) -> None:
        """Release previously consumed resources."""
        for resource_type, amount in release.items():
            if resource_type in self.resource_consumption:
                self.resource_consumption[resource_type] = max(
                    0.0, self.resource_consumption[resource_type] - amount
                )
    
    # === Constraint Management ===
    
    def add_constraint(self, constraint: ContextConstraint) -> None:
        """Add a constraint to the context."""
        self.constraints.append(constraint)
    
    def check_constraints(self, current_state: Dict[str, Any]) -> List[ContextConstraint]:
        """
        Check which constraints are violated.
        
        Args:
            current_state: Current system state
            
        Returns:
            List of violated constraints
        """
        violated = []
        for constraint in self.constraints:
            if not constraint.is_satisfied(current_state):
                violated.append(constraint)
        return violated
    
    # === Adaptation ===
    
    def adapt_fidelity(self, performance_feedback: Dict[str, float]) -> None:
        """
        Adapt ACF settings based on performance feedback.
        
        Args:
            performance_feedback: Metrics about recent performance
        """
        quality_score = performance_feedback.get("quality_score", 0.5)
        resource_usage = performance_feedback.get("resource_usage", 0.5)
        
        # Adapt fidelity based on feedback
        if quality_score < self.acf_setting.quality_threshold:
            # Increase fidelity if quality is too low
            adjustment = self.acf_setting.adaptation_rate * (self.acf_setting.quality_threshold - quality_score)
            self.acf_setting.fidelity_level = min(1.0, self.acf_setting.fidelity_level + adjustment)
        elif resource_usage > 0.8:
            # Decrease fidelity if resource usage is too high
            adjustment = self.acf_setting.adaptation_rate * (resource_usage - 0.8)
            self.acf_setting.fidelity_level = max(0.1, self.acf_setting.fidelity_level - adjustment)
    
    # === Property Management ===
    
    def get_property(self, key: str, default: Any = None) -> Any:
        """Get a property from the context."""
        # Check both properties and custom_context for compatibility
        return self.properties.get(key, self.custom_context.get(key, default))
    
    def set_property(self, key: str, value: Any) -> None:
        """Set a property in the context."""
        self.properties[key] = value
    
    # === Factory Methods ===
    
    @classmethod
    def create_default(cls, domain: str, priority: str = "normal") -> 'UnifiedContext':
        """Create a default context for a domain."""
        return cls(
            domain=domain,
            priority=priority,
            acf_setting=ACFParams(),
            resources={
                ResourceType.CPU: 1.0,
                ResourceType.MEMORY: 1.0,
                ResourceType.NETWORK: 1.0,
                ResourceType.STORAGE: 1.0,
            }
        )
    
    @classmethod
    def create_high_fidelity(cls, domain: str) -> 'UnifiedContext':
        """Create a high-fidelity context for critical operations."""
        return cls(
            domain=domain,
            priority="high",
            acf_setting=ACFParams(
                fidelity_level=0.95,
                quality_threshold=0.9,
                enable_degradation=False,
            ),
            resources={
                ResourceType.CPU: 2.0,
                ResourceType.MEMORY: 2.0,
                ResourceType.NETWORK: 1.5,
                ResourceType.STORAGE: 1.5,
            }
        )
    
    @classmethod
    def create_low_resource(cls, domain: str) -> 'UnifiedContext':
        """Create a low-resource context for background operations."""
        return cls(
            domain=domain,
            priority="low",
            acf_setting=ACFParams(
                fidelity_level=0.5,
                quality_threshold=0.4,
                enable_degradation=True,
            ),
            resources={
                ResourceType.CPU: 0.3,
                ResourceType.MEMORY: 0.3,
                ResourceType.NETWORK: 0.2,
                ResourceType.STORAGE: 0.5,
            }
        )
    
    @classmethod
    def create_wave_focused(cls, domain: str) -> 'UnifiedContext':
        """Create a wave-focused context that emphasizes wave-like properties."""
        context = cls(
            domain=domain,
            priority="normal",
            observation_mode=ObservationMode.WAVE_FOCUSED,
            wave_particle_ratio=0.8,
        )
        
        # Set wave-focused properties
        context.set_property("wave_factor", 2.0)
        context.set_property("particle_factor", 0.5)
        context.set_property("collapse_rate", 0.1)
        
        return context
    
    @classmethod
    def create_particle_focused(cls, domain: str) -> 'UnifiedContext':
        """Create a particle-focused context that emphasizes particle-like properties."""
        context = cls(
            domain=domain,
            priority="normal",
            observation_mode=ObservationMode.PARTICLE_FOCUSED,
            wave_particle_ratio=0.2,
        )
        
        # Set particle-focused properties
        context.set_property("wave_factor", 0.5)
        context.set_property("particle_factor", 2.0)
        context.set_property("collapse_rate", 0.3)
        
        return context
    
    # === Serialization ===
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert context to dictionary representation."""
        return {
            "context_id": self.context_id,
            "timestamp": self.timestamp,
            "domain": self.domain,
            "priority": self.priority,
            "goals": self.goals,
            "acf_setting": asdict(self.acf_setting),
            "resources": {rt.name: amount for rt, amount in self.resources.items()},
            "resource_consumption": {rt.name: amount for rt, amount in self.resource_consumption.items()},
            "constraints": [{"type": c.type, "value": c.value, "priority": c.priority} for c in self.constraints],
            "wave_particle_ratio": self.wave_particle_ratio,
            "observation_mode": self.observation_mode.name,
            "parent_context_id": self.parent_context_id,
            "propagation_path": self.propagation_path,
            "creation_time": self.creation_time,
            "properties": self.properties,
            "custom_context": self.custom_context,
            "active_goals": self.active_goals,
            "causal_context_id": self.causal_context_id,
            "observer_id": self.observer_id,
            # Note: Skipping capability and analog fields for serialization
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UnifiedContext':
        """Create context from dictionary representation."""
        # Reconstruct ACF settings
        acf_data = data.get("acf_setting", {})
        acf_setting = ACFParams(**acf_data)
        
        # Reconstruct resources
        resources = {ResourceType[rt]: amount for rt, amount in data.get("resources", {}).items()}
        resource_consumption = {ResourceType[rt]: amount for rt, amount in data.get("resource_consumption", {}).items()}
        
        # Reconstruct constraints
        constraints = [
            ContextConstraint(type=c["type"], value=c["value"], priority=c.get("priority", 1.0))
            for c in data.get("constraints", [])
        ]
        
        # Reconstruct observation mode
        obs_mode = ObservationMode[data.get("observation_mode", "BALANCED")]
        
        return cls(
            context_id=data.get("context_id", str(uuid.uuid4())),
            timestamp=data.get("timestamp", time.time()),
            domain=data["domain"],
            priority=data.get("priority", "normal"),
            goals=data.get("goals", []),
            acf_setting=acf_setting,
            resources=resources,
            resource_consumption=resource_consumption,
            constraints=constraints,
            wave_particle_ratio=data.get("wave_particle_ratio", 0.5),
            observation_mode=obs_mode,
            parent_context_id=data.get("parent_context_id"),
            propagation_path=data.get("propagation_path", []),
            creation_time=data.get("creation_time", time.time()),
            properties=data.get("properties", {}),
            custom_context=data.get("custom_context", {}),
            active_goals=data.get("active_goals", []),
            causal_context_id=data.get("causal_context_id"),
            observer_id=data.get("observer_id"),
        )
    
    # === Utility Methods ===
    
    def _copy(self) -> 'UnifiedContext':
        """Create a copy of this context."""
        return UnifiedContext(
            context_id=str(uuid.uuid4()),  # New ID for copy
            timestamp=time.time(),
            domain=self.domain,
            priority=self.priority,
            goals=self.goals.copy(),
            acf_setting=ACFParams(**asdict(self.acf_setting)),
            resources=self.resources.copy(),
            resource_consumption=self.resource_consumption.copy(),
            constraints=self.constraints.copy(),
            wave_particle_ratio=self.wave_particle_ratio,
            observation_mode=self.observation_mode,
            parent_context_id=self.context_id,  # Parent is original
            propagation_path=self.propagation_path + [f"copy_of_{self.context_id}"],
            creation_time=time.time(),
            properties=self.properties.copy(),
            custom_context=self.custom_context.copy(),
            active_goals=self.active_goals.copy(),
            current_capability=self.current_capability,
            capability_context=self.capability_context,
            emotional_state_analog=self.emotional_state_analog,
            neurochemical_analog=self.neurochemical_analog,
            neutrino_field_analog=self.neutrino_field_analog,
            causal_context_id=self.causal_context_id,
            observer_id=self.observer_id,
        )
    
    def matches(self, other: 'UnifiedContext') -> float:
        """
        Compute the match score between this context and another.
        For compatibility with legacy dual_wave context.
        
        Args:
            other: Another context
            
        Returns:
            A float between 0 and 1 representing the match score
        """
        # Domain match
        domain_match = 1.0 if self.domain == other.domain else 0.0
        
        # Priority match
        priority_match = 1.0 if self.priority == other.priority else 0.0
        
        # Constraint match
        constraint_match = 0.0
        if self.constraints and other.constraints:
            common_types = set(c.type for c in self.constraints) & set(c.type for c in other.constraints)
            constraint_match = len(common_types) / max(len(self.constraints), 1)
        
        # Observation mode match
        observation_match = 1.0 if self.observation_mode == other.observation_mode else 0.0
        
        # Wave-particle ratio similarity
        wave_similarity = 1.0 - abs(self.wave_particle_ratio - other.wave_particle_ratio)
        
        # Combine scores with weights
        domain_weight = 0.3
        priority_weight = 0.2
        constraint_weight = 0.2
        observation_weight = 0.15
        wave_weight = 0.15
        
        weighted_score = (
            domain_match * domain_weight
            + priority_match * priority_weight
            + constraint_match * constraint_weight
            + observation_match * observation_weight
            + wave_similarity * wave_weight
        )
        
        return weighted_score
    
    def with_observation_mode(self, mode: ObservationMode) -> 'UnifiedContext':
        """
        Create a new context with a different observation mode.
        For compatibility with legacy dual_wave context.
        """
        new_context = self._copy()
        new_context.observation_mode = mode
        return new_context
    
    def with_properties(self, properties: Dict[str, Any]) -> 'UnifiedContext':
        """
        Create a new context with updated properties.
        For compatibility with legacy dual_wave context.
        """
        new_context = self._copy()
        new_context.properties.update(properties)
        return new_context
    
    def is_expired(self, comparison_time: Optional[float] = None) -> bool:
        """
        Check if context has expired (for capability integration).
        Currently always returns False as contexts don't expire by default.
        """
        # This could be extended to check constraint-based expiration
        return False


# === Backward Compatibility Aliases ===

# For easy migration from old implementations
DualContext = UnifiedContext  # Alias for CAW migration
Context = UnifiedContext  # Alias for schemas migration


# === Export All ===
__all__ = [
    'UnifiedContext',
    'DualContext',  # Compatibility alias
    'Context',  # Compatibility alias
    'ObservationMode',
    'ResourceType',
    'ACFParams',
    'ContextConstraint',
] 