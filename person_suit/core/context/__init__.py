"""
Unified Context Module for Person Suit
=====================================

This module provides the single, unified context implementation for the entire
Person Suit system, replacing all previous context implementations.

File Purpose: Export unified context for CAW paradigm implementation
Related Files: 
- person_suit/core/context/unified.py - The unified context implementation
Dependencies: None
"""

from person_suit.core.context.unified import (
    UnifiedContext,
    DualContext,  # Compatibility alias
    Context,  # Compatibility alias
    ObservationMode,
    ResourceType,
    ACFParams,
    ContextConstraint,
)

__all__ = [
    'UnifiedContext',
    'DualContext',
    'Context',
    'ObservationMode',
    'ResourceType',
    'ACFParams',
    'ContextConstraint',
]
