"""
CAW Choreography Engine and Definitions API.

This module provides the public API for the choreography system.
Implementation details have been moved to the choreography package.

See person_suit.core.actors.choreography.__init__ for package details.
"""

# Re-export everything from the choreography package
from .choreography import (
    BASIC_REQUEST_TEMPLATE,
    BASIC_RESPONSE_SCHEMA,
    GET_DATA_REQUEST_CHOREO_V2,
    PROCESS_DATA_CONTEXT_UPDATE_DICT,
    ActorID,
    ChoreographyDefinition,
    ChoreographyEngine,
    ChoreographyError,
    ChoreographyID,
    ChoreographyStep,
    generate_correlation_id,
)

# Export all names
__all__ = [
    "ChoreographyStep",
    "ChoreographyDefinition",
    "ChoreographyEngine",
    "ChoreographyError",
    "ActorID",
    "ChoreographyID",
    "generate_correlation_id",
    "BASIC_REQUEST_TEMPLATE",
    "BASIC_RESPONSE_SCHEMA",
    "PROCESS_DATA_CONTEXT_UPDATE_DICT",
    "GET_DATA_REQUEST_CHOREO_V2",
]
