# -*- coding: utf-8 -*-
"""
Actor System Messaging Implementation.

This module provides the messaging functionality for the Actor System, handling
message delivery, the ask pattern, and reply resolution.

CAW Alignment:
- Passes Context within StandardActorMessage.
- Supports ask pattern with correlation IDs and timeouts.
- Handles message delivery and correlation.

Related Files:
- person_suit.core.actors.actor_system.py (Main ActorSystem class)
- person_suit.core.actors.base.py (Defines StandardActorMessage)
"""

import asyncio
import logging
import uuid
from asyncio import Future
from typing import Any, Dict, Optional

from .base import ActorPath, ActorRef, StandardActorMessage
from ..caw.schemas import Context
from ..security.capabilities import CapabilityToken

logger = logging.getLogger(__name__)


class MessagingManager:
    """Manages actor messaging, delivery, and the ask pattern."""

    def __init__(self, actor_system):
        """
        Initialize the messaging manager.

        Args:
            actor_system: Reference to the parent ActorSystem
        """
        self._actor_system = actor_system
        # Ask pattern futures
        self._ask_futures: Dict[str, Future] = {}

    def clear(self):
        """Clear all pending ask futures."""
        for future in self._ask_futures.values():
            if not future.done():
                future.cancel("ActorSystem shutting down")
        self._ask_futures.clear()

    async def send(self, target_id: str, message: Any):
        """
        Send a message to a target actor identified by ActorID (path string).
        Ensures the message is StandardActorMessage.

        Args:
            target_id: Target actor ID (path)
            message: Message to send
        """
        if not isinstance(message, StandardActorMessage):
            logger.error(
                f"ActorSystem.send received non-StandardActorMessage for {target_id}. Type: {type(message)}. Message dropped."
            )
            return
        try:
            path_str = target_id.strip("/")
            path = ActorPath(
                system_name=self._actor_system.system_name,
                path_elements=tuple(path_str.split("/")) if path_str else tuple(),
            )
            # Use internal deliver_message which handles ask/reply logic
            await self.deliver_message(path, message)
        except Exception as e:
            logger.warning(
                f"Error parsing ActorID or sending message to '{target_id}' in send: {e}"
            )

    async def deliver_message(
        self, target_path: ActorPath, message: StandardActorMessage
    ) -> bool:
        """
        Internal method to deliver a message, handling ask replies first.

        Args:
            target_path: Path of the target actor
            message: Message to deliver

        Returns:
            True if the message was successfully delivered (or handled as a reply),
            False otherwise (e.g., target not found)
        """
        # --- Ask/Reply Hook --- #
        if message.correlation_id:
            handled = self.resolve_ask_future(message.correlation_id, message.payload)
            if handled:
                # If it was a reply for an active ask future, consume it.
                # Design decision: Should replies ALSO be delivered to the actor?
                # For now, assume no: replies are primarily for the asker.
                logger.debug(
                    f"Consumed message for {target_path} as reply for corr_id {message.correlation_id}"
                )
                return True  # Indicate message was handled (as a reply)
            # If not handled (no matching future), maybe it's a late reply or
            # intended for the actor anyway. Let it proceed to enqueueing.
            logger.debug(
                f"Reply for {target_path} (corr_id {message.correlation_id}) had no matching future, delivering to actor."
            )
        # --- End Ask/Reply Hook ---

        actor = self._actor_system._actors.get(target_path)
        if actor and actor._running:  # Check if actor is running
            await actor._enqueue_message(message)
            return True
        else:
            logger.warning(
                f"ActorSystem.deliver_message: Target actor {target_path} not found or not running."
            )
            # TODO: Implement dead letter handling for message
            return False

    async def ask(
        self,
        target_ref: ActorRef,
        message_type: str,
        payload: Any,
        context: Optional[Context] = None,
        capability_token: Optional[CapabilityToken] = None,
        timeout: Optional[float] = 5.0,
    ) -> Any:
        """
        Handle the ask pattern, sending a message and awaiting a future.

        Args:
            target_ref: Reference to the target actor
            message_type: Type of message to send
            payload: Message payload
            context: Optional message context
            capability_token: Optional capability token
            timeout: Timeout in seconds for the request

        Returns:
            The reply payload

        Raises:
            RuntimeError: If the system is not running
            LookupError: If the target actor doesn't exist
            TimeoutError: If the request times out
            CancelledError: If the request is cancelled
        """
        if not self._actor_system._running:
            raise RuntimeError(
                f"ActorSystem '{self._actor_system.system_name}' is not running."
            )

        correlation_id = str(uuid.uuid4())
        future = asyncio.get_running_loop().create_future()
        self._ask_futures[correlation_id] = future

        actor_message = StandardActorMessage(
            type=message_type,
            payload=payload,
            context=context,
            sender_ref=None,  # Replies come back via correlation_id
            capability_token=capability_token,
            correlation_id=correlation_id,
        )

        delivered = await self.deliver_message(target_ref.path, actor_message)

        if not delivered:
            # Target actor didn't exist, fail the future immediately
            self._ask_futures.pop(correlation_id, None)
            future.set_exception(
                LookupError(f"Target actor {target_ref.path} for ask not found.")
            )
            raise future.exception()  # Raise immediately

        try:
            # Wait for the future to be resolved by resolve_ask_future
            return await asyncio.wait_for(future, timeout)
        except asyncio.TimeoutError:
            logger.warning(
                f"Ask request to {target_ref.path} with correlation ID {correlation_id} timed out."
            )
            self._ask_futures.pop(correlation_id, None)
            raise
        except asyncio.CancelledError:
            logger.info(
                f"Ask request to {target_ref.path} with correlation ID {correlation_id} was cancelled."
            )
            self._ask_futures.pop(correlation_id, None)
            raise
        finally:
            # Ensure future is removed if it wasn't already (e.g., on success/timeout/cancel)
            self._ask_futures.pop(correlation_id, None)

    def resolve_ask_future(self, correlation_id: str, reply_payload: Any) -> bool:
        """
        Called internally by deliver_message to resolve a pending future.

        Args:
            correlation_id: Correlation ID of the request
            reply_payload: Payload of the reply

        Returns:
            True if a future was found and resolved, False otherwise
        """
        future = self._ask_futures.get(correlation_id)
        if future and not future.done():
            future.set_result(reply_payload)
            return True  # Indicate future was resolved
        elif future and future.done():
            logger.warning(
                f"Received reply for already completed ask future: {correlation_id}"
            )
            return (
                True  # Still counts as handled in terms of consuming the reply message
            )
        # If future is None, it might have timed out or never existed.
        return False  # Indicate no active future was resolved
