# -*- coding: utf-8 -*-
"""
Module: person_suit.core.actors.base
Purpose: Foundational classes for the Actor system within the Person Suit CAW framework.

Defines the core building blocks like Actor, ActorRef, ActorPath, ActorContext,
and the standard message format.
"""

import asyncio
import logging
import uuid
from abc import ABC, abstractmethod
from asyncio import Queue
from dataclasses import dataclass, field
from typing import (
    TYPE_CHECKING,
    Any,
    Dict,
    Generic,
    List,
    Optional,
    Set,
    Tuple,
    TypeVar,
)

# Import Context for message envelope - Adjust path if needed
from person_suit.core.context import Context

# REMOVED: No longer importing notify_choreography_reply
# from .choreography import notify_choreography_reply

if TYPE_CHECKING:
    from person_suit.core.actors.actor_system import (
        ActorSystem,
    )  # Avoid circular import

    # Import needed for supervision later
    from person_suit.core.actors.supervision import SupervisionStrategyImplementation

from ..security.capabilities.types import Capability  # Import Capability

logger = logging.getLogger(__name__)

# Type Variable for message payloads
T = TypeVar("T")

# --- Message Structure ---


@dataclass
class StandardActorMessage(Generic[T]):
    """Standard message envelope for actor communication."""

    type: str  # Describes the message type/intent
    payload: T
    context: Optional[Context] = None  # CAW Context
    sender_ref: Optional["ActorRef"] = None  # Reference to the sender actor
    capability_token: Optional["CapabilityToken"] = (
        None  # Token authorizing the sender's action
    )
    correlation_id: Optional[str] = None  # For request-reply patterns / choreography
    message_id: str = field(default_factory=lambda: str(uuid.uuid4()))


# --- Actor Identification ---


@dataclass(frozen=True)  # Immutable actor path
class ActorPath:
    """
    Immutable representation of an actor's unique hierarchical path.

    Example: "actor_system_name://parent_actor/child_actor"
    """

    system_name: str
    path_elements: tuple[str, ...]  # Use tuple for immutability

    def __str__(self) -> str:
        """Convert the actor path to a string."""
        return f"{self.system_name}://{'/'.join(self.path_elements)}"

    @classmethod
    def from_string(
        cls, path_str: str, system_name: str = "default_system"
    ) -> "ActorPath":
        """
        Create an actor path from a string representation relative to a system.
        Assumes format like "parent/child" or "/parent/child".
        """
        path = path_str.strip("/")
        path_elements = tuple(path.split("/")) if path else tuple()
        return cls(system_name=system_name, path_elements=path_elements)

    def child(self, name: str) -> "ActorPath":
        """Create a child actor path."""
        if not name or "/" in name:
            raise ValueError(f"Invalid child name: {name}")
        return ActorPath(
            system_name=self.system_name, path_elements=self.path_elements + (name,)
        )

    @property
    def parent(self) -> Optional["ActorPath"]:
        """Get the parent actor path."""
        if not self.path_elements:
            return None
        return ActorPath(
            system_name=self.system_name, path_elements=self.path_elements[:-1]
        )

    @property
    def name(self) -> str:
        """Get the actor's own name."""
        return self.path_elements[-1] if self.path_elements else ""

    def is_root(self) -> bool:
        """Check if this is a root-level actor path."""
        return not self.path_elements


# --- Actor Reference ---


@dataclass(frozen=True)  # ActorRef should be lightweight and immutable
class ActorRef:
    """
    An immutable reference to an actor, used for sending messages.
    """

    path: ActorPath
    _system_ref: "ActorSystem"  # Use forward reference

    async def tell(
        self,
        message_type: str,
        payload: Any,
        context: Optional[Context] = None,
        sender: Optional["ActorRef"] = None,
        capability_token: Optional["CapabilityToken"] = None,  # Added token
    ) -> None:
        """
        Send a fire-and-forget message to the actor.

        Args:
            message_type: The type identifier for the message.
            payload: The message content.
            context: Optional CAW Context.
            sender: Optional reference to the sending actor.
            capability_token: Optional token authorizing the sender's action for this message.
        """
        actor_message = StandardActorMessage(
            type=message_type,
            payload=payload,
            context=context,
            sender_ref=sender,
            capability_token=capability_token,  # Pass token
        )
        # Delegate delivery to the actor system
        await self._system_ref.deliver_message(self.path, actor_message)

    async def ask(
        self,
        message_type: str,
        payload: Any,
        context: Optional[Context] = None,
        capability_token: Optional["CapabilityToken"] = None,  # Added token
        timeout: Optional[float] = 5.0,
    ) -> Any:
        """
        Send a message and wait for a reply.

        Note: Requires ActorSystem to implement reply handling (e.g., futures).

        Args:
            message_type: The type identifier for the message.
            payload: The message content.
            context: Optional CAW Context.
            capability_token: Optional token authorizing the sender's action for this request.
            timeout: Timeout in seconds.

        Returns:
            The payload of the reply message.

        Raises:
            TimeoutError: If the reply is not received within the timeout.
        """
        # Implementation depends on ActorSystem's ask mechanism
        # Pass the capability token along to the ask implementation
        return await self._system_ref.ask(
            self, message_type, payload, context, capability_token, timeout
        )

    def __hash__(self) -> int:
        return hash(self.path)

    def __eq__(self, other: object) -> bool:
        if not isinstance(other, ActorRef):
            return NotImplemented
        return self.path == other.path

    def __str__(self) -> str:
        return f"ActorRef({self.path})"


# --- Actor Context ---


@dataclass
class ActorContext:
    """
    Provides contextual information and capabilities within an actor's execution.
    Passed to lifecycle methods and accessible within the actor.
    """

    self_ref: ActorRef
    parent: Optional[ActorRef]
    actor_system: "ActorSystem"  # Use forward reference
    children: Set[ActorRef] = field(default_factory=set)  # Managed by Actor/System
    # Supervision strategy for this actor (set during creation)
    supervision_strategy: Optional["SupervisionStrategyImplementation"] = None
    # Arguments used to create the actor instance
    creation_args: Tuple = field(default_factory=tuple)
    creation_kwargs: Dict[str, Any] = field(default_factory=dict)
    # Capabilities held by the actor
    capabilities: List[Capability] = field(default_factory=list)

    @property
    def path(self) -> ActorPath:
        return self.self_ref.path


# --- Base Actor Class ---


class Actor(ABC):
    """
    Abstract Base Class for all actors in the Person Suit CAW system.

    Provides lifecycle methods, message handling loop, and context access.
    Error handling delegates to the supervision strategy defined in the context.
    """

    _context: ActorContext
    _mailbox: Queue[Optional[StandardActorMessage]]
    _task: Optional[asyncio.Task]
    _running: bool
    _message_context: Optional[Context]  # Context of the currently processed message

    def __init__(self):
        """Initializes internal actor state. Context is set later by ActorSystem."""
        # _context will be assigned by the ActorSystem during creation
        self._mailbox = Queue()
        self._task = None
        self._running = False
        self._message_context = None
        logger.debug("Actor instance created (path not yet assigned).")

    @property
    def context(self) -> ActorContext:
        """Read-only access to the actor's context."""
        if not hasattr(self, "_context"):
            raise RuntimeError("Actor context has not been initialized.")
        return self._context

    @property
    def self_ref(self) -> ActorRef:
        """Read-only access to the actor's own reference."""
        return self.context.self_ref

    @property
    def path(self) -> ActorPath:
        """Read-only access to the actor's path."""
        return self.context.path

    @property
    def current_message_context(self) -> Optional[Context]:
        """Provides access to the CAW context of the message currently being processed."""
        return self._message_context

    # --- Lifecycle Methods (to be overridden by subclasses) ---

    async def pre_start(self) -> None:
        """Called asynchronously before the actor starts processing messages."""
        pass

    async def post_stop(self) -> None:
        """Called asynchronously after the actor stops processing messages."""
        pass

    async def pre_restart(self, reason: Exception) -> None:
        """Called before restarting the actor after a failure."""
        pass

    async def post_restart(self, reason: Exception) -> None:
        """Called after restarting the actor after a failure."""
        pass

    # --- Control Methods ---

    async def start(self):
        """Starts the actor's message processing loop."""
        if self._running:
            return
        if not hasattr(self, "_context"):
            raise RuntimeError("Actor context must be set before starting.")

        logger.info(f"Actor {self.path} starting...")
        await self.pre_start()  # Call lifecycle hook
        self._running = True
        self._task = asyncio.create_task(self._run(), name=f"Actor-{self.path}")
        logger.info(f"Actor {self.path} started.")

    async def stop(self):
        """Stops the actor's message processing loop gracefully."""
        if not self._running or not self._task:
            return
        logger.info(f"Actor {self.path} stopping...")
        self._running = False
        await self._mailbox.put(None)  # Sentinel to exit loop
        try:
            # Wait for the task to finish processing the current message + sentinel
            await asyncio.wait_for(self._task, timeout=5.0)
            logger.info(f"Actor {self.path} stopped gracefully.")
        except asyncio.TimeoutError:
            logger.warning(f"Actor {self.path} stop timed out. Task will be cancelled.")
            if self._task and not self._task.done():
                self._task.cancel()
        except asyncio.CancelledError:
            logger.warning(f"Actor {self.path} stop cancelled during shutdown.")
        except Exception as e:
            logger.exception(f"Actor {self.path} error during stop: {e}")
        finally:
            self._task = None
            try:
                await self.post_stop()  # Call lifecycle hook
                # Notify watchers via ActorSystem after post_stop completes
                await self.context.actor_system.notify_termination(self.self_ref)
            except Exception as post_stop_err:
                logger.exception(
                    f"Actor {self.path} error during post_stop or termination notification: {post_stop_err}"
                )

    # --- Internal Methods ---

    async def _enqueue_message(self, message: StandardActorMessage):
        """Internal method used by ActorSystem to add a message to the mailbox."""
        if not self._running:
            logger.warning(
                f"Attempted to send message to stopped actor {self.path}: {message.type}"
            )
            # Optionally raise an error or return a status
            return
        await self._mailbox.put(message)

    async def _run(self):
        """The main message processing loop. Delegates errors to supervisor."""
        while self._running:
            message: Optional[StandardActorMessage] = None
            try:
                message = await self._mailbox.get()
                if message is None:  # Sentinel check
                    logger.debug(f"Actor {self.path} received stop sentinel.")
                    break  # Exit loop

                # Store message context for access during receive
                self._message_context = message.context

                # REMOVED: Reply Routing Hook is now handled by ActorSystem.deliver_message

                # Proceed with normal receive handling
                await self.receive(message)
                self._mailbox.task_done()

            except asyncio.CancelledError:
                logger.info(f"Actor {self.path} run loop cancelled.")
                self._running = False  # Ensure loop terminates
                # Notify watchers via ActorSystem during stop()
            except Exception as e:
                # --- Supervision Handling ---
                logger.exception(
                    f"Actor {self.path} encountered error processing message ({getattr(message, 'type', 'UNKNOWN')}). Delegating to supervisor."
                )
                try:
                    # Delegate to the ActorSystem to handle the failure based on strategy
                    should_continue = (
                        await self.context.actor_system.handle_actor_failure(
                            self.self_ref, e, message
                        )
                    )
                    if not should_continue:
                        self._running = False  # Supervisor decided to stop
                        break  # Exit run loop
                    else:
                        # Supervisor decided to resume or restart was handled
                        logger.info(
                            f"Actor {self.path} resuming after supervised error."
                        )
                except Exception as supervisor_err:
                    # Catastrophic failure in supervision itself
                    logger.critical(
                        f"CRITICAL: Error during supervision handling for actor {self.path}: {supervisor_err}",
                        exc_info=True,
                    )
                    self._running = False  # Stop actor if supervision fails
                    break
                # --- End Supervision Handling ---

            finally:
                # Clear context after message handling (or error/cancellation)
                self._message_context = None

        logger.debug(f"Actor {self.path} run loop finished.")

    # --- Abstract Method (to be implemented by subclasses) ---

    @abstractmethod
    async def receive(self, message: StandardActorMessage) -> None:
        """
        Process an incoming message. Must be implemented by concrete actor subclasses.

        Args:
            message: The message envelope containing payload, sender, context, etc.
        """
        raise NotImplementedError
