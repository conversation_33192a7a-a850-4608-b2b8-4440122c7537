# -*- coding: utf-8 -*-
"""
Core Actor System Implementation (Asyncio-based).

Manages the lifecycle and communication of actors, integrating with the
ChoreographyEngine and utilizing the base Actor classes.
Includes Death Watch and Supervision capabilities.

CAW Alignment:
- Uses ActorPath for hierarchical naming.
- Passes Context within StandardActorMessage.
- Integrates with ChoreographyEngine via ActorSystemInterface and reply routing.
- Leverages base Actor class with integrated CAW features.

Related Files:
- person_suit.core.actors.base.py (Defines Actor, ActorRef, ActorPath, etc.)
- person_suit.core.actors.choreography.py (Defines ActorSystemInterface)
- person_suit.core.actors.actor_system_messaging.py (Handles messaging)
- person_suit.core.actors.actor_system_watch.py (<PERSON><PERSON> death watch)
- person_suit.core.actors.actor_system_supervision.py (Handles supervision)
- person_suit.core.actors.supervision.py (Defines supervision strategies)
- person_suit.core.caw.schemas.py (Defines Context)
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional, Tuple, Type

# Import manager modules (new)
from .actor_system_messaging import MessagingManager
from .actor_system_supervision import SupervisionManager
from .actor_system_watch import DeathWatchManager

# Base classes for the actor system
from .base import (
    Actor,
    ActorContext,
    ActorPath,
    ActorRef,
    StandardActorMessage,
)

# Import the interface and ActorRef protocol from choreography
from .choreography import ActorID, ActorSystemInterface
from .choreography import ActorRef as ActorRefProtocol

# Import system messages
from .messages import (
    GrantCapabilitiesReply,
    RequestInitialCapabilities,
)

# Import supervision components
from .supervision import (
    DefaultSupervisor,
    SupervisionStrategyImplementation,
)

# Import Context for message envelope
from ..caw.schemas import Context

# Import capability components (adjust path as needed)
from ..security.capabilities import (
    CapabilityScope,
    CapabilityToken,
    Permission,
    verify_capability,
)

logger = logging.getLogger(__name__)


class ActorSystem(ActorSystemInterface):  # Implement the protocol
    """Manages the lifecycle and communication of actors, including supervision and death watch."""

    def __init__(
        self, system_name: str = "default_system", fail_on_capability_error: bool = True
    ):
        """
        Initialize the Actor System.

        Args:
            system_name: Name of the actor system
            fail_on_capability_error: Whether to fail actor creation if capability request fails
        """
        self.system_name = system_name
        self._actors: Dict[ActorPath, Actor] = {}
        self._running = False
        self._root_actor_path = ActorPath(system_name=system_name, path_elements=())
        # Default supervision strategy for the system
        self._default_supervisor = DefaultSupervisor()  # Or load from config
        # Capability Service Reference - Needs to be set after init or passed in
        self._capability_service_ref: Optional[ActorRef] = None
        # Add policy flag
        self._fail_on_capability_error = fail_on_capability_error

        # Initialize the managers
        self._messaging_manager = MessagingManager(self)
        self._watch_manager = DeathWatchManager(self)
        self._supervision_manager = SupervisionManager(self)

        logging.info(
            f"ActorSystem '{self.system_name}' initialized. Fail on capability error: {self._fail_on_capability_error}"
        )

    # Method to set the capability service reference after system start
    def set_capability_service_ref(self, ref: ActorRef):
        """
        Set the reference to the CapabilityServiceActor.

        Args:
            ref: Reference to the capability service actor
        """
        if self._capability_service_ref is not None:
            logger.warning("CapabilityServiceRef already set.")
            return
        # TODO: Validate the type/path of the ref?
        self._capability_service_ref = ref
        logger.info(f"CapabilityServiceRef set to: {ref.path}")

    async def start(self):
        """Start the actor system."""
        if self._running:
            return
        self._running = True
        logging.info(f"ActorSystem '{self.system_name}' started.")

    async def stop(self):
        """Stop all actors managed by the system gracefully."""
        if not self._running:
            return
        self._running = False
        logging.info(
            f"ActorSystem '{self.system_name}' stopping... stopping {len(self._actors)} actors."
        )
        # Stop actors concurrently - consider stopping children before parents later
        stop_tasks = [actor.stop() for actor in self._actors.values()]
        results = await asyncio.gather(*stop_tasks, return_exceptions=True)
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                actor_path = list(self._actors.keys())[i]  # Get corresponding path
                logger.error(f"Error stopping actor {actor_path}: {result}")

        self._actors.clear()  # Clear registry after stopping
        self._watch_manager.clear()  # Clear watch relationships
        self._messaging_manager.clear()  # Clear ask futures

        logging.info(f"ActorSystem '{self.system_name}' stopped.")

    async def create_actor(
        self,
        actor_class: Type[Actor],
        name: str,
        parent: Optional[ActorRef] = None,
        supervision_strategy: Optional[SupervisionStrategyImplementation] = None,
        args: Tuple = (),
        kwargs: Optional[Dict[str, Any]] = None,
        invoker_token: Optional[CapabilityToken] = None,
    ) -> ActorRef:
        """
        Create, register, start, and potentially supervise an actor instance.
        Requires CREATE_CHILD capability scoped to the parent.

        Args:
            actor_class: The class of the actor to create (must subclass Actor).
            name: A unique name for the actor within its parent.
            parent: Optional reference to the parent actor. If None, created as root child.
            supervision_strategy: Specific strategy for this actor. If None, uses parent's or system default.
            args: Positional arguments for the actor's constructor.
            kwargs: Keyword arguments for the actor's constructor.
            invoker_token: Optional capability token for permission check.

        Returns:
            An ActorRef for the newly created actor.

        Raises:
            ValueError: If the name is invalid or already exists under the parent.
            RuntimeError: If the system is not running or instantiation/start fails.
            PermissionError: If invoker lacks required capability.
        """
        if not self._running:
            raise RuntimeError(f"ActorSystem '{self.system_name}' is not running.")

        kwargs = kwargs or {}
        parent_actor: Optional[Actor] = None
        if parent:
            parent_actor = self._actors.get(parent.path)
            if not parent_actor:
                # This check prevents creating orphans with invalid parent refs
                raise ValueError(
                    f"Parent actor {parent.path} specified for child '{name}' does not exist."
                )
            parent_path = parent.path
            # Inherit supervisor strategy if not specified
            if supervision_strategy is None:
                supervision_strategy = parent_actor.context.supervision_strategy
        else:
            parent_path = self._root_actor_path

        # --- Capability Check --- #
        required_permission = Permission.CREATE_CHILD
        required_scope = CapabilityScope.ACTOR(str(parent_path))  # Scope to parent path
        if not verify_capability(invoker_token, required_permission, required_scope):
            raise PermissionError(
                f"Invoker lacks capability {required_permission.name} in scope {required_scope}"
            )
        # --- End Capability Check --- #

        # Use system default strategy if still None
        if supervision_strategy is None:
            supervision_strategy = self._default_supervisor

        try:
            actor_path = parent_path.child(name)
        except ValueError as e:
            raise ValueError(f"Invalid actor name '{name}'.") from e

        if actor_path in self._actors:
            raise ValueError(f"Actor with path '{actor_path}' already exists.")

        # Instantiate the actor
        try:
            actor_instance = actor_class(*args, **kwargs)
            if not isinstance(actor_instance, Actor):
                raise TypeError(
                    f"Class {actor_class.__name__} does not inherit from Actor."
                )
        except Exception as e:
            logger.exception(
                f"Failed to instantiate actor {actor_path} of type {actor_class.__name__}"
            )
            raise RuntimeError(f"Actor instantiation failed for {actor_path}") from e

        # Create the ActorRef and ActorContext
        actor_ref = ActorRef(path=actor_path, _system_ref=self)
        actor_context = ActorContext(
            self_ref=actor_ref,
            parent=parent,
            actor_system=self,
            supervision_strategy=supervision_strategy,
            creation_args=args,
            creation_kwargs=kwargs,
            # capabilities list is initially empty
        )

        actor_instance._context = actor_context  # Assign context

        # --- Request Initial Capabilities --- #
        initial_capabilities: List[Capability] = []
        capability_request_failed = False
        capability_error_details = ""

        if self._capability_service_ref:
            try:
                logger.debug(f"Requesting initial capabilities for {actor_path}...")
                request_msg = RequestInitialCapabilities(
                    requester_ref=actor_ref,  # The new actor is the subject
                    actor_path=actor_path,
                    actor_class_name=actor_class.__name__,
                    parent_ref=parent,
                    creator_token=invoker_token,  # Pass the token used for creation
                )
                # Use ask to get the reply
                reply = await self._capability_service_ref.ask(
                    message_type="RequestInitialCapabilities",
                    payload=request_msg,
                    timeout=10.0,  # Add a timeout
                    # No capability needed to ASK the service? Or use system cap?
                    # For now, assume ask doesn't need specific token to service.
                )

                if isinstance(reply, GrantCapabilitiesReply) and reply.success:
                    initial_capabilities = reply.granted_capabilities
                    actor_context.capabilities = (
                        initial_capabilities  # Store in context
                    )
                    logger.info(
                        f"Received {len(initial_capabilities)} initial capabilities for {actor_path}."
                    )
                else:
                    error_msg = (
                        reply.error
                        if isinstance(reply, GrantCapabilitiesReply)
                        else "Invalid reply"
                    )
                    capability_request_failed = True
                    capability_error_details = (
                        f"Failed to grant initial capabilities: {error_msg}"
                    )
                    logger.error(f"{capability_error_details} for {actor_path}")

            except asyncio.TimeoutError:
                capability_request_failed = True
                capability_error_details = "Timeout requesting initial capabilities"
                logger.error(
                    f"{capability_error_details} for {actor_path}. Failing creation."
                )
            except Exception as cap_err:
                capability_request_failed = True
                capability_error_details = (
                    f"Error getting initial capabilities: {cap_err}"
                )
                logger.exception(
                    f"Error requesting initial capabilities for {actor_path}: {cap_err}"
                )
        else:
            capability_request_failed = (
                True  # Treat missing service as failure for policy check
            )
            capability_error_details = "CapabilityService not available"
            logger.warning(
                f"{capability_error_details}. Actor {actor_path} created without initial capabilities."
            )

        # Check policy if request failed
        if capability_request_failed and self._fail_on_capability_error:
            await self._cleanup_failed_start(actor_path, parent_actor, actor_ref)
            raise RuntimeError(capability_error_details)
        elif capability_request_failed:  # Policy allows creation, just log warning
            logger.warning(
                f"Proceeding with actor {actor_path} creation despite capability issue: {capability_error_details}"
            )
        # --- End Request Initial Capabilities --- #

        # Register and start (only proceeds if policy allows or if capability request succeeded)
        self._actors[actor_path] = actor_instance
        if parent and parent_actor:
            parent_actor.context.children.add(actor_ref)

        try:
            await actor_instance.start()
            logger.info(f"Successfully created and started actor: {actor_path}")
            return actor_ref
        except Exception as start_err:
            logger.exception(
                f"Failed to start actor {actor_path} after getting capabilities."
            )
            await self._cleanup_failed_start(actor_path, parent_actor, actor_ref)
            raise RuntimeError(f"Actor start failed for {actor_path}") from start_err

    async def _cleanup_failed_start(
        self, actor_path: ActorPath, parent_actor: Optional[Actor], actor_ref: ActorRef
    ):
        """
        Helper to clean up registration if actor start fails.

        Args:
            actor_path: Path of the actor that failed to start
            parent_actor: Parent of the actor that failed to start
            actor_ref: Reference to the actor that failed to start
        """
        self._actors.pop(actor_path, None)
        if parent_actor:
            parent_actor.context.children.discard(actor_ref)
        logger.debug(f"Cleaned up registration for failed actor start: {actor_path}")

    # --- Interface Methods (from ActorSystemInterface) ---

    async def get_actor_ref(self, actor_id: ActorID) -> Optional[ActorRefProtocol]:
        """
        Retrieve an ActorRef for a given ActorID (interpreted as path string).
        Satisfies the ActorSystemInterface.

        Args:
            actor_id: Actor ID (path string)

        Returns:
            Actor reference if found, None otherwise
        """
        try:
            # Assuming ActorID from the interface is a string path relative to root
            path_str = actor_id.strip("/")
            path = ActorPath(
                system_name=self.system_name,
                path_elements=tuple(path_str.split("/")) if path_str else tuple(),
            )

            if path in self._actors:
                # Return the concrete ActorRef, assuming it matches the protocol
                return ActorRef(path=path, _system_ref=self)
            else:
                return None
        except Exception as e:  # Catch potential errors during parsing/lookup
            logger.warning(
                f"Error resolving ActorID '{actor_id}' in get_actor_ref: {e}"
            )
            return None

    # --- Delegated Methods (to manager classes) ---

    async def send(self, target_id: ActorID, message: Any):
        """
        Send a message to a target actor identified by ActorID (path string).
        Delegates to MessagingManager.

        Args:
            target_id: Target actor ID (path)
            message: Message to send
        """
        await self._messaging_manager.send(target_id, message)

    async def deliver_message(
        self, target_path: ActorPath, message: StandardActorMessage
    ) -> bool:
        """
        Deliver a message, handling ask replies first.
        Delegates to MessagingManager.

        Args:
            target_path: Path of the target actor
            message: Message to deliver

        Returns:
            True if delivered successfully, False otherwise
        """
        return await self._messaging_manager.deliver_message(target_path, message)

    async def ask(
        self,
        target_ref: ActorRef,
        message_type: str,
        payload: Any,
        context: Optional[Context] = None,
        capability_token: Optional[CapabilityToken] = None,
        timeout: Optional[float] = 5.0,
    ) -> Any:
        """
        Handle the ask pattern, sending a message and awaiting a future.
        Delegates to MessagingManager.

        Args:
            target_ref: Reference to the target actor
            message_type: Type of message to send
            payload: Message payload
            context: Optional message context
            capability_token: Optional capability token
            timeout: Timeout in seconds for the request

        Returns:
            The reply payload
        """
        return await self._messaging_manager.ask(
            target_ref, message_type, payload, context, capability_token, timeout
        )

    def resolve_ask_future(self, correlation_id: str, reply_payload: Any) -> bool:
        """
        Resolve a pending ask future.
        Delegates to MessagingManager.

        Args:
            correlation_id: Correlation ID of the request
            reply_payload: Payload of the reply

        Returns:
            True if resolved successfully, False otherwise
        """
        return self._messaging_manager.resolve_ask_future(correlation_id, reply_payload)

    async def watch(
        self,
        watcher_ref: ActorRef,
        target_ref: ActorRef,
        invoker_token: Optional[CapabilityToken] = None,
    ):
        """
        Register watcher_ref to receive Terminated when target_ref stops.
        Delegates to DeathWatchManager.

        Args:
            watcher_ref: Actor that will watch
            target_ref: Actor to be watched
            invoker_token: Capability token for permission check
        """
        await self._watch_manager.watch(watcher_ref, target_ref, invoker_token)

    async def unwatch(
        self,
        watcher_ref: ActorRef,
        target_ref: ActorRef,
        invoker_token: Optional[CapabilityToken] = None,
    ):
        """
        Unregister watcher_ref from watching target_ref.
        Delegates to DeathWatchManager.

        Args:
            watcher_ref: Actor that will stop watching
            target_ref: Actor that is being watched
            invoker_token: Capability token for permission check
        """
        await self._watch_manager.unwatch(watcher_ref, target_ref, invoker_token)

    async def notify_termination(self, terminated_ref: ActorRef):
        """
        Notify watchers and cleanup watches involving the terminated actor.
        Delegates to DeathWatchManager.

        Args:
            terminated_ref: Actor that has terminated
        """
        await self._watch_manager.notify_termination(terminated_ref)

    async def handle_actor_failure(
        self,
        failed_actor_ref: ActorRef,
        exception: Exception,
        message: Optional[StandardActorMessage],
    ) -> bool:
        """
        Handle an exception from an actor according to supervision strategy.
        Delegates to SupervisionManager.

        Args:
            failed_actor_ref: Reference to the actor that failed
            exception: The exception that occurred
            message: The message being processed when the failure occurred

        Returns:
            True if the actor should continue, False otherwise
        """
        return await self._supervision_manager.handle_actor_failure(
            failed_actor_ref, exception, message
        )

    async def stop_actor(
        self, actor_ref: ActorRef, invoker_token: Optional[CapabilityToken] = None
    ):
        """
        Stop a single actor and clean up its resources and watches.
        Delegates to SupervisionManager.

        Args:
            actor_ref: Reference to the actor to stop
            invoker_token: Capability token for permission check
        """
        await self._supervision_manager.stop_actor(actor_ref, invoker_token)

    async def restart_actor(
        self,
        actor_ref: ActorRef,
        reason: Exception,
        invoker_token: Optional[CapabilityToken] = None,
    ):
        """
        Restart a failed actor.
        Delegates to SupervisionManager.

        Args:
            actor_ref: Reference to the actor to restart
            reason: The reason for restarting the actor
            invoker_token: Capability token for permission check
        """
        await self._supervision_manager.restart_actor(actor_ref, reason, invoker_token)
