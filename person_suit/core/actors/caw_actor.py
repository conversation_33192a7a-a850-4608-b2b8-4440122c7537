"""person_suit.core.actors.caw_actor
=================================================

CAWActor Base Class
-------------------
This module introduces `CAWActor`, a thin wrapper around the existing
`person_suit.core.actors.base.Actor` that adds:

1. DualContext awareness – every actor instance is created with a local
   `DualContext`, and every incoming message envelope may carry its own
   context.  When a message arrives, the actor **composes** the local and
   message contexts to obtain a *processing context*.
2. Adaptive Computational Fidelity (ACF) integration – actors are
   initialised with (or automatically create) an `ACFManager` instance.
   Before delegating work to the concrete implementation they call
   `ACFManager.determine_fidelity()` to obtain a fidelity value for the
   current *operation* (in this case derived from the message type).
3. A simplified hook (`receive_with_context`) that concrete subclasses
   implement instead of the full `receive`.  This hook already receives the
   composed context and computed fidelity so business logic can focus on
   *what* to do rather than *how* to adapt.

The goal is to make CAW-aware actor implementations trivial while keeping the
existing rich actor infrastructure intact.

Related:
- person_suit/core/caw/context.py (DualContext)
- person_suit/core/caw/acf.py (ACFManager)
- person_suit/core/actors/base.py (Actor, StandardActorMessage)
"""

from __future__ import annotations

import logging
from abc import ABC, abstractmethod
from typing import Optional

from person_suit.core.actors.base import Actor, StandardActorMessage
from person_suit.core.context import UnifiedContext as DualContext
from person_suit.core.caw.acf import ACFManager, AdaptationStrategy

logger = logging.getLogger(__name__)


class CAWActor(Actor, ABC):
    """Context- and ACF-aware actor base class (CAW Principle #4).

    Subclasses should implement :py:meth:`receive_with_context` instead of
    overriding :py:meth:`receive` directly.
    """

    def __init__(
        self,
        local_context: DualContext,
        acf_manager: Optional[ACFManager] = None,
        *,
        adaptation_strategy: AdaptationStrategy = AdaptationStrategy.BALANCED,
    ) -> None:
        super().__init__()
        self._local_context: DualContext = local_context
        self._acf: ACFManager = acf_manager or ACFManager(
            adaptation_strategy=adaptation_strategy
        )

    # ---------------------------------------------------------------------
    # Actor.receive override – composes context & computes fidelity
    # ---------------------------------------------------------------------
    async def receive(self, message: StandardActorMessage) -> None:  # noqa: D401
        """Internal receive wrapper.

        1. Compose the actor's own context with the message context
           (if present).
        2. Ask the ACF manager for a fidelity value.
        3. Delegate to :py:meth:`receive_with_context` implemented by the
           concrete subclass.
        """

        # 1. Context composition
        msg_ctx: Optional[DualContext] = (
            message.context if isinstance(message.context, DualContext) else None
        )
        processing_ctx: DualContext = (
            self._local_context.compose(msg_ctx) if msg_ctx else self._local_context
        )

        # 2. Fidelity determination (use message type as operation key)
        try:
            fidelity: float = self._acf.determine_fidelity(
                processing_ctx, operation=message.type
            )
        except Exception as err:  # Defensive – never fail the actor loop
            logger.error(
                "ACFManager.determine_fidelity failed for %s: %s", message.type, err
            )
            fidelity = 0.8  # Reasonable default

        # 3. Delegate to subclass implementation
        try:
            await self.receive_with_context(message, processing_ctx, fidelity)
        except Exception as exc:  # Let supervision strategies handle it
            logger.exception(
                "Unhandled exception in receive_with_context %s: %s", self.path, exc
            )
            raise  # Re-raise so Actor._run delegates to supervision logic

    # ------------------------------------------------------------------
    # Subclass contract
    # ------------------------------------------------------------------
    @abstractmethod
    async def receive_with_context(
        self,
        message: StandardActorMessage,
        context: DualContext,
        fidelity: float,
    ) -> None:
        """Handle a message given composed context & ACF-computed fidelity.""" 