# -*- coding: utf-8 -*-
"""
Implementation of the Central State Actor for managing DualInformation state.

This actor acts as the serialization point for state modifications for a
specific entity, ensuring consistent updates in a concurrent environment.
It validates requests, applies effects using StateTransformationLogic,
updates its internal state reference, coordinates logging, and notifies
listeners.

Related Files:
- docs/design/Central_State_Actor_Design.md
- docs/CAW_REPRESENTATION_PLAN.md
- schemas.python_schema.py
- person_suit.core.caw.dual_information.py
- person_suit.core.actors (This actor resides here)
- Interfaces for dependencies (CapabilityValidator, EventLogService, etc.)
"""

import asyncio
import logging
import time
import uuid
from collections import defaultdict
from typing import Dict, Optional, Union

# --- Actor Framework --- #
# Import the concrete ActorBase
from .base import (
    Actor,
    ActorRef,
    StandardActorMessage,
)
from ..caw.dual_information import (
    DualInformation,
)
from ..caw.particle_state import ConcreteParticleState

# --- Schema Imports --- #
from ..caw.schemas import (
    BaseEffect,
    Capability,
    Context,
    EventID,
    RejectionErrorType,
    StateChangeNotification,
    StateRef,
    StateUpdateAcceptedReply,
    StateUpdateEffectRequest,
    StateUpdateRejectedReply,
)

# --- Dependency Interfaces (Placeholders - Defined below for now) --- #
# In a real setup, these would be imported from their respective modules


class CapabilityValidatorInterface:  # Interface definition
    async def validate(
        self,
        capability: Capability,
        effect: BaseEffect,
        current_state: DualInformation,
        context: Context,
    ) -> bool:
        raise NotImplementedError


class EventLogServiceInterface:  # Interface definition
    async def log_event(
        self,
        event_id: EventID,
        effect: BaseEffect,
        context: Context,
        previous_state_ref: Optional[StateRef],
        resulting_state_ref: StateRef,
        entity_id: str,
        timestamp: float,
    ) -> bool:
        raise NotImplementedError


class NotificationServiceInterface:  # Interface definition
    async def publish(self, notification: StateChangeNotification):
        raise NotImplementedError


class StateTransformationLogicInterface:  # Interface definition
    async def check_preconditions(
        self, effect: BaseEffect, current_state: DualInformation, context: Context
    ) -> bool:
        raise NotImplementedError

    async def apply_effect(
        self, effect: BaseEffect, current_state: DualInformation, context: Context
    ) -> DualInformation:
        raise NotImplementedError


class StateStorageServiceInterface:  # Interface definition
    async def load_state(self, storage_location: str) -> Optional[DualInformation]:
        """Loads a specific state version by its reference/location."""
        raise NotImplementedError

    async def store_snapshot(self, state: DualInformation) -> bool:
        """Stores a complete state snapshot."""
        raise NotImplementedError

    async def load_latest_snapshot(self, entity_id: str) -> Optional[DualInformation]:
        """Loads the most recent snapshot available for a given entity."""
        raise NotImplementedError

    # Optional: Delete method might be part of interface too
    # async def delete_snapshot(self, storage_location: str) -> bool:
    #     raise NotImplementedError


# --- Central State Actor Implementation --- #

# REMOVED: from ..interfaces.notification import NotificationServiceInterface # Redundant/Handled by DI
# REMOVED: from ..caw.schemas import EffectCategory, LoggedEvent, StateChangeNotification # Redundant
# Import EffectDispatcher
from ..effects.dispatcher import EffectDispatcher, EffectDispatchError
from ..effects.schemas import (  # Ensure schemas are imported
    BaseEffect,
    RejectionErrorType,
    StateUpdateAcceptedReply,
    StateUpdateEffectRequest,
    StateUpdateRejectedReply,
)
from ..security.capabilities.types import (  # Import capability types
    Capability,
    CapabilityScope,
    Permission,
)
from ..security.capabilities.validator import (  # Import validator function
    CapabilityValidator,
)

# Setup logger
logger = logging.getLogger(__name__)


# Custom Exception for Load Failures
class StateLoadError(Exception):
    pass


class CentralStateActor(Actor):  # Corrected base class
    """
    Manages and updates the DualInformation state for multiple entities.
    Uses an injected EffectDispatcher to handle effect processing based on category.
    """

    def __init__(
        self,
        state_storage: StateStorageServiceInterface,
        event_log: EventLogServiceInterface,
        notification_service: NotificationServiceInterface,
        effect_dispatcher: EffectDispatcher,
        capability_validator: Union[CapabilityValidatorInterface, CapabilityValidator],
    ):
        super().__init__()  # Call base Actor init
        self._entity_states: Dict[str, DualInformation] = {}
        self._entity_locks: Dict[str, asyncio.Lock] = defaultdict(asyncio.Lock)
        self._entity_load_tasks: Dict[str, asyncio.Future] = {}

        # Dependencies (Assume they are injected/set after init or via context)
        self._state_storage = state_storage
        self._event_log = event_log
        self._notification_service = notification_service
        self._capability_validator = capability_validator
        self._effect_dispatcher = effect_dispatcher

        # Logger will use self.path once context is available
        # logging.info(f"CentralStateActor {self.path} initialized...") # Defer this log

    # Override pre_start to log after context is set
    async def pre_start(self):
        await super().pre_start()
        logging.info(
            f"CentralStateActor {self.path} initialized with EffectDispatcher."
        )

    async def receive(self, message: StandardActorMessage):
        """Handles incoming messages, primarily StateUpdateEffectRequest."""
        if not isinstance(message, StandardActorMessage):
            logging.warning(
                f"{self.path} received non-standard message format: {type(message)}. Skipping."
            )
            return

        if message.type == "STATE_UPDATE_EFFECT_REQUEST":
            request: Optional[StateUpdateEffectRequest] = message.payload
            sender_ref: Optional[ActorRef] = message.sender_ref  # Use sender_ref
            # Extract capability token from the standard message
            cap_token: Optional[Capability] = message.capability_token

            if not isinstance(request, StateUpdateEffectRequest) or not sender_ref:
                logging.error(
                    f"{self.path} received invalid STATE_UPDATE_EFFECT_REQUEST payload or missing sender_ref: {message}"
                )
                # Cannot reply if sender_ref is missing
                return

            # Pass the sender_ref and token to the handler
            await self._handle_state_update_request(
                request, sender_ref, message.context, cap_token
            )

        elif message.type == "STATE_READ_REQUEST":  # Added handler for read
            request: Optional[Dict] = message.payload  # Define request format
            sender_ref: Optional[ActorRef] = message.sender_ref
            cap_token: Optional[Capability] = message.capability_token
            if not isinstance(request, dict) or not sender_ref:
                logging.error(
                    f"{self.path} received invalid STATE_READ_REQUEST payload or missing sender_ref."
                )
                return
            await self._handle_state_read_request(
                request, sender_ref, message.context, cap_token
            )
        else:
            logging.warning(
                f"{self.path} received unhandled message type '{message.type}'"
            )

    async def _ensure_entity_state_loaded(
        self, entity_id: str
    ) -> Optional[DualInformation]:
        """
        Ensures the state for the entity is loaded, handling concurrent requests.
        Attempts to load from storage, creates initial state if not found.

        Returns:
            The loaded or created DualInformation state, or None if loading failed critically.
        """
        # Fast path: already loaded
        if entity_id in self._entity_states:
            return self._entity_states[entity_id]

        # Check if another task is already loading this entity
        if entity_id in self._entity_load_tasks:
            logging.debug(f"Waiting for existing load task for entity {entity_id}")
            try:
                await asyncio.wait_for(
                    self._entity_load_tasks[entity_id], timeout=30.0
                )  # Add timeout
                # Task finished, state should now be in _entity_states (or load failed)
                return self._entity_states.get(entity_id)
            except asyncio.TimeoutError:
                logging.error(
                    f"Timeout waiting for state load task for entity {entity_id}"
                )
                # Clean up potentially stuck future?
                self._entity_load_tasks.pop(entity_id, None)
                return None  # Indicate failure
            except Exception as e:
                logging.exception(
                    f"Error waiting on state load task for {entity_id}: {e}"
                )
                self._entity_load_tasks.pop(entity_id, None)
                return None  # Indicate failure

        # No state and no load task running, initiate loading
        loop = asyncio.get_running_loop()
        load_future = loop.create_future()
        self._entity_load_tasks[entity_id] = load_future
        logging.info(f"Initiating state load for entity {entity_id}")

        loaded_state: Optional[DualInformation] = None
        try:
            # Acquire lock specifically for loading this entity's initial state
            async with self._entity_locks[entity_id]:
                # Double check if loaded by another task between future creation and lock acquisition
                if entity_id in self._entity_states:
                    loaded_state = self._entity_states[entity_id]
                    logging.debug(
                        f"State for {entity_id} was loaded concurrently, using it."
                    )
                else:
                    # --- Attempt to load latest state --- #
                    try:
                        logging.debug(
                            f"Attempting to load latest snapshot for {entity_id} from storage..."
                        )
                        # Replace placeholder with actual call
                        loaded_state = await self._state_storage.load_latest_snapshot(
                            entity_id
                        )

                        if loaded_state:
                            logging.info(
                                f"Successfully loaded state {loaded_state.state_ref} for entity {entity_id} from storage."
                            )
                        else:
                            logging.warning(
                                f"No existing state found for entity {entity_id} in storage. Creating initial state."
                            )
                            loaded_state = DualInformation.create_initial(
                                initial_particle_state=ConcreteParticleState.create_empty(),
                                initial_wave_state=None,
                            )
                            # Optional: Persist this newly created initial state?
                            # Consider if this should happen automatically or require an explicit action
                            # try:
                            #     await self._state_storage.save_snapshot(loaded_state)
                            # except Exception as save_err:
                            #     logging.error(f"Failed to save newly created initial state for {entity_id}: {save_err}")

                        # Store the loaded/created state in memory
                        self._entity_states[entity_id] = loaded_state

                    except Exception as load_err:
                        logging.exception(
                            f"Error loading state from storage for {entity_id}: {load_err}"
                        )
                        loaded_state = None  # Ensure state indicates failure

            # Set future result outside the lock
            if loaded_state:
                load_future.set_result(True)
            else:
                load_future.set_exception(
                    RuntimeError(
                        f"Failed to load or create initial state for {entity_id}"
                    )
                )

        except Exception as outer_err:
            # Error acquiring lock or setting future result
            logging.exception(
                f"Unexpected error during state load initiation for {entity_id}: {outer_err}"
            )
            if not load_future.done():
                load_future.set_exception(outer_err)
            loaded_state = None  # Ensure failure indicated
        finally:
            # Remove the load task future once done (success or failure)
            self._entity_load_tasks.pop(entity_id, None)

        return loaded_state

    # --- Main Request Handler (_handle_state_update_request) ---

    async def _handle_state_update_request(
        self,
        request: StateUpdateEffectRequest,
        sender_ref: ActorRef,  # Changed from reply_to_actor_id
        envelope_context: Optional[Context],
        cap_token: Optional[Capability],  # Receive the token
    ):
        """Handles the logic for a single state update request, including capability check."""
        entity_id = request.entity_id
        effect = request.effect
        event_id = EventID(str(uuid.uuid4()))  # Generate event ID early
        timestamp = time.time()

        # --- Pre-load state for capability check --- #
        try:
            pre_check_state = await self._ensure_entity_state_loaded(entity_id)
        except StateLoadError as load_err:
            # If critical load fails even before check, reject request
            logging.error(f"[{entity_id}] Pre-check state load failed: {load_err}")
            reply = StateUpdateRejectedReply(
                request_id=request.request_id,
                entity_id=entity_id,
                event_id=event_id,
                error_type=RejectionErrorType.STATE_LOAD_FAILED,
                error_message=f"Initial state load failed: {load_err}",
                timestamp=timestamp,
            )
            await self._send_reply(sender_ref, reply)
            return

        # --- 1. Capability Check --- #
        required_permission = Permission.APPLY_EFFECT  # Or map from effect.effect_type
        required_scope = CapabilityScope.ENTITY_STATE(entity_id)
        is_valid = False
        validation_error = "Permission denied (Unknown reason)"
        try:
            logger.debug(
                f"[{entity_id}] Checking capability {getattr(cap_token, 'capability_id', 'None')} for permission {required_permission.name} on scope {required_scope}"
            )
            # We need the current state for potential constraint checks
            # This introduces a challenge: we might need to load state *before* the final check
            # Option A: Load state first (potentially unnecessary work if caps fail)
            # Option B: Do a preliminary check without state, then final check after load?
            # Let's try Option A for simplicity, loading state before the main lock.
            # Note: This load is just for the check, the main load happens under lock.
            pre_check_state = await self._ensure_entity_state_loaded(entity_id)
            if pre_check_state is None and entity_id in self._entity_states:
                # If loading failed, but state exists in memory (e.g. from prev op), use that for check
                pre_check_state = self._entity_states.get(entity_id)

            is_valid = self._capability_validator.validate(
                capability=cap_token,
                required_permission=required_permission,
                required_scope=required_scope,
                context=envelope_context,
                effect=effect,
                current_state=pre_check_state,  # Pass potentially loaded state
            )
            if not is_valid:
                validation_error = "Permission denied by capability validator."
        except Exception as cap_err:  # Catch potential errors during validation itself
            logger.error(
                f"[{entity_id}] Error during capability validation: {cap_err}",
                exc_info=True,
            )
            is_valid = False
            validation_error = f"Capability validation error: {cap_err}"

        if not is_valid:
            logger.warning(
                f"[{entity_id}] Capability check failed for {sender_ref} requesting {effect.effect_type}. Reason: {validation_error}"
            )
            reply = StateUpdateRejectedReply(
                request_id=request.request_id,
                entity_id=entity_id,
                event_id=event_id,
                error_type=RejectionErrorType.PERMISSION_DENIED,
                error_message=validation_error,
                timestamp=timestamp,
            )
            await self._send_reply(sender_ref, reply)
            return  # Stop processing
        # --- End Capability Check --- #

        logger.debug(
            f"[{entity_id}] Capability check passed for {effect.effect_type}. Proceeding with state update."
        )

        # --- 2. Acquire Entity Lock & Ensure State Loaded --- #
        async with self._entity_locks[entity_id]:
            current_state = await self._ensure_entity_state_loaded(entity_id)
            if current_state is None:
                logger.error(
                    f"[{entity_id}] Failed to load or initialize state inside lock."
                )
                reply = StateUpdateRejectedReply(
                    request_id=request.request_id,
                    entity_id=entity_id,
                    event_id=event_id,
                    error_type=RejectionErrorType.STATE_LOAD_FAILED,
                    error_message="Failed to load current state for update.",
                    timestamp=timestamp,
                )
                await self._send_reply(sender_ref, reply)
                return

            previous_state_ref = current_state.state_ref

            # --- 3. Apply Effect using Dispatcher --- #
            try:
                logger.debug(
                    f"[{entity_id}] Dispatching effect {effect.effect_type}..."
                )
                (
                    new_state,
                    applied_effect,
                ) = await self._effect_dispatcher.dispatch_effect(
                    effect=effect,
                    current_state=current_state,
                    context=envelope_context,
                )
                logger.info(
                    f"[{entity_id}] Effect {applied_effect.effect_type} applied successfully. New state ref: {new_state.state_ref}"
                )

                # --- 4. Update Internal State --- #
                self._entity_states[entity_id] = new_state
                resulting_state_ref = new_state.state_ref

                # --- 5. Log Event & Notify --- #
                await self._log_and_notify(
                    event_id=event_id,
                    effect=applied_effect,
                    context=envelope_context,
                    previous_state_ref=previous_state_ref,
                    resulting_state_ref=resulting_state_ref,
                    entity_id=entity_id,
                    timestamp=timestamp,
                    validated_capability=cap_token,  # Pass validated token for logging
                )

                # --- 6. Send Acceptance Reply --- #
                reply = StateUpdateAcceptedReply(
                    request_id=request.request_id,
                    entity_id=entity_id,
                    event_id=event_id,
                    new_state_ref=resulting_state_ref,
                    timestamp=timestamp,
                )
                await self._send_reply(sender_ref, reply)

                # --- 7. Snapshotting (Optional) --- #
                if self._should_snapshot(new_state, current_state):
                    try:
                        await self._state_storage.store_snapshot(new_state)
                        logger.info(
                            f"[{entity_id}] Stored snapshot for state {resulting_state_ref}"
                        )
                    except Exception as snapshot_err:
                        logger.error(
                            f"[{entity_id}] Failed to store snapshot for state {resulting_state_ref}: {snapshot_err}",
                            exc_info=True,
                        )

            # --- Handle Dispatcher/Processing Errors --- #
            except EffectDispatchError as dispatch_err:
                logger.error(
                    f"[{entity_id}] Dispatch error applying effect {effect.effect_type}: {dispatch_err}",
                    exc_info=True,
                )
                reply = StateUpdateRejectedReply(
                    request_id=request.request_id,
                    entity_id=entity_id,
                    event_id=event_id,
                    error_type=RejectionErrorType.EFFECT_APPLICATION_FAILED,
                    error_message=f"Effect dispatch failed: {dispatch_err}",
                    timestamp=timestamp,
                )
                await self._send_reply(sender_ref, reply)
            except Exception as process_err:
                logger.exception(
                    f"[{entity_id}] Unexpected error applying effect {effect.effect_type}: {process_err}"
                )
                reply = StateUpdateRejectedReply(
                    request_id=request.request_id,
                    entity_id=entity_id,
                    event_id=event_id,
                    error_type=RejectionErrorType.INTERNAL_ERROR,
                    error_message=f"Internal server error: {process_err}",
                    timestamp=timestamp,
                )
                await self._send_reply(sender_ref, reply)
        # --- End Entity Lock --- #

    async def _handle_state_read_request(
        self,
        request: Dict,
        sender_ref: ActorRef,
        envelope_context: Optional[Context],
        cap_token: Optional[Capability],
    ):
        """Handles requests to read the current state."""
        entity_id = request.get("entity_id")
        if not entity_id:
            # Send error reply
            return

        # Capability Check for Read
        required_permission = Permission.READ_STATE  # Example permission
        required_scope = CapabilityScope.ENTITY_STATE(entity_id)
        try:
            pre_check_state = await self._ensure_entity_state_loaded(entity_id)
            is_valid = self._capability_validator.validate(
                capability=cap_token,
                required_permission=required_permission,
                required_scope=required_scope,
                context=envelope_context,
                current_state=pre_check_state,
                effect=None,
            )
            if not is_valid:
                raise PermissionError("Read permission denied.")

            # Get state (should be loaded now)
            async with self._entity_locks[entity_id]:  # Brief lock for consistent read
                current_state = self._entity_states.get(entity_id)

            if current_state:
                # Send reply with current_state or state_ref based on request details
                reply_payload = {
                    "state_ref": current_state.state_ref,
                    "timestamp": current_state.timestamp,
                }
                await self._send_reply(sender_ref, reply_payload)
            else:
                # Should not happen if _ensure works, but handle defensively
                raise StateLoadError("State not found after load attempt.")

        except (StateLoadError, PermissionError, Exception) as e:
            logging.warning(f"[{entity_id}] Failed to handle read request: {e}")
            # Send error reply
            error_reply = {"error": str(e)}
            await self._send_reply(sender_ref, error_reply)

    async def _log_and_notify(
        self,
        event_id: EventID,
        effect: BaseEffect,
        context: Optional[Context],  # Context can be optional
        previous_state_ref: Optional[StateRef],
        resulting_state_ref: StateRef,
        entity_id: str,
        timestamp: float,
        validated_capability: Optional[Capability],
    ):
        """Logs the successful event and publishes a notification."""
        # Use a placeholder context if None provided
        log_context = (
            context if context else Context(context_id="unknown", timestamp=timestamp)
        )  # Example placeholder
        try:
            # Create LoggedEvent using actual schema if available
            # Assuming LoggedEvent exists and mirrors these fields
            logged_event_data = {
                "event_id": event_id,
                "timestamp": timestamp,
                "entity_id": entity_id,
                "effect": effect,  # Requires BaseEffect to be serializable
                "context": log_context,  # Requires Context to be serializable
                "previous_state_ref": previous_state_ref,
                "resulting_state_ref": resulting_state_ref,
                "capability_id": (
                    validated_capability.capability_id if validated_capability else None
                ),
                "granter_id": (
                    validated_capability.granter_id if validated_capability else None
                ),
            }
            # Replace with actual call: await self._event_log.log_event(LoggedEvent(**logged_event_data))
            await self._event_log.log_event(
                **logged_event_data
            )  # Pass args directly if interface matches
            logger.debug(f"[{entity_id}] Successfully logged event {event_id}")

            notification = StateChangeNotification(
                event_id=event_id,
                timestamp=timestamp,
                entity_id=entity_id,
                effect_type=effect.effect_type,
                previous_state_ref=previous_state_ref,
                new_state_ref=resulting_state_ref,
            )
            await self._notification_service.publish(notification)
            logger.debug(
                f"[{entity_id}] Published state change notification for event {event_id}"
            )
        except Exception as e:
            logger.error(
                f"[{entity_id}] Failed to log event or notify for event {event_id}: {e}",
                exc_info=True,
            )

    def _should_snapshot(
        self, new_state: DualInformation, old_state: DualInformation
    ) -> bool:
        """Placeholder for snapshotting policy decision."""
        # Example: Snapshot every N updates, or based on time elapsed, or state complexity change
        # Simple placeholder: snapshot if state ref changes (always true for changes)
        return new_state.state_ref != old_state.state_ref

    async def _send_reply(
        self,
        recipient_ref: ActorRef,
        reply_payload: Union[StateUpdateAcceptedReply, StateUpdateRejectedReply],
    ):
        """Sends a reply message back to the original requestor."""
        # No need to resolve ref, it's passed in directly
        if not recipient_ref:
            logging.error("Cannot send reply: Invalid recipient_ref provided.")
            return

        reply_msg = StandardActorMessage(
            type="STATE_UPDATE_REPLY",
            payload=reply_payload,
            sender_ref=self.self_ref,  # Use self_ref from context
            context=None,  # Reply context usually not needed unless for correlation
        )

        # Use ActorRef's tell method (which uses system.deliver_message)
        try:
            await recipient_ref.tell(
                message_type=reply_msg.type,
                payload=reply_msg.payload,
                sender=self.self_ref,
                # Context not needed for simple reply
            )
            # Logging handled within tell/deliver_message if needed
        except Exception as e:
            # Catch potential errors during send (e.g., system stopped)
            logging.error(
                f"Failed to send reply to {recipient_ref.path}: {e}", exc_info=True
            )


# --- Dependency Implementations (Placeholders/Mocks) --- #


class MockStateStorage(StateStorageServiceInterface):
    _store = {}

    async def load_state(self, storage_location: str) -> Optional[DualInformation]:
        logging.debug(f"[Mock] Loading state from {storage_location}")
        return self._store.get(storage_location)

    async def store_snapshot(self, state: DualInformation) -> bool:
        logging.info(f"[Mock] Storing snapshot {state.state_ref}...")
        self._store[state.state_ref] = state
        # Simulate potential failure
        return True

    async def load_latest_snapshot(self, entity_id: str) -> Optional[DualInformation]:
        logging.debug(f"[Mock] Loading latest snapshot for {entity_id}")
        # Find highest timestamp or ref? Simple for now.
        latest_state = None
        latest_ts = -1
        for ref, state in self._store.items():
            # NOTE: The following loop assumes entity_id is part of the state_ref string.
            # This is likely not robust. A proper implementation would rely on metadata
            # stored alongside the snapshot or a dedicated index.
            ts = getattr(state, "timestamp", 0) or 0
            # Simple check if entity_id is in ref? Needs better strategy.
            if entity_id in ref and ts > latest_ts:
                latest_state = state
                latest_ts = ts
        return latest_state


from ..security.capabilities.validator import (
    CapabilityValidator,  # Import concrete
)

# REMOVED: from person_suit.core.effects.transformation_logic import StateTransformationLogic # Import concrete

# Example Usage (Conceptual - Needs Actor Framework Setup & Dependency Injection)
# [REMOVED - This is now handled conceptually by the DI container in core/containers.py]
