"""person_suit.core.actors.caw_examples
=================================================

Example CAW Actors for demonstration and testing.

- PersonaCoreActor: Simulates persona core logic
- AnalystActor: Simulates analyst logic
- PredictorActor: Simulates predictor logic

Each logs context and fidelity on message receipt.
"""

import logging
from person_suit.core.actors.caw_actor import CAWActor
from person_suit.core.actors.base import StandardActorMessage
from person_suit.core.context import UnifiedContext as DualContext

logger = logging.getLogger(__name__)

class PersonaCoreActor(CAWActor):
    async def receive_with_context(self, message: StandardActorMessage, context: DualContext, fidelity: float) -> None:
        logger.info(f"[PersonaCoreActor] Received: {message.type}, Fidelity: {fidelity:.3f}, Context: {context.domain}, Priority: {context.priority}")
        # Simulate work
        if message.type == "persona_query":
            logger.info(f"PersonaCoreActor processing query with goals: {context.goals}")

class AnalystActor(CAWActor):
    async def receive_with_context(self, message: StandardActorMessage, context: DualContext, fidelity: float) -> None:
        logger.info(f"[AnalystActor] Received: {message.type}, Fidelity: {fidelity:.3f}, Context: {context.domain}, Priority: {context.priority}")
        # Simulate work
        if message.type == "analysis_request":
            logger.info(f"AnalystActor analyzing with constraints: {context.constraints}")

class PredictorActor(CAWActor):
    async def receive_with_context(self, message: StandardActorMessage, context: DualContext, fidelity: float) -> None:
        logger.info(f"[PredictorActor] Received: {message.type}, Fidelity: {fidelity:.3f}, Context: {context.domain}, Priority: {context.priority}")
        # Simulate work
        if message.type == "prediction_request":
            logger.info(f"PredictorActor predicting with wave/particle ratio: {context.wave_particle_ratio}") 