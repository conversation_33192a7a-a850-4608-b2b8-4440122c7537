"""
File: actor.py
Purpose: Implementation of the Central State Actor, which manages the state
         of an entity and processes effects to transform it
Related Files:
    - interfaces.py: Contains the interfaces used by this implementation
    - person_suit/core/information/dual_information.py: Contains the DualInformation class
    - person_suit/core/effects/base_effect.py: Contains the BaseEffect class
Dependencies:
    - asyncio, time, uuid, logging
    - person_suit core modules
"""

import asyncio
import logging
import time
import uuid
from typing import Any, Dict

from .interfaces import (
    CapabilityValidatorInterface,
    EventLogServiceInterface,
    NotificationServiceInterface,
    StateLoadError,
    StateStorageServiceInterface,
    StateTransformationLogicInterface,
)
from ...capabilities.capability import Capability
from ...context.context import Context
from ...effects.base_effect import BaseEffect
from ...events.event_types import EventID
from ...information.dual_information import DualInformation, StateRef
from ...notification.notification_types import (
    StateChangeNotification,
)
from ...security.permissions.permission_types import (
    CapabilityScope,
    Permission,
)

logger = logging.getLogger(__name__)


class CentralStateActor:
    """
    Actor responsible for managing the state of an entity and processing effects
    that transform it. This actor ensures state consistency, capability validation,
    event logging, and notification publishing.

    The actor follows these principles:
    1. All state changes must go through validated effects
    2. All state changes are logged and can be audited
    3. Interested parties are notified of state changes
    4. State is persisted for durability
    5. Effects are processed with proper capability validation
    """

    def __init__(
        self,
        capability_validator: CapabilityValidatorInterface,
        state_storage_service: StateStorageServiceInterface,
        notification_service: NotificationServiceInterface,
        event_log_service: EventLogServiceInterface,
        state_transformation_logic: StateTransformationLogicInterface,
    ):
        """
        Initialize the Central State Actor with required services.

        Args:
            capability_validator: Service to validate capabilities against permissions
            state_storage_service: Service to store and load state snapshots
            notification_service: Service for publishing notifications about state changes
            event_log_service: Service for logging state change events
            state_transformation_logic: Logic for transforming state via effects
        """
        self._capability_validator = capability_validator
        self._state_storage_service = state_storage_service
        self._notification_service = notification_service
        self._event_log_service = event_log_service
        self._state_transformation_logic = state_transformation_logic

        # Runtime state
        self._entity_id = None
        self._current_state = None
        self._pending_effects = {}  # Dict[tx_id, effect]
        self._active_transactions = {}  # Dict[tx_id, start_time]
        self._state_lock = asyncio.Lock()

        # Performance metrics
        self._total_effects_processed = 0
        self._total_processing_time = 0.0

    async def initialize(self, entity_id: str) -> None:
        """
        Initialize the actor for a specific entity.

        Args:
            entity_id: The ID of the entity this actor will manage

        Raises:
            StateLoadError: If there's an error loading the entity state
        """
        self._entity_id = entity_id

        try:
            # Try to load existing state
            state = await self._state_storage_service.load_latest_snapshot(entity_id)

            if state is None:
                # Create a new initial state if none exists
                self._current_state = DualInformation(
                    entity_id=entity_id,
                    version=1,
                    data={},
                    vector=None,  # Will be computed as needed
                    timestamp=time.time(),
                )

                # Store initial state
                await self._state_storage_service.store_snapshot(self._current_state)
            else:
                self._current_state = state

            logger.info(
                f"Central State Actor initialized for entity {entity_id} "
                f"with state version {self._current_state.version}"
            )

        except Exception as e:
            logger.error(
                f"Error initializing Central State Actor for entity {entity_id}: {str(e)}"
            )
            raise StateLoadError(
                f"Failed to load state for entity {entity_id}: {str(e)}"
            )

    async def get_current_state(
        self, capability: Capability, context: Context
    ) -> DualInformation:
        """
        Get the current state of the entity.

        Args:
            capability: The capability to verify for READ permission
            context: Context of the request

        Returns:
            The current state as DualInformation

        Raises:
            PermissionError: If the capability does not have READ permission
        """
        if not self._capability_validator.validate(
            capability=capability,
            required_permission=Permission.READ,
            required_scope=CapabilityScope.ENTITY_SPECIFIC,
            context=context,
            current_state=self._current_state,
        ):
            logger.warning(
                f"Permission denied: {capability.capability_id} attempted to read "
                f"state for entity {self._entity_id}"
            )
            raise PermissionError(
                f"Capability {capability.capability_id} does not have "
                f"READ permission for entity {self._entity_id}"
            )

        return self._current_state

    async def apply_effect(
        self,
        effect: BaseEffect,
        capability: Capability,
        context: Context,
        wait_for_processing: bool = False,
    ) -> str:
        """
        Apply an effect to transform the entity state.

        Args:
            effect: The effect to apply
            capability: The capability to verify for WRITE permission
            context: Context of the request
            wait_for_processing: If True, wait for the effect to be processed before returning

        Returns:
            Transaction ID that can be used to check the status

        Raises:
            PermissionError: If the capability does not have WRITE permission
            ValueError: If the effect is not valid for the current state
        """
        # Validate the capability has write permission
        if not self._capability_validator.validate(
            capability=capability,
            required_permission=Permission.WRITE,
            required_scope=CapabilityScope.ENTITY_SPECIFIC,
            context=context,
            current_state=self._current_state,
            effect=effect,
        ):
            logger.warning(
                f"Permission denied: {capability.capability_id} attempted to apply "
                f"effect to entity {self._entity_id}"
            )
            raise PermissionError(
                f"Capability {capability.capability_id} does not have "
                f"WRITE permission for entity {self._entity_id}"
            )

        # Generate transaction ID
        tx_id = str(uuid.uuid4())

        # Store the effect in pending queue
        self._pending_effects[tx_id] = effect

        # Process the effect
        if wait_for_processing:
            await self._process_effect(tx_id, effect, capability, context)
        else:
            # Schedule async processing
            asyncio.create_task(
                self._process_effect(tx_id, effect, capability, context)
            )

        return tx_id

    async def _process_effect(
        self, tx_id: str, effect: BaseEffect, capability: Capability, context: Context
    ) -> None:
        """
        Process an effect to transform the state. This includes:
        1. Checking preconditions
        2. Acquiring state lock
        3. Applying the effect
        4. Storing the new state
        5. Logging the event
        6. Publishing notification

        Args:
            tx_id: Transaction ID
            effect: The effect to apply
            capability: The capability used to apply the effect
            context: Context of the request
        """
        start_time = time.time()

        try:
            # Remove from pending and mark as active
            if tx_id in self._pending_effects:
                del self._pending_effects[tx_id]
            self._active_transactions[tx_id] = start_time

            # Check preconditions
            preconditions_met = (
                await self._state_transformation_logic.check_preconditions(
                    effect, self._current_state, context
                )
            )

            if not preconditions_met:
                logger.warning(
                    f"Preconditions not met for effect {effect.get_effect_id()} "
                    f"on entity {self._entity_id}"
                )
                raise ValueError(
                    f"Preconditions not met for effect {effect.get_effect_id()}"
                )

            # Acquire lock to ensure atomic state updates
            async with self._state_lock:
                # Create state reference for the previous state
                previous_state_ref = StateRef(
                    entity_id=self._current_state.entity_id,
                    version=self._current_state.version,
                    timestamp=self._current_state.timestamp,
                )

                # Apply the effect to transform state
                new_state = await self._state_transformation_logic.apply_effect(
                    effect, self._current_state, context
                )

                # Store the new state
                await self._state_storage_service.store_snapshot(new_state)

                # Create state reference for the new state
                new_state_ref = StateRef(
                    entity_id=new_state.entity_id,
                    version=new_state.version,
                    timestamp=new_state.timestamp,
                )

                # Log the event
                event_id = EventID(str(uuid.uuid4()))
                await self._event_log_service.log_event(
                    event_id=event_id,
                    effect=effect,
                    context=context,
                    previous_state_ref=previous_state_ref,
                    resulting_state_ref=new_state_ref,
                    entity_id=self._entity_id,
                    timestamp=time.time(),
                    capability_id=capability.capability_id,
                    granter_id=capability.granted_by,
                )

                # Create and publish notification
                notification = StateChangeNotification(
                    entity_id=self._entity_id,
                    effect_id=effect.get_effect_id(),
                    effect_type=effect.get_effect_type(),
                    previous_version=previous_state_ref.version,
                    new_version=new_state_ref.version,
                    timestamp=time.time(),
                    context=context,
                )
                await self._notification_service.publish(notification)

                # Update current state
                self._current_state = new_state

                logger.info(
                    f"Effect {effect.get_effect_id()} successfully applied to "
                    f"entity {self._entity_id}, new version: {new_state.version}"
                )

        except Exception as e:
            logger.error(
                f"Error processing effect {effect.get_effect_id()} for "
                f"entity {self._entity_id}: {str(e)}"
            )
            # Re-add to pending queue for possible retry
            self._pending_effects[tx_id] = effect
            raise

        finally:
            # Remove from active transactions
            if tx_id in self._active_transactions:
                del self._active_transactions[tx_id]

            # Update metrics
            processing_time = time.time() - start_time
            self._total_effects_processed += 1
            self._total_processing_time += processing_time

    async def get_transaction_status(self, tx_id: str) -> str:
        """
        Get the status of a transaction.

        Args:
            tx_id: Transaction ID

        Returns:
            Status string: "pending", "active", or "completed"
        """
        if tx_id in self._pending_effects:
            return "pending"
        elif tx_id in self._active_transactions:
            return "active"
        else:
            return "completed"

    async def get_performance_metrics(self) -> Dict[str, Any]:
        """
        Get performance metrics for this actor.

        Returns:
            Dictionary of performance metrics
        """
        avg_processing_time = 0.0
        if self._total_effects_processed > 0:
            avg_processing_time = (
                self._total_processing_time / self._total_effects_processed
            )

        return {
            "total_effects_processed": self._total_effects_processed,
            "total_processing_time": self._total_processing_time,
            "avg_processing_time": avg_processing_time,
            "pending_effects_count": len(self._pending_effects),
            "active_transactions_count": len(self._active_transactions),
        }
