"""Dependency Injection Registration for Actors Subsystem.

Registers actor system, choreography engine, and related components with the DI container.
Aligns with v0.3 unified paradigm and design documentation for modular, context-aware orchestration.

References:
- docs/future/unified_paradigm/v0.3/unified_paradigm_v0.3.md
- docs/design/Central_State_Actor_Design.md
- docs/design/Context_Management_Design.md
"""

from .actor_system import ActorSystemInterface, RayActorSystem
from .choreography import ChoreographyEngine
from .choreography_api import ChoreographyAPI
from ..infrastructure.dependency_injection import (
    ServiceLifetime,
    ServiceProvider,
)


def register_with_container(container: ServiceProvider) -> None:
    """Register actors subsystem components with the DI container.

    Args:
        container: The DI service provider/container.
    """
    # Register Actor System (singleton)
    container.register(
        ActorSystemInterface, RayActorSystem, lifetime=ServiceLifetime.SINGLETON
    )
    # Register Choreography Engine (singleton)
    container.register(
        ChoreographyEngine, ChoreographyEngine, lifetime=ServiceLifetime.SINGLETON
    )
    # Register Choreography API (transient)
    container.register(
        ChoreographyAPI, ChoreographyAPI, lifetime=ServiceLifetime.TRANSIENT
    )
    # TODO: Register additional actor/choreography handlers as needed
    # TODO: Support context/capability injection for advanced CAW scenarios
