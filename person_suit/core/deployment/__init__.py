"""
Person Suit - Deployment Environment System (DE-1)

This module provides the deployment environment awareness and adaptation capabilities
for the Person Suit framework, enabling the system to automatically detect and configure
itself for its current operating environment.

Components:
- Environment Detection: Hardware and platform detection
- Resource Management: CPU, memory, storage, and network resource management
- Runtime Configuration: Environment-specific configuration
- Hardware Optimization: Platform-specific optimizations, especially for M3 Max
- Deployment Adapters: Environment-specific adaptations
- Security Boundaries: Environment-specific security controls and isolation
- Health Monitoring: Monitors system health and reports issues

The Deployment Environment System enables Person Suit to adapt to different deployment
environments, optimizing for available resources while maintaining consistent behavior
across development, testing, staging, and production environments.

Example usage:
```python
from . import detect_environment, get_security_manager, check_health

# Detect environment
env = detect_environment()
print(f"Running on {env.platform.system} with {env.hardware.cpu_cores} CPU cores")

# Configure security
security = get_security_manager()
security.set_security_level(SecurityLevel.HIGH)

# Check health
health_report = check_health()
if health_report.is_healthy:
    print("System is healthy")
else:
    print("System has issues:")
    for issue in health_report.issues:
        print(f"- {issue.severity.name}: {issue.message}")
```
"""

# Standard library imports
import logging
from typing import Any, Dict, Optional

# Configure logger
logger = logging.getLogger("person_suit.deployment")

# Import deployment adapter components
from .adapters.core import (
    AdapterCapability,
    AdapterInfo,
    AdapterRegistry,
    AdapterType,
    CloudAdapter,
    ContainerAdapter,
    DeploymentAdapter,
    LocalAdapter,
    get_active_adapters,
    get_adapter,
    get_adapter_registry,
    get_capability,
    register_adapter,
)

# Import runtime configuration components
from .configuration.core import (
    ConfigurationProfile,
    ProfileManager,
    RuntimeConfiguration,
    get_active_profile,
    get_environment_setting,
    get_runtime_configuration,
    refresh_configuration,
    set_environment_setting,
)

# Import environment detection components
from .detection.core import (
    EnvironmentDetector,
    detect_environment,
    get_environment_detector,
    is_container,
    is_development,
    is_m3_max,
    is_production,
)
# Import models directly from detection.models
from .detection.models import (
    EnvironmentData,
    HardwareInfo,
    PlatformInfo,
    RuntimeInfo,
    Environment, # Import Environment if it is defined in models.py
)

# Health monitoring
from .health.core import (
    HealthIssue,
    HealthReport,
    HealthSeverity,
    HealthStatus,
    check_health,
    get_health_monitor,
)

# Import hardware optimization components
from .optimization.core import (
    HardwareOptimizer,
    M3MaxOptimizationStrategy,
    OptimizationProfile,
    OptimizationSetting,
    OptimizationStrategy,
    OptimizationType,
    get_hardware_optimizer,
    is_optimization_available,
    optimize,
    optimize_function,
)

# Import security boundary components
from .security.core import (
    BoundaryType,
    SecurityBoundary,
    SecurityConstraint,
    SecurityLevel,
    SecurityManager,
    SecurityPolicy,
    create_token,
    get_active_policy,
    get_boundary,
    get_security_context,
    get_security_manager,
    verify_token,
)

# Validation
from .validation.core import (
    ValidationIssue,
    ValidationResult,
    ValidationSeverity,
    get_validation_manager,
    validate_all,
    validate_environment,
    validate_resources,
    validate_security,
)

# Local application imports
# Backward compatibility for transitional imports
from ..infrastructure.configuration import (
    Environment,
    get_environment_handler,
)

# Import resource management components
# Import the actual ResourceManager from infrastructure
from ..infrastructure.resource_optimization import (
    ResourceConsumer,
    get_resource_manager,
)
from ..infrastructure.resource_optimization import (
    ResourceManagerInterface as ResourceManager,
)

# Version info
__version__ = "0.1.0"


# Transitional DeploymentManager class (previously used in initial implementation)
class DeploymentManager:
    """
    Deployment environment manager.

    Provides deployment environment detection, feature discovery,
    and runtime adaptation to the deployment environment.

    Note: This is a transitional class that maintains compatibility with
    the previous implementation while redirecting to the new components.
    """

    def __init__(self):
        """Initialize deployment manager."""
        self._env_handler = get_environment_handler()
        self._env_detector = get_environment_detector()
        self._resource_manager = get_resource_manager()
        self._runtime_config = get_runtime_configuration()
        self._hardware_optimizer = get_hardware_optimizer()
        self._initialized = False
        self._deployment_config = {}
        self._detected_features = {}

    def initialize(self) -> bool:
        """
        Initialize deployment manager.

        Returns:
            True if initialization was successful
        """
        if self._initialized:
            logger.warning("DeploymentManager already initialized")
            return True

        # Initialize new components
        env_data = self._env_detector.detect_environment()
        self._resource_manager.initialize()
        self._runtime_config.initialize()
        self._hardware_optimizer.initialize()

        # For backward compatibility
        self._detected_features = {
            "is_m3_max": env_data.hardware.is_m3_max,
            "metal_support": env_data.hardware.supports_metal,
            "multi_core_support": env_data.hardware.cpu_count > 1,
            "container_environment": env_data.runtime.is_container,
            "memory_constrained": (env_data.hardware.total_memory or 0)
            < 8 * 1024 * 1024 * 1024,  # 8GB
        }

        # Log deployment information
        self._log_deployment_info()

        self._initialized = True
        return True

    def get_environment(self) -> Environment:
        """
        Get the current deployment environment.

        Returns:
            Current environment
        """
        env_data = detect_environment()
        return env_data.runtime.environment

    def has_feature(self, feature_name: str) -> bool:
        """
        Check if a feature is available in the current environment.

        Args:
            feature_name: Name of the feature to check

        Returns:
            True if the feature is available
        """
        if not self._detected_features:
            env_data = detect_environment()
            self._detected_features = {
                "is_m3_max": env_data.hardware.is_m3_max,
                "metal_support": env_data.hardware.supports_metal,
                "multi_core_support": env_data.hardware.cpu_count > 1,
                "container_environment": env_data.runtime.is_container,
                "memory_constrained": (env_data.hardware.total_memory or 0)
                < 8 * 1024 * 1024 * 1024,  # 8GB
            }

        return self._detected_features.get(feature_name, False)

    def get_deployment_config(self, key: str, default: Any = None) -> Any:
        """
        Get a deployment configuration value.

        Args:
            key: Configuration key
            default: Default value if not found

        Returns:
            Configuration value
        """
        # Try to get from new runtime configuration
        parts = key.split(".")
        if len(parts) == 2:
            section, setting = parts
            return get_environment_setting(section, setting, default)

        # Fallback to old mechanism
        return self._deployment_config.get(key, default)

    def _log_deployment_info(self) -> None:
        """Log information about the deployment environment."""
        env_data = detect_environment()

        # System information
        logger.info(f"Platform: {env_data.platform.system} {env_data.platform.release}")
        logger.info(f"Python version: {env_data.platform.python_version}")
        logger.info(f"Processor: {env_data.hardware.cpu_brand or 'Unknown'}")

        # Environment information
        logger.info(f"Environment: {env_data.runtime.environment.name}")

        # Feature information
        for feature, enabled in self._detected_features.items():
            logger.info(f"Feature {feature}: {'enabled' if enabled else 'disabled'}")


# Singleton instance
_deployment_manager_instance = None


def get_deployment_manager() -> DeploymentManager:
    """
    Get the singleton deployment manager instance.

    Returns:
        DeploymentManager instance
    """
    global _deployment_manager_instance

    if _deployment_manager_instance is None:
        _deployment_manager_instance = DeploymentManager()
        _deployment_manager_instance.initialize()

    return _deployment_manager_instance


# Convenience functions (for backward compatibility)
def get_environment() -> Environment:
    """Get current environment."""
    return get_deployment_manager().get_environment()


def has_feature(feature_name: str) -> bool:
    """Check if a feature is available."""
    return get_deployment_manager().has_feature(feature_name)


def is_development_environment() -> bool:
    """Check if running in development environment."""
    return is_development()


def is_production_environment() -> bool:
    """Check if running in production environment."""
    return is_production()


# New high-level convenience functions
def get_environment_info() -> Dict[str, Any]:
    """
    Get comprehensive environment information.

    Returns:
        Dictionary with environment information
    """
    env_data = detect_environment()
    return env_data.to_dict()


def optimize_for_environment(
    target: Any, optimization_type: Optional[str] = None
) -> Any:
    """
    Optimize a target for the current environment.

    Args:
        target: Target to optimize
        optimization_type: Type of optimization to apply (optional)

    Returns:
        Optimized target
    """
    opt_type = None
    if optimization_type:
        try:
            opt_type = OptimizationType[optimization_type.upper()]
        except (KeyError, ValueError):
            pass

    return optimize(target, opt_type)


# Define public API
__all__ = [
    # Environment detection
    "detect_environment",
    "EnvironmentData",
    "PlatformInfo",
    "HardwareInfo",
    "RuntimeInfo",
    # Resource Management (Imported from infrastructure & resources)
    "ResourceManager",
    "get_resource_manager",
    "ResourceConsumer",
    # Security management
    "get_security_manager",
    "SecurityLevel",
    "SecurityPolicy",
    "SecurityConstraint",
    "BoundaryType",
    "SecurityBoundary",
    # Optimization
    "OptimizationType",
    "OptimizationSetting",
    "OptimizationProfile",
    "HardwareOptimizer",
    "OptimizationStrategy",
    "M3MaxOptimizationStrategy",
    # Validation
    "get_validation_manager",
    "validate_environment",
    "validate_resources",
    "validate_security",
    "validate_all",
    "ValidationResult",
    "ValidationSeverity",
    "ValidationIssue",
    # Health monitoring
    "get_health_monitor",
    "check_health",
    "HealthStatus",
    "HealthSeverity",
    "HealthReport",
    "HealthIssue",
]
