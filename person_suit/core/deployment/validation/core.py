"""
Person Suit - Deployment Validation Core (DE-1)

This module provides validation capabilities for the Person Suit framework,
enabling verification of the deployment environment, resources, and security.

Components:
- ValidationSeverity: Severity levels for validation issues
- ValidationIssue: Validation issue details
- ValidationResult: Result of a validation operation
- Validator: Base class for validators
- EnvironmentValidator: Validates the deployment environment
- ResourceValidator: Validates resource availability and allocation
- SecurityValidator: Validates security configuration
- ValidationManager: Central validation management

Related Files:
- models.py: Validation data models and enumerations
- validator.py: Base validator implementation
- environment_validator.py: Environment validation implementation
- resource_validator.py: Resource validation implementation
- security_validator.py: Security validation implementation
- manager.py: Validation manager implementation

Dependencies:
- person_suit.core.deployment.detection: For environment detection
- person_suit.core.deployment.resources: For resource management
- person_suit.core.deployment.security: For security management
"""

# Import from environment_validator.py
from .environment_validator import (
    EnvironmentValidator,
)

# Import from manager.py
from .manager import (
    ValidationManager,
    get_validation_manager,
)

# Import from models.py
from .models import (
    Val<PERSON><PERSON>I<PERSON>ue,
    ValidationResult,
    ValidationSeverity,
)

# Import from resource_validator.py
from .resource_validator import ResourceValidator

# Import from security_validator.py
from .security_validator import SecurityValidator

# Import from validator.py
from .validator import Validator


# Convenience functions
def validate_environment() -> ValidationResult:
    """Validate the deployment environment."""
    return get_validation_manager().validate_environment()


def validate_resources() -> ValidationResult:
    """Validate resource availability and allocation."""
    return get_validation_manager().validate_resources()


def validate_security() -> ValidationResult:
    """Validate security configuration."""
    return get_validation_manager().validate_security()


def validate_all() -> ValidationResult:
    """Validate all aspects of the deployment."""
    return get_validation_manager().validate_all()


# Define exported symbols
__all__ = [
    # Models
    "ValidationSeverity",
    "ValidationIssue",
    "ValidationResult",
    # Validators
    "Validator",
    "EnvironmentValidator",
    "ResourceValidator",
    "SecurityValidator",
    # Manager
    "ValidationManager",
    "get_validation_manager",
    # Convenience functions
    "validate_environment",
    "validate_resources",
    "validate_security",
    "validate_all",
]
