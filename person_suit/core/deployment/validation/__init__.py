"""
Person Suit - Deployment Validation Package (DE-1)

This package provides validation capabilities for the Person Suit framework,
ensuring that the deployment environment meets the requirements for proper operation.

Components:
- ValidationSeverity: Severity levels for validation issues
- ValidationIssue: Detected validation issue
- ValidationResult: Results of a validation check
- ValidationRule: Rule defining a validation check
- Validator: Base validator implementation
- EnvironmentValidator: Environment-specific validation
- ResourceValidator: Resource-specific validation
- SecurityValidator: Security-specific validation
- ValidationManager: Validation orchestration and management

Functions:
- get_validation_manager: Get the validation manager instance
- validate_environment: Validate the environment
- validate_resources: Validate resource availability
- validate_security: Validate security configurations
- validate_all: Validate all aspects of the deployment

Example usage:
```python
from . import validate_all, ValidationSeverity

# Validate all aspects of the deployment
results = validate_all()

# Check for any critical issues
has_critical_issues = False
for validator, validator_results in results.items():
    for result in validator_results:
        for issue in result.issues:
            if issue.severity == ValidationSeverity.CRITICAL:
                print(f"CRITICAL ISSUE: {issue.message}")
                has_critical_issues = True

if not has_critical_issues:
    print("No critical issues found in validation")
```
"""

# Import from core module
from .core import (  # Models; Validators; Manager; Convenience functions
    EnvironmentValidator,
    ResourceValidator,
    SecurityValidator,
    ValidationIssue,
    ValidationManager,
    ValidationResult,
    ValidationSeverity,
    Validator,
    get_validation_manager,
    validate_all,
    validate_environment,
    validate_resources,
    validate_security,
)

# Define public API
__all__ = [
    # Models
    "ValidationSeverity",
    "ValidationIssue",
    "ValidationResult",
    # Validators
    "Validator",
    "EnvironmentValidator",
    "ResourceValidator",
    "SecurityValidator",
    # Manager
    "ValidationManager",
    "get_validation_manager",
    "validate_environment",
    "validate_resources",
    "validate_security",
    "validate_all",
]
