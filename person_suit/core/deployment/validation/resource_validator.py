"""
Person Suit - Resource Validator (DE-1)

This module provides resource-specific validation for the Person Suit framework,
validating CPU, memory, storage, and network resource availability and configurations.

Classes:
- ResourceValidator: Validator for deployment resources

Related Files:
- models.py: Validation data models and enumerations
- validator.py: Base validator implementation
- environment_validator.py: Environment-specific validation
- security_validator.py: Security-specific validation
- manager.py: Validation orchestration and management

Dependencies:
- person_suit.core.deployment.detection: For environment data to validate
- person_suit.core.deployment.resources: For resource management capabilities
"""

import logging
import os
import shutil
import socket
from typing import List

from ..detection import EnvironmentData
from .models import (
    ValidationIssue,
    ValidationResult,
    ValidationRule,
    ValidationSeverity,
)
from .validator import Validator

# Configure logger
logger = logging.getLogger("person_suit.deployment.validation.resource_validator")


class ResourceValidator(Validator):
    """
    Resource validator for deployment resources.

    Validates CPU, memory, storage, and network resource availability
    and configurations to ensure they meet the requirements for Person Suit.
    """

    def __init__(self):
        """Initialize the resource validator."""
        super().__init__()

    def _register_rules(self) -> None:
        """Register resource validation rules."""
        # CPU rules
        self.register_rule(
            ValidationRule(
                rule_id="RES_CPU_AVAILABILITY",
                name="CPU Availability Check",
                description="Validates that CPU resources are available",
                component="resources.cpu",
                is_critical=True,
                parameters={"min_available_percent": 10},
            )
        )

        # Memory rules
        self.register_rule(
            ValidationRule(
                rule_id="RES_MEMORY_AVAILABILITY",
                name="Memory Availability Check",
                description="Validates that memory resources are available",
                component="resources.memory",
                is_critical=True,
                parameters={"min_available_mb": 1024},  # 1GB
            )
        )

        # Storage rules
        self.register_rule(
            ValidationRule(
                rule_id="RES_STORAGE_AVAILABILITY",
                name="Storage Availability Check",
                description="Validates that storage resources are available",
                component="resources.storage",
                is_critical=True,
                parameters={"min_available_gb": 5},  # 5GB
            )
        )

        self.register_rule(
            ValidationRule(
                rule_id="RES_STORAGE_PERMISSIONS",
                name="Storage Permissions Check",
                description="Validates that storage permissions are correct",
                component="resources.storage",
                parameters={
                    "check_paths": ["~/.person_suit", "~/.person_suit/storage"]
                },
            )
        )

        # Network rules
        self.register_rule(
            ValidationRule(
                rule_id="RES_NETWORK_CONNECTIVITY",
                name="Network Connectivity Check",
                description="Validates network connectivity",
                component="resources.network",
                parameters={
                    "check_hosts": ["api.person_suit.org", "cdn.person_suit.org"]
                },
            )
        )

        self.register_rule(
            ValidationRule(
                rule_id="RES_NETWORK_PORTS",
                name="Network Ports Check",
                description="Validates required network ports are available",
                component="resources.network",
                parameters={"required_ports": [8080, 8081, 8082]},
            )
        )

    def validate(self, env_data: EnvironmentData) -> List[ValidationResult]:
        """
        Validate the resource configuration.

        Args:
            env_data: Environment data to validate

        Returns:
            List[ValidationResult]: Validation results
        """
        results = []

        # Validate all enabled rules
        for rule_id, rule in self._rules.items():
            if rule.is_enabled:
                result = self.validate_rule(rule_id, env_data)
                results.append(result)

                if not result.is_valid and rule.is_critical:
                    logger.error(f"Critical validation rule failed: {rule_id}")

        return results

    def validate_rule(
        self, rule_id: str, env_data: EnvironmentData
    ) -> ValidationResult:
        """
        Validate a specific resource rule.

        Args:
            rule_id: The ID of the rule to validate
            env_data: Environment data to validate

        Returns:
            ValidationResult: The validation result
        """
        # Check if rule exists and is enabled
        rule = self.get_rule(rule_id)
        if not rule or not rule.is_enabled:
            return super().validate_rule(rule_id, env_data)

        # Validate based on rule ID
        if rule_id == "RES_CPU_AVAILABILITY":
            return self._validate_cpu_availability(rule, env_data)
        elif rule_id == "RES_MEMORY_AVAILABILITY":
            return self._validate_memory_availability(rule, env_data)
        elif rule_id == "RES_STORAGE_AVAILABILITY":
            return self._validate_storage_availability(rule, env_data)
        elif rule_id == "RES_STORAGE_PERMISSIONS":
            return self._validate_storage_permissions(rule, env_data)
        elif rule_id == "RES_NETWORK_CONNECTIVITY":
            return self._validate_network_connectivity(rule, env_data)
        elif rule_id == "RES_NETWORK_PORTS":
            return self._validate_network_ports(rule, env_data)
        else:
            logger.warning(f"Unknown rule ID: {rule_id}")
            return ValidationResult(
                rule_id=rule_id,
                is_valid=False,
                issues=[
                    ValidationIssue(
                        code="UNKNOWN_RULE",
                        message=f"Unknown rule: {rule_id}",
                        severity=ValidationSeverity.ERROR,
                        component="resource_validator",
                    )
                ],
            )

    def _validate_cpu_availability(
        self, rule: ValidationRule, env_data: EnvironmentData
    ) -> ValidationResult:
        """Validate that CPU resources are available."""
        import psutil

        try:
            # Get CPU usage percentage
            cpu_usage = psutil.cpu_percent(interval=0.1)
            min_available_percent = rule.parameters.get("min_available_percent", 10)
            available_percent = 100 - cpu_usage

            is_valid = available_percent >= min_available_percent

            if is_valid:
                return ValidationResult(
                    rule_id=rule.rule_id,
                    is_valid=True,
                    metadata={
                        "cpu_usage": cpu_usage,
                        "available_percent": available_percent,
                        "min_available_percent": min_available_percent,
                    },
                )
            else:
                return ValidationResult(
                    rule_id=rule.rule_id,
                    is_valid=False,
                    issues=[
                        ValidationIssue(
                            code="INSUFFICIENT_CPU",
                            message=f"CPU usage is {cpu_usage}%, only {available_percent}% available (min {min_available_percent}% required)",
                            severity=ValidationSeverity.WARNING,
                            component="resources.cpu",
                            remediation="Close unnecessary applications to free up CPU resources.",
                        )
                    ],
                    metadata={
                        "cpu_usage": cpu_usage,
                        "available_percent": available_percent,
                        "min_available_percent": min_available_percent,
                    },
                )
        except Exception as e:
            logger.error(f"Error validating CPU availability: {e}")
            return ValidationResult(
                rule_id=rule.rule_id,
                is_valid=False,
                issues=[
                    ValidationIssue(
                        code="CPU_CHECK_ERROR",
                        message=f"Error checking CPU availability: {e}",
                        severity=ValidationSeverity.ERROR,
                        component="resources.cpu",
                    )
                ],
            )

    def _validate_memory_availability(
        self, rule: ValidationRule, env_data: EnvironmentData
    ) -> ValidationResult:
        """Validate that memory resources are available."""
        import psutil

        try:
            # Get memory usage
            memory = psutil.virtual_memory()
            available_mb = memory.available // (1024 * 1024)
            min_available_mb = rule.parameters.get("min_available_mb", 1024)  # 1GB

            is_valid = available_mb >= min_available_mb

            if is_valid:
                return ValidationResult(
                    rule_id=rule.rule_id,
                    is_valid=True,
                    metadata={
                        "total_mb": memory.total // (1024 * 1024),
                        "available_mb": available_mb,
                        "min_available_mb": min_available_mb,
                    },
                )
            else:
                return ValidationResult(
                    rule_id=rule.rule_id,
                    is_valid=False,
                    issues=[
                        ValidationIssue(
                            code="INSUFFICIENT_MEMORY",
                            message=f"Only {available_mb}MB memory available (min {min_available_mb}MB required)",
                            severity=ValidationSeverity.WARNING,
                            component="resources.memory",
                            remediation="Close unnecessary applications to free up memory resources.",
                        )
                    ],
                    metadata={
                        "total_mb": memory.total // (1024 * 1024),
                        "available_mb": available_mb,
                        "min_available_mb": min_available_mb,
                    },
                )
        except Exception as e:
            logger.error(f"Error validating memory availability: {e}")
            return ValidationResult(
                rule_id=rule.rule_id,
                is_valid=False,
                issues=[
                    ValidationIssue(
                        code="MEMORY_CHECK_ERROR",
                        message=f"Error checking memory availability: {e}",
                        severity=ValidationSeverity.ERROR,
                        component="resources.memory",
                    )
                ],
            )

    def _validate_storage_availability(
        self, rule: ValidationRule, env_data: EnvironmentData
    ) -> ValidationResult:
        """Validate that storage resources are available."""

        try:
            # Get storage usage
            home_dir = os.path.expanduser("~")

            # Try to get disk usage for the home directory
            disk_usage = shutil.disk_usage(home_dir)

            available_gb = disk_usage.free / (1024 * 1024 * 1024)
            min_available_gb = rule.parameters.get("min_available_gb", 5)  # 5GB

            is_valid = available_gb >= min_available_gb

            if is_valid:
                return ValidationResult(
                    rule_id=rule.rule_id,
                    is_valid=True,
                    metadata={
                        "total_gb": disk_usage.total / (1024 * 1024 * 1024),
                        "available_gb": available_gb,
                        "min_available_gb": min_available_gb,
                    },
                )
            else:
                return ValidationResult(
                    rule_id=rule.rule_id,
                    is_valid=False,
                    issues=[
                        ValidationIssue(
                            code="INSUFFICIENT_STORAGE",
                            message=f"Only {available_gb:.2f}GB storage available (min {min_available_gb}GB required)",
                            severity=ValidationSeverity.WARNING,
                            component="resources.storage",
                            remediation="Free up disk space or use a larger disk.",
                        )
                    ],
                    metadata={
                        "total_gb": disk_usage.total / (1024 * 1024 * 1024),
                        "available_gb": available_gb,
                        "min_available_gb": min_available_gb,
                    },
                )
        except Exception as e:
            logger.error(f"Error validating storage availability: {e}")
            return ValidationResult(
                rule_id=rule.rule_id,
                is_valid=False,
                issues=[
                    ValidationIssue(
                        code="STORAGE_CHECK_ERROR",
                        message=f"Error checking storage availability: {e}",
                        severity=ValidationSeverity.ERROR,
                        component="resources.storage",
                    )
                ],
            )

    def _validate_storage_permissions(
        self, rule: ValidationRule, env_data: EnvironmentData
    ) -> ValidationResult:
        """Validate that storage permissions are correct."""
        check_paths = rule.parameters.get("check_paths", [])
        issues = []

        for path in check_paths:
            # Expand path if it contains a tilde
            if path.startswith("~"):
                path = os.path.expanduser(path)

            # Check if path exists, and if not, try to create it
            if not os.path.exists(path):
                try:
                    os.makedirs(path, exist_ok=True)
                except Exception as e:
                    issues.append(
                        ValidationIssue(
                            code="PATH_CREATION_ERROR",
                            message=f"Could not create path {path}: {e}",
                            severity=ValidationSeverity.ERROR,
                            component="resources.storage",
                            remediation=f"Ensure you have permissions to create {path}.",
                        )
                    )
                    continue

            # Check if we can write to the path
            try:
                test_file = os.path.join(path, ".person_suit_test")
                with open(test_file, "w") as f:
                    f.write("test")
                os.remove(test_file)
            except Exception as e:
                issues.append(
                    ValidationIssue(
                        code="PATH_WRITE_ERROR",
                        message=f"Could not write to path {path}: {e}",
                        severity=ValidationSeverity.ERROR,
                        component="resources.storage",
                        remediation=f"Ensure you have write permissions for {path}.",
                    )
                )

        is_valid = len(issues) == 0

        if is_valid:
            return ValidationResult(
                rule_id=rule.rule_id,
                is_valid=True,
                metadata={"checked_paths": check_paths},
            )
        else:
            return ValidationResult(
                rule_id=rule.rule_id,
                is_valid=False,
                issues=issues,
                metadata={"checked_paths": check_paths},
            )

    def _validate_network_connectivity(
        self, rule: ValidationRule, env_data: EnvironmentData
    ) -> ValidationResult:
        """Validate network connectivity."""
        check_hosts = rule.parameters.get("check_hosts", [])
        issues = []
        connectivity_results = {}

        for host in check_hosts:
            try:
                # Try to resolve the host first
                socket.gethostbyname(host)

                # Try to connect to the host on port 80
                s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                s.settimeout(2)
                result = s.connect_ex((host, 80))
                s.close()

                if result == 0:
                    connectivity_results[host] = True
                else:
                    connectivity_results[host] = False
                    issues.append(
                        ValidationIssue(
                            code="HOST_CONNECTION_ERROR",
                            message=f"Could not connect to host {host}",
                            severity=ValidationSeverity.WARNING,
                            component="resources.network",
                            remediation=f"Check your network connection and ensure {host} is reachable.",
                        )
                    )
            except Exception as e:
                connectivity_results[host] = False
                issues.append(
                    ValidationIssue(
                        code="HOST_RESOLUTION_ERROR",
                        message=f"Could not resolve host {host}: {e}",
                        severity=ValidationSeverity.WARNING,
                        component="resources.network",
                        remediation="Check your network connection and DNS configuration.",
                    )
                )

        is_valid = len(issues) == 0

        if is_valid:
            return ValidationResult(
                rule_id=rule.rule_id,
                is_valid=True,
                metadata={
                    "checked_hosts": check_hosts,
                    "results": connectivity_results,
                },
            )
        else:
            return ValidationResult(
                rule_id=rule.rule_id,
                is_valid=False,
                issues=issues,
                metadata={
                    "checked_hosts": check_hosts,
                    "results": connectivity_results,
                },
            )

    def _validate_network_ports(
        self, rule: ValidationRule, env_data: EnvironmentData
    ) -> ValidationResult:
        """Validate required network ports are available."""
        required_ports = rule.parameters.get("required_ports", [])
        issues = []
        port_results = {}

        for port in required_ports:
            try:
                # Try to bind to the port
                s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                s.bind(("127.0.0.1", port))
                s.close()
                port_results[port] = True
            except Exception as e:
                port_results[port] = False
                issues.append(
                    ValidationIssue(
                        code="PORT_UNAVAILABLE",
                        message=f"Port {port} is not available: {e}",
                        severity=ValidationSeverity.WARNING,
                        component="resources.network",
                        remediation=f"Ensure port {port} is not in use by another application.",
                    )
                )

        is_valid = len(issues) == 0

        if is_valid:
            return ValidationResult(
                rule_id=rule.rule_id,
                is_valid=True,
                metadata={"checked_ports": required_ports, "results": port_results},
            )
        else:
            return ValidationResult(
                rule_id=rule.rule_id,
                is_valid=False,
                issues=issues,
                metadata={"checked_ports": required_ports, "results": port_results},
            )
