"""
Person Suit - Deployment Adapters Core (DE-1)

This module provides deployment adapters for the Person Suit framework,
enabling integration with different deployment environments.

Components:
- AdapterType: Types of deployment adapters
- AdapterCapability: Capabilities provided by deployment adapters
- AdapterInfo: Information about a deployment adapter
- DeploymentAdapter: Base class for all deployment adapters
- CloudAdapter: Base class for cloud adapters
- ContainerAdapter: Base class for container adapters
- LocalAdapter: Adapter for local environments
- AdapterRegistry: Central registry for deployment adapters

Related Files:
- models.py: Adapter data models and enumerations
- base.py: Base adapter classes and interfaces
- cloud.py: Cloud service adapters
- container.py: Container environment adapters
- local.py: Local environment adapter
- registry.py: Adapter registry for managing adapters

Dependencies:
- person_suit.core.deployment.detection: For environment detection
"""

from .base import DeploymentAdapter
from .cloud import AWSAdapter, CloudAdapter
from .container import (
    ContainerAdapter,
    KubernetesAdapter,
)
from .local import LocalAdapter

# Import from modular adapter components
from .models import (
    Adapt<PERSON><PERSON>apa<PERSON>,
    AdapterInfo,
    AdapterType,
)
from .registry import (
    AdapterRegistry,
    get_active_adapters,
    get_adapter,
    get_adapter_registry,
    get_capability,
    register_adapter,
)

# Define public API
__all__ = [
    # Models
    "AdapterType",
    "AdapterCapability",
    "AdapterInfo",
    # Base adapter
    "DeploymentAdapter",
    # Cloud adapters
    "CloudAdapter",
    "AWSAdapter",
    # Container adapters
    "ContainerAdapter",
    "KubernetesAdapter",
    # Local adapter
    "LocalAdapter",
    # Registry
    "AdapterRegistry",
    "get_adapter_registry",
    "get_adapter",
    "get_active_adapters",
    "get_capability",
    "register_adapter",
]
