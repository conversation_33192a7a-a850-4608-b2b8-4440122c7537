"""
Person Suit - Container Deployment Adapters (DE-1)

This module provides container deployment adapters for the Person Suit framework,
enabling integration with container orchestration platforms like Kubernetes and Docker.

Classes:
- ContainerAdapter: Abstract base class for container adapters
- KubernetesAdapter: Kubernetes container orchestration adapter

Related Files:
- models.py: Adapter data models and enumerations
- base.py: Base adapter classes and interfaces
- cloud.py: Cloud service adapters
- local.py: Local environment adapter
- registry.py: Adapter registry for managing adapters

Dependencies:
- person_suit.core.deployment.detection: For environment detection
"""

import abc
import logging
import os
import socket
from typing import Any, Dict, Optional

from .base import DeploymentAdapter
from .models import (
    AdapterCapability,
    AdapterInfo,
    AdapterType,
)
from ..detection import EnvironmentData
from ...infrastructure.configuration.environment.handler import (
    Environment,
)

# Configure logger
logger = logging.getLogger("person_suit.deployment.adapters.container")


class ContainerAdapter(DeploymentAdapter[Any], metaclass=abc.ABCMeta):
    """
    Abstract base class for container-based deployment adapters.

    This class extends the DeploymentAdapter with container-specific
    functionality for Docker, Kubernetes, etc.
    """

    def __init__(self, environment_data: EnvironmentData):
        """
        Initialize the container adapter.

        Args:
            environment_data: Environment data from detection
        """
        super().__init__(environment_data)
        self._container_info = {}

        logger.debug(f"Created container adapter: {self.adapter_info.name}")

    @abc.abstractmethod
    def detect_container_type(self) -> Optional[str]:
        """
        Detect the container type.

        Returns:
            str: Container type if detected, None otherwise
        """
        pass

    @abc.abstractmethod
    def get_container_info(self) -> Dict[str, Any]:
        """
        Get container information.

        Returns:
            Dict[str, Any]: Container information
        """
        pass


class KubernetesAdapter(ContainerAdapter):
    """
    Kubernetes container orchestration adapter.

    This adapter provides integration with Kubernetes, enabling
    Person Suit to leverage Kubernetes' container orchestration.
    """

    def __init__(self, environment_data: EnvironmentData):
        """
        Initialize the Kubernetes adapter.

        Args:
            environment_data: Environment data from detection
        """
        super().__init__(environment_data)
        self._namespace = None
        self._pod_name = None
        self._service_account = None

        logger.debug("Created Kubernetes adapter")

    def _create_adapter_info(self) -> AdapterInfo:
        """
        Create adapter information.

        Returns:
            AdapterInfo: Adapter information
        """
        return AdapterInfo(
            name="Kubernetes",
            adapter_type=AdapterType.CONTAINER,
            description="Kubernetes container orchestration adapter",
            capabilities={
                AdapterCapability.NETWORKING,
                AdapterCapability.LOGGING,
                AdapterCapability.MONITORING,
                AdapterCapability.SCALING,
                AdapterCapability.SECURITY,
                AdapterCapability.CONFIGURATION,
                AdapterCapability.DISCOVERY,
            },
            environments={
                Environment.DEVELOPMENT,
                Environment.TESTING,
                Environment.STAGING,
                Environment.PRODUCTION,
            },
            version="0.1.0",
            metadata={"container_type": "kubernetes"},
        )

    def initialize(self) -> bool:
        """
        Initialize the Kubernetes adapter.

        Returns:
            bool: True if initialization was successful
        """
        if self._is_initialized:
            return True

        try:
            # Detect Kubernetes environment
            self._namespace = self._detect_namespace()
            self._pod_name = self._detect_pod_name()
            self._service_account = self._detect_service_account()

            # Initialize Kubernetes client
            # In a real implementation, we would initialize the Kubernetes client

            self._is_initialized = True
            logger.info(
                f"Kubernetes adapter initialized (namespace: {self._namespace})"
            )
            return True
        except Exception as e:
            logger.error(f"Failed to initialize Kubernetes adapter: {e}")
            return False

    def is_applicable(self) -> bool:
        """
        Check if this adapter is applicable to the current environment.

        Returns:
            bool: True if applicable
        """
        # Check for Kubernetes environment indicators
        if self.detect_container_type() == "kubernetes":
            return True

        return False

    def detect_container_type(self) -> Optional[str]:
        """
        Detect if running in Kubernetes.

        Returns:
            str: "kubernetes" if detected, None otherwise
        """
        # Check for Kubernetes service account
        if os.path.exists("/var/run/secrets/kubernetes.io/serviceaccount/token"):
            return "kubernetes"

        # Check for Kubernetes environment variables
        if "KUBERNETES_SERVICE_HOST" in os.environ:
            return "kubernetes"

        return None

    def get_container_info(self) -> Dict[str, Any]:
        """
        Get Kubernetes container information.

        Returns:
            Dict[str, Any]: Kubernetes information
        """
        return {
            "namespace": self._namespace,
            "pod_name": self._pod_name,
            "service_account": self._service_account,
        }

    def _detect_namespace(self) -> Optional[str]:
        """
        Detect Kubernetes namespace.

        Returns:
            str: Namespace if detected, None otherwise
        """
        # Try to read from the service account namespace file
        namespace_file = "/var/run/secrets/kubernetes.io/serviceaccount/namespace"
        if os.path.exists(namespace_file):
            try:
                with open(namespace_file, "r") as f:
                    return f.read().strip()
            except Exception as e:
                logger.warning(f"Failed to read namespace file: {e}")

        # Check for environment variable
        if "KUBERNETES_NAMESPACE" in os.environ:
            return os.environ["KUBERNETES_NAMESPACE"]

        return "default"  # Default namespace

    def _detect_pod_name(self) -> Optional[str]:
        """
        Detect Kubernetes pod name.

        Returns:
            str: Pod name if detected, None otherwise
        """
        # Check for hostname which is usually the pod name
        try:
            return socket.gethostname()
        except Exception:
            pass

        # Check for environment variable
        if "HOSTNAME" in os.environ:
            return os.environ["HOSTNAME"]

        return None

    def _detect_service_account(self) -> Optional[str]:
        """
        Detect Kubernetes service account.

        Returns:
            str: Service account if detected, None otherwise
        """
        # In a real implementation, this would read from the service account token
        # For now, return a placeholder
        return "default"

    def get_networking_capability(self) -> Optional[Any]:
        """
        Get Kubernetes networking capability.

        Returns:
            Optional[Any]: Networking capability if available
        """
        return {"type": "kubernetes_networking", "namespace": self._namespace}

    def get_discovery_capability(self) -> Optional[Any]:
        """
        Get Kubernetes service discovery capability.

        Returns:
            Optional[Any]: Service discovery capability if available
        """
        return {"type": "kubernetes_discovery", "namespace": self._namespace}
