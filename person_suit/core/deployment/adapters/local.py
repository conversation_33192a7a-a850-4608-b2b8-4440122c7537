"""
Person Suit - Local Deployment Adapter (DE-1)

This module provides a local deployment adapter for the Person Suit framework,
enabling operation in local development and testing environments.

Classes:
- LocalAdapter: Adapter for local development and testing environments

Related Files:
- models.py: Adapter data models and enumerations
- base.py: Base adapter classes and interfaces
- cloud.py: Cloud service adapters
- container.py: Container environment adapters
- registry.py: Adapter registry for managing adapters

Dependencies:
- person_suit.core.deployment.detection: For environment detection
"""

import logging
import os
import platform
from typing import Any, Optional

from .base import DeploymentAdapter
from .models import (
    AdapterCapability,
    AdapterInfo,
    AdapterType,
)
from ..detection import EnvironmentData
from ...infrastructure.configuration.environment.handler import (
    Environment,
)

# Configure logger
logger = logging.getLogger("person_suit.deployment.adapters.local")


class LocalAdapter(DeploymentAdapter[Any]):
    """
    Local environment deployment adapter.

    This adapter provides deployment capabilities for local development
    and testing environments, using local file systems and resources.
    """

    def __init__(self, environment_data: EnvironmentData):
        """
        Initialize the local adapter.

        Args:
            environment_data: Environment data from detection
        """
        super().__init__(environment_data)
        self._local_env_type = None

        logger.debug("Created local adapter")

    def _create_adapter_info(self) -> AdapterInfo:
        """
        Create adapter information.

        Returns:
            AdapterInfo: Adapter information
        """
        return AdapterInfo(
            name="Local",
            adapter_type=AdapterType.LOCAL,
            description="Local development and testing adapter",
            capabilities={
                AdapterCapability.STORAGE,
                AdapterCapability.LOGGING,
                AdapterCapability.CONFIGURATION,
                AdapterCapability.IDENTITY,
            },
            environments={Environment.DEVELOPMENT, Environment.TESTING},
            version="0.1.0",
            metadata={"environment_type": "local"},
        )

    def initialize(self) -> bool:
        """
        Initialize the local adapter.

        Returns:
            bool: True if initialization was successful
        """
        if self._is_initialized:
            return True

        try:
            # Detect local environment type
            self._local_env_type = self._detect_local_environment()

            # Set up local storage paths
            home_dir = os.path.expanduser("~")
            self._storage_path = os.path.join(home_dir, ".person_suit", "storage")
            self._config_path = os.path.join(home_dir, ".person_suit", "config")
            self._logs_path = os.path.join(home_dir, ".person_suit", "logs")

            # Create directories if they don't exist
            os.makedirs(self._storage_path, exist_ok=True)
            os.makedirs(self._config_path, exist_ok=True)
            os.makedirs(self._logs_path, exist_ok=True)

            self._is_initialized = True
            logger.info(
                f"Local adapter initialized (environment: {self._local_env_type})"
            )
            return True
        except Exception as e:
            logger.error(f"Failed to initialize local adapter: {e}")
            return False

    def is_applicable(self) -> bool:
        """
        Check if this adapter is applicable to the current environment.

        Returns:
            bool: True if applicable
        """
        # Local adapter is applicable in development and testing environments
        # that are not in cloud or container environments
        runtime_env = self._environment_data.runtime.environment
        return (
            runtime_env in (Environment.DEVELOPMENT, Environment.TESTING)
            and not self._environment_data.runtime.is_container
            and not any(env.startswith("AWS_") for env in os.environ)
        )

    def _detect_local_environment(self) -> str:
        """
        Detect the type of local environment.

        Returns:
            str: Local environment type
        """
        system = platform.system()

        if system == "Darwin":
            return "macos"
        elif system == "Linux":
            return "linux"
        elif system == "Windows":
            return "windows"
        else:
            return "unknown"

    def get_storage_capability(self) -> Optional[Any]:
        """
        Get local storage capability.

        Returns:
            Optional[Any]: Storage capability if available
        """
        return {
            "type": "local_filesystem",
            "path": self._storage_path,
            "environment": self._local_env_type,
        }

    def get_logging_capability(self) -> Optional[Any]:
        """
        Get local logging capability.

        Returns:
            Optional[Any]: Logging capability if available
        """
        return {
            "type": "local_logging",
            "path": self._logs_path,
            "environment": self._local_env_type,
        }
