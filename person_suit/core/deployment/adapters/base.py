"""
Person Suit - Deployment Adapters Base (DE-1)

This module provides base classes for deployment adapters in the Person Suit framework.
It defines the abstract interfaces that all adapter implementations must follow.

Classes:
- DeploymentAdapter: Abstract base class for all deployment adapters

Related Files:
- models.py: Adapter data models and enumerations
- cloud.py: Cloud service adapters (AWS, GCP, Azure)
- container.py: Container environment adapters (Kubernetes, Docker)
- local.py: Local environment adapter
- registry.py: Adapter registry for managing adapters

Dependencies:
- person_suit.core.deployment.detection: For environment detection
"""

import abc
import logging
from typing import Any, Generic, Optional, TypeVar

from .models import AdapterCapability, AdapterInfo
from ..detection import EnvironmentData

# Configure logger
logger = logging.getLogger("person_suit.deployment.adapters.base")

# Type variable for adapter-specific implementations
T = TypeVar("T")


class DeploymentAdapter(Generic[T], metaclass=abc.ABCMeta):
    """
    Abstract base class for deployment adapters.

    This class defines the interface that all deployment adapters must implement,
    providing a consistent way to interact with different deployment environments.
    """

    def __init__(self, environment_data: EnvironmentData):
        """
        Initialize the deployment adapter.

        Args:
            environment_data: Environment data from detection
        """
        self._environment_data = environment_data
        self._adapter_info = self._create_adapter_info()
        self._is_initialized = False

        logger.debug(f"Created adapter: {self._adapter_info.name}")

    @property
    def adapter_info(self) -> AdapterInfo:
        """Get adapter information."""
        return self._adapter_info

    @abc.abstractmethod
    def _create_adapter_info(self) -> AdapterInfo:
        """
        Create adapter information.

        Returns:
            AdapterInfo: Adapter information
        """
        pass

    @abc.abstractmethod
    def initialize(self) -> bool:
        """
        Initialize the adapter.

        This method should perform any necessary setup to make the adapter
        ready for use.

        Returns:
            bool: True if initialization was successful
        """
        pass

    @abc.abstractmethod
    def is_applicable(self) -> bool:
        """
        Check if this adapter is applicable to the current environment.

        Returns:
            bool: True if applicable
        """
        pass

    def get_capability(self, capability: AdapterCapability) -> Optional[Any]:
        """
        Get a specific capability from this adapter.

        Args:
            capability: The capability to get

        Returns:
            Optional[Any]: The capability implementation if available, None otherwise
        """
        # Check if this adapter supports the capability
        if capability not in self._adapter_info.capabilities:
            logger.debug(
                f"Adapter {self._adapter_info.name} does not support capability {capability.name}"
            )
            return None

        # Construct the capability method name
        method_name = f"get_{capability.name.lower()}_capability"

        # Check if the method exists
        if hasattr(self, method_name) and callable(getattr(self, method_name)):
            try:
                return getattr(self, method_name)()
            except Exception as e:
                logger.error(f"Error getting capability {capability.name}: {e}")
                return None
        else:
            logger.warning(
                f"Adapter {self._adapter_info.name} claims to support capability {capability.name} but does not implement {method_name}()"
            )
            return None
