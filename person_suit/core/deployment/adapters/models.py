"""
Person Suit - Deployment Adapters Models (DE-1)

This module provides data models for the deployment adapter system,
defining types, capabilities, and information structures for deployment adapters.

Models:
- AdapterType: Types of deployment adapters
- AdapterCapability: Capabilities provided by deployment adapters
- AdapterInfo: Information about a deployment adapter

Related Files:
- base.py: Base adapter classes and interfaces
- cloud.py: Cloud service adapters (AWS, GCP, Azure)
- container.py: Container environment adapters (Kubernetes, Docker)
- local.py: Local environment adapter
- registry.py: Adapter registry for managing adapters

Dependencies:
- person_suit.core.infrastructure.configuration.environment: For environment types
"""

from dataclasses import dataclass, field
from enum import Enum, auto
from typing import Any, Dict, Set

# Import from infrastructure for environment types
from ...infrastructure.configuration.environment.handler import (
    Environment,
)


class AdapterType(Enum):
    """Types of deployment adapters."""

    CLOUD = auto()
    CONTAINER = auto()
    LOCAL = auto()
    EDGE = auto()
    CI = auto()
    CUSTOM = auto()


class AdapterCapability(Enum):
    """Capabilities provided by deployment adapters."""

    STORAGE = auto()
    NETWORKING = auto()
    LOGGING = auto()
    MONITORING = auto()
    SCALING = auto()
    SECURITY = auto()
    CONFIGURATION = auto()
    IDENTITY = auto()
    MESSAGING = auto()
    DISCOVERY = auto()


@dataclass
class AdapterInfo:
    """Information about a deployment adapter."""

    name: str
    adapter_type: AdapterType
    description: str
    capabilities: Set[AdapterCapability]
    environments: Set[Environment]
    version: str
    is_active: bool = False
    metadata: Dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "name": self.name,
            "adapter_type": self.adapter_type.name,
            "description": self.description,
            "capabilities": [cap.name for cap in self.capabilities],
            "environments": [env.name for env in self.environments],
            "version": self.version,
            "is_active": self.is_active,
            "metadata": self.metadata,
        }
