"""
Person Suit - Hardware Detection Module (DE-1)

This module implements hardware detection for the Person Suit framework,
identifying CPU, memory, GPU, and other hardware characteristics with
special focus on Apple M3 Max hardware.

Components:
- HardwareDetector: Detects and reports hardware information

Related Files:
- models.py: Data models for detection results
- platform_detector.py: Platform detection implementation
- runtime_detector.py: Runtime environment detection
- __init__.py: Module exports and convenience functions

Dependencies:
- person_suit.core.deployment.detection.platform_detector: For platform information
"""

import logging
import multiprocessing
import os
import re
import subprocess
from typing import Any, Dict, Optional, Tuple

from .models import HardwareInfo
from .platform_detector import detect_platform

# Configure logger
logger = logging.getLogger("person_suit.deployment.detection.hardware")


class HardwareDetector:
    """
    Hardware detection for the Person Suit framework.

    Detects and provides information about the hardware,
    including CPU, memory, GPU, and M3 Max-specific capabilities.
    """

    def __init__(self):
        """Initialize the hardware detector."""
        self._hardware_info = None
        self._platform_info = None

    def detect_hardware(self) -> HardwareInfo:
        """
        Detect hardware information.

        Returns:
            HardwareInfo: Hardware information
        """
        if self._hardware_info is not None:
            return self._hardware_info

        # Get platform information if needed
        if self._platform_info is None:
            self._platform_info = detect_platform()

        try:
            # Get basic hardware information
            cpu_count = os.cpu_count() or multiprocessing.cpu_count()
            total_memory = self._get_total_memory()
            available_memory = self._get_available_memory()

            # Initialize hardware info
            self._hardware_info = HardwareInfo(
                cpu_count=cpu_count,
                total_memory=total_memory,
                available_memory=available_memory,
            )

            # Detect CPU model and brand
            self._detect_cpu_info()

            # Detect core types (for hybrid architectures)
            self._detect_core_types()

            # Detect cache sizes
            self._hardware_info.cache_sizes = self._detect_cache_sizes()

            # Detect M3 Max and other capabilities
            self._detect_m3_max()

            # Detect GPU
            self._hardware_info.has_gpu, self._hardware_info.gpu_info = (
                self._detect_gpu()
            )

            # Log hardware detection
            logger.debug(
                f"Detected CPU: {self._hardware_info.cpu_brand or 'Unknown'} with {cpu_count} cores"
            )
            if self._hardware_info.is_m3_max:
                logger.info("Detected Apple M3 Max processor")

            return self._hardware_info
        except Exception as e:
            logger.error(f"Failed to detect hardware: {e}")

            # Return default hardware info on error
            return HardwareInfo(cpu_count=cpu_count, total_memory=0)

    def _get_total_memory(self) -> int:
        """
        Get total system memory.

        Returns:
            int: Total memory in bytes
        """
        system = self._platform_info.system

        try:
            if system == "Linux":
                with open("/proc/meminfo", "r") as f:
                    for line in f:
                        if "MemTotal" in line:
                            # Memory is in KB, convert to bytes
                            return int(line.split()[1]) * 1024
            elif system == "Darwin":  # macOS
                output = subprocess.check_output(["sysctl", "-n", "hw.memsize"]).strip()
                return int(output)
            elif system == "Windows":
                import ctypes

                kernel32 = ctypes.windll.kernel32
                memory_status = ctypes.c_ulonglong * 5
                memory_data = memory_status()
                memory_data[0] = ctypes.sizeof(memory_status)
                kernel32.GlobalMemoryStatusEx(ctypes.byref(memory_data))
                return memory_data[1]
        except Exception as e:
            logger.warning(f"Failed to get total memory: {e}")

        # Fallback
        return 0

    def _get_available_memory(self) -> Optional[int]:
        """
        Get available system memory.

        Returns:
            Optional[int]: Available memory in bytes, if available
        """
        system = self._platform_info.system

        try:
            if system == "Linux":
                with open("/proc/meminfo", "r") as f:
                    for line in f:
                        if "MemAvailable" in line:
                            # Memory is in KB, convert to bytes
                            return int(line.split()[1]) * 1024
            elif system == "Darwin":  # macOS
                vmstat = (
                    subprocess.check_output(["vm_stat"]).decode().strip().split("\n")
                )
                page_size = 4096  # Default to 4KB page size

                # Extract page size if available
                for line in vmstat:
                    if "page size" in line.lower():
                        try:
                            page_size = int(
                                line.split("page size of")[1].strip().split()[0]
                            )
                        except (IndexError, ValueError):
                            pass

                free_pages = 0
                for line in vmstat:
                    if "Pages free" in line:
                        free_pages += int(line.split(":")[1].strip().split(".")[0])
                    elif "Pages inactive" in line:
                        free_pages += int(line.split(":")[1].strip().split(".")[0])

                return free_pages * page_size
            elif system == "Windows":
                import ctypes

                kernel32 = ctypes.windll.kernel32
                memory_status = ctypes.c_ulonglong * 5
                memory_data = memory_status()
                memory_data[0] = ctypes.sizeof(memory_status)
                kernel32.GlobalMemoryStatusEx(ctypes.byref(memory_data))
                return memory_data[2]
        except Exception as e:
            logger.warning(f"Failed to get available memory: {e}")

        return None

    def _detect_cpu_info(self) -> None:
        """
        Detect CPU model and brand.

        Updates the hardware_info object with CPU information.
        """
        system = self._platform_info.system

        try:
            if system == "Linux":
                with open("/proc/cpuinfo", "r") as f:
                    content = f.read()
                    model_match = re.search(r"model name\s+:\s+(.*)", content)
                    if model_match:
                        self._hardware_info.cpu_model = model_match.group(1)
                        self._hardware_info.cpu_brand = self._hardware_info.cpu_model
            elif system == "Darwin":  # macOS
                output = (
                    subprocess.check_output(
                        ["sysctl", "-n", "machdep.cpu.brand_string"]
                    )
                    .decode()
                    .strip()
                )
                self._hardware_info.cpu_brand = output
                self._hardware_info.cpu_model = output
            elif system == "Windows":
                import winreg

                key = winreg.OpenKey(
                    winreg.HKEY_LOCAL_MACHINE,
                    r"HARDWARE\DESCRIPTION\System\CentralProcessor\0",
                )
                self._hardware_info.cpu_model = winreg.QueryValueEx(
                    key, "ProcessorNameString"
                )[0]
                self._hardware_info.cpu_brand = self._hardware_info.cpu_model
        except Exception as e:
            logger.warning(f"Failed to detect CPU info: {e}")

    def _detect_core_types(self) -> None:
        """
        Detect performance and efficiency cores.

        Updates the hardware_info object with core type information.
        Especially important for Apple Silicon hybrid architecture.
        """
        # Initialize with defaults
        self._hardware_info.performance_core_count = self._hardware_info.cpu_count
        self._hardware_info.efficiency_core_count = 0

        # For Apple Silicon, detect hybrid architecture
        if (
            self._platform_info.system == "Darwin"
            and self._platform_info.machine == "arm64"
        ):
            try:
                # Check for Apple hybrid architecture
                if "Apple M" in (self._hardware_info.cpu_brand or ""):
                    # Use sysctl to get CPU topology
                    topology = (
                        subprocess.check_output(["sysctl", "-n", "hw.perflevel"])
                        .decode()
                        .strip()
                    )

                    # Count core types
                    perf_cores = 0
                    eff_cores = 0

                    for level in topology.split():
                        if level == "0":  # Efficiency cores
                            eff_cores += 1
                        elif level == "1":  # Performance cores
                            perf_cores += 1

                    if perf_cores > 0 or eff_cores > 0:
                        self._hardware_info.performance_core_count = perf_cores
                        self._hardware_info.efficiency_core_count = eff_cores
                        logger.debug(
                            f"Detected hybrid core architecture: {perf_cores} performance cores, {eff_cores} efficiency cores"
                        )
            except Exception as e:
                logger.warning(f"Failed to detect core types: {e}")

    def _detect_cache_sizes(self) -> Dict[str, int]:
        """
        Detect CPU cache sizes.

        Returns:
            Dict[str, int]: Cache sizes in bytes
        """
        cache_sizes = {}
        system = self._platform_info.system

        try:
            if system == "Linux":
                cache_levels = ["L1d", "L1i", "L2", "L3"]
                for level in cache_levels:
                    path = "/sys/devices/system/cpu/cpu0/cache/index*/level"
                    size_path = "/sys/devices/system/cpu/cpu0/cache/index*/size"

                    if os.path.exists(path.replace("*", "0")):
                        # Find caches of this level
                        for i in range(10):  # Check up to 10 cache indices
                            level_path = path.replace("*", str(i))
                            if os.path.exists(level_path):
                                with open(level_path, "r") as f:
                                    if f.read().strip() == level[-1]:  # L1, L2, L3
                                        # Get size
                                        size_file = size_path.replace("*", str(i))
                                        if os.path.exists(size_file):
                                            with open(size_file, "r") as sf:
                                                size_str = sf.read().strip()
                                                size = int(size_str.split()[0])
                                                unit = size_str[-1].upper()

                                                # Convert to bytes
                                                if unit == "K":
                                                    size *= 1024
                                                elif unit == "M":
                                                    size *= 1024 * 1024

                                                cache_sizes[level] = size
            elif system == "Darwin":  # macOS
                # Get L1 data cache size
                output = (
                    subprocess.check_output(["sysctl", "-n", "hw.l1dcachesize"])
                    .decode()
                    .strip()
                )
                if output:
                    cache_sizes["L1d"] = int(output)

                # Get L1 instruction cache size
                output = (
                    subprocess.check_output(["sysctl", "-n", "hw.l1icachesize"])
                    .decode()
                    .strip()
                )
                if output:
                    cache_sizes["L1i"] = int(output)

                # Get L2 cache size
                output = (
                    subprocess.check_output(["sysctl", "-n", "hw.l2cachesize"])
                    .decode()
                    .strip()
                )
                if output:
                    cache_sizes["L2"] = int(output)

                # Get L3 cache size
                output = (
                    subprocess.check_output(["sysctl", "-n", "hw.l3cachesize"])
                    .decode()
                    .strip()
                )
                if output:
                    cache_sizes["L3"] = int(output)
            elif system == "Windows":
                # Windows requires using WMI, which is more complex
                # This is a simplified approach
                try:
                    import wmi

                    c = wmi.WMI()
                    for cpu in c.Win32_Processor():
                        cache_sizes["L2"] = (
                            cpu.L2CacheSize * 1024 if cpu.L2CacheSize else 0
                        )
                        cache_sizes["L3"] = (
                            cpu.L3CacheSize * 1024 if cpu.L3CacheSize else 0
                        )
                except ImportError:
                    logger.warning(
                        "Failed to import wmi module for Windows cache detection"
                    )
        except Exception as e:
            logger.warning(f"Failed to detect cache sizes: {e}")

        return cache_sizes

    def _detect_m3_max(self) -> None:
        """
        Detect Apple M3 Max hardware and related capabilities.

        Updates the hardware_info object with M3 Max-specific information.
        """
        # Initialize with defaults
        self._hardware_info.is_m3_max = False
        self._hardware_info.supports_metal = False
        self._hardware_info.has_neural_engine = False

        # Check environment variable override
        if os.environ.get("PERSON_SUIT_M3_MAX") == "1":
            self._hardware_info.is_m3_max = True
            self._hardware_info.supports_metal = True
            self._hardware_info.has_neural_engine = True
            logger.info("M3 Max detected from environment variable")
            return

        # Check for Apple Silicon / Darwin + arm64
        if (
            self._platform_info.system == "Darwin"
            and self._platform_info.machine == "arm64"
        ):
            # Check for M3 Max specifically
            try:
                output = (
                    subprocess.check_output(
                        ["sysctl", "-n", "machdep.cpu.brand_string"]
                    )
                    .decode()
                    .strip()
                )

                if "Apple M3 Max" in output:
                    self._hardware_info.is_m3_max = True
                    self._hardware_info.supports_metal = True
                    self._hardware_info.has_neural_engine = True
                    logger.info("Apple M3 Max detected")
                elif "Apple M3" in output:
                    # M3 but not Max
                    self._hardware_info.supports_metal = True
                    self._hardware_info.has_neural_engine = True
                    logger.info("Apple M3 detected (not Max)")
                elif "Apple M" in output:
                    # Other M-series
                    self._hardware_info.supports_metal = True
                    self._hardware_info.has_neural_engine = True
                    logger.info(f"Apple Silicon detected: {output}")
            except Exception as e:
                logger.warning(f"Failed to detect M3 Max: {e}")

            # Check for Metal support
            if not self._hardware_info.supports_metal:
                try:
                    # Simple check for metal framework
                    metal_path = "/System/Library/Frameworks/Metal.framework"
                    if os.path.exists(metal_path):
                        self._hardware_info.supports_metal = True
                        logger.debug("Metal support detected")
                except Exception as e:
                    logger.warning(f"Failed to detect Metal support: {e}")

    def _detect_gpu(self) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """
        Detect GPU hardware.

        Returns:
            Tuple[bool, Optional[Dict[str, Any]]]: Has GPU and GPU info
        """
        has_gpu = False
        gpu_info = None
        system = self._platform_info.system

        try:
            if system == "Linux":
                # Check for NVIDIA GPU
                try:
                    output = (
                        subprocess.check_output(
                            [
                                "nvidia-smi",
                                "--query-gpu=name,memory.total,driver_version",
                                "--format=csv,noheader",
                            ]
                        )
                        .decode()
                        .strip()
                    )
                    if output:
                        parts = output.split(", ")
                        has_gpu = True
                        gpu_info = {
                            "name": parts[0],
                            "memory": parts[1],
                            "driver_version": parts[2],
                            "vendor": "NVIDIA",
                        }
                except (subprocess.SubprocessError, FileNotFoundError):
                    # No NVIDIA GPU or nvidia-smi not available
                    pass

                # Check for AMD GPU if NVIDIA not found
                if not has_gpu:
                    try:
                        if os.path.exists("/sys/class/drm/"):
                            import glob

                            amd_cards = glob.glob("/sys/class/drm/card*/device/vendor")
                            for card in amd_cards:
                                with open(card, "r") as f:
                                    vendor_id = f.read().strip()
                                    if vendor_id == "0x1002":  # AMD vendor ID
                                        # Found AMD GPU
                                        card_path = os.path.dirname(card)
                                        name_path = os.path.join(
                                            card_path, "product_name"
                                        )
                                        if os.path.exists(name_path):
                                            with open(name_path, "r") as f:
                                                gpu_name = f.read().strip()
                                                has_gpu = True
                                                gpu_info = {
                                                    "name": gpu_name,
                                                    "vendor": "AMD",
                                                }
                                                break
                    except Exception:
                        pass

            elif system == "Darwin":  # macOS
                try:
                    output = (
                        subprocess.check_output(
                            ["system_profiler", "SPDisplaysDataType"]
                        )
                        .decode()
                        .strip()
                    )

                    # Parse output for GPU info
                    if "Chipset Model" in output:
                        chipset = re.search(r"Chipset Model: (.+)", output)
                        vendor = re.search(r"Vendor: (.+)", output)

                        if chipset:
                            gpu_name = chipset.group(1).strip()
                            has_gpu = True
                            gpu_info = {
                                "name": gpu_name,
                                "vendor": (
                                    vendor.group(1).strip() if vendor else "Unknown"
                                ),
                            }

                            # Check for Metal support
                            metal_check = subprocess.run(
                                [
                                    "system_profiler",
                                    "SPDisplaysDataType",
                                    "-detailLevel",
                                    "full",
                                ],
                                capture_output=True,
                                text=True,
                            )
                            if "Metal: Supported" in metal_check.stdout:
                                gpu_info["metal_support"] = True
                except Exception:
                    pass

            elif system == "Windows":
                try:
                    # Using WMI to get GPU info
                    import wmi

                    w = wmi.WMI()
                    for gpu in w.Win32_VideoController():
                        has_gpu = True
                        gpu_info = {
                            "name": gpu.Name,
                            "adapter_ram": gpu.AdapterRAM,
                            "driver_version": gpu.DriverVersion,
                            "vendor": "Unknown",
                        }

                        # Try to determine vendor
                        name_lower = gpu.Name.lower()
                        if "nvidia" in name_lower:
                            gpu_info["vendor"] = "NVIDIA"
                        elif "amd" in name_lower or "radeon" in name_lower:
                            gpu_info["vendor"] = "AMD"
                        elif "intel" in name_lower:
                            gpu_info["vendor"] = "Intel"

                        break  # Just get the first GPU for now
                except ImportError:
                    logger.warning(
                        "Failed to import wmi module for Windows GPU detection"
                    )
        except Exception as e:
            logger.warning(f"Failed to detect GPU: {e}")

        return has_gpu, gpu_info

    def get_optimal_thread_count(self) -> int:
        """
        Get the optimal thread count for the current hardware.

        Returns:
            int: Optimal thread count
        """
        if self._hardware_info is None:
            self.detect_hardware()

        # For M3 Max, use performance cores
        if self._hardware_info.is_m3_max and self._hardware_info.performance_core_count:
            return self._hardware_info.performance_core_count

        # For other hardware, use a reasonable default based on CPU count
        cpu_count = self._hardware_info.cpu_count
        if cpu_count <= 2:
            return cpu_count
        elif cpu_count <= 4:
            return cpu_count - 1
        else:
            return cpu_count - 2

    def get_hardware_capabilities(self) -> Dict[str, Any]:
        """
        Get hardware capabilities.

        Returns:
            Dict[str, Any]: Hardware capabilities
        """
        if self._hardware_info is None:
            self.detect_hardware()

        return {
            "is_m3_max": self._hardware_info.is_m3_max,
            "supports_metal": self._hardware_info.supports_metal,
            "has_neural_engine": self._hardware_info.has_neural_engine,
            "has_gpu": self._hardware_info.has_gpu,
            "cpu_count": self._hardware_info.cpu_count,
            "performance_cores": self._hardware_info.performance_core_count,
            "efficiency_cores": self._hardware_info.efficiency_core_count,
            "total_memory": self._hardware_info.total_memory,
            "available_memory": self._hardware_info.available_memory,
        }


# Singleton instance
_hardware_detector_instance = None


def get_hardware_detector() -> HardwareDetector:
    """Get the singleton hardware detector instance."""
    global _hardware_detector_instance

    if _hardware_detector_instance is None:
        _hardware_detector_instance = HardwareDetector()

    return _hardware_detector_instance


# Convenience functions
def detect_hardware() -> HardwareInfo:
    """Detect hardware information."""
    return get_hardware_detector().detect_hardware()


def get_optimal_thread_count() -> int:
    """Get the optimal thread count for the current hardware."""
    return get_hardware_detector().get_optimal_thread_count()


def get_hardware_capabilities() -> Dict[str, Any]:
    """Get hardware capabilities."""
    return get_hardware_detector().get_hardware_capabilities()


def is_m3_max() -> bool:
    """Check if running on M3 Max hardware."""
    hardware_info = detect_hardware()
    return hardware_info.is_m3_max
