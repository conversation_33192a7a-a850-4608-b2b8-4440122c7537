"""
Person Suit - Deployment Environment Detection Core (DE-1)

This module provides environment detection capabilities for the Person Suit framework,
enabling automatic identification of hardware, platform, and runtime environment.

Components:
- EnvironmentDetector: Core detection of hardware and platform characteristics
- PlatformInfo: Information about the current platform
- HardwareInfo: Information about the hardware (CPU, memory, etc.)
- RuntimeInfo: Information about the runtime environment

Related Files:
- platform_detector.py: Platform detection
- hardware_detector.py: Hardware detection
- runtime_detector.py: Runtime environment detection
- models.py: Data models for environment information

Dependencies:
- person_suit.core.infrastructure.monitoring: For metrics collection
- person_suit.core.infrastructure.configuration.environment: For environment types
"""

import logging
import threading
import time
from typing import Any, Dict

# Import hardware detection
from .hardware_detector import (
    get_hardware_detector,
    is_m3_max,
)

# Import models
from .models import (
    EnvironmentData,
)

# Import platform detection
from .platform_detector import (
    get_platform_detector,
)

# Import runtime detection
from .runtime_detector import (
    get_runtime_detector,
    is_container,
    is_development,
    is_production,
)

# Import from infrastructure for environment types
from ...infrastructure.configuration.environment.handler import (
    Environment,
)

# Configure logger
logger = logging.getLogger("person_suit.deployment.detection")


class EnvironmentDetector:
    """
    Environment detection for hardware, platform, and runtime.

    This class provides comprehensive environment detection capabilities,
    identifying hardware characteristics, platform details, and runtime
    environment information.
    """

    _instance = None
    _lock = threading.RLock()

    def __new__(cls):
        """Singleton implementation."""
        with cls._lock:
            if not cls._instance:
                cls._instance = super(EnvironmentDetector, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self):
        """Initialize the environment detector."""
        if self._initialized:
            return

        with self._lock:
            if not self._initialized:
                self._platform_detector = get_platform_detector()
                self._hardware_detector = get_hardware_detector()
                self._runtime_detector = get_runtime_detector()
                self._environment_data = None
                self._initialized = True

                logger.debug("Environment detector initialized")

    def detect_environment(self, force_refresh: bool = False) -> EnvironmentData:
        """
        Detect the current environment.

        Args:
            force_refresh: Force refresh of cached data

        Returns:
            EnvironmentData: Comprehensive environment data
        """
        # Return cached data if available and not forcing refresh
        if self._environment_data is not None and not force_refresh:
            return self._environment_data

        start_time = time.time()

        # Detect platform
        platform_info = self._platform_detector.detect_platform()

        # Detect hardware
        hardware_info = self._hardware_detector.detect_hardware()

        # Detect runtime
        runtime_info = self._runtime_detector.detect_runtime()

        # Create environment data
        self._environment_data = EnvironmentData(
            platform=platform_info,
            hardware=hardware_info,
            runtime=runtime_info,
            detection_time=time.time() - start_time,
        )

        logger.info(
            f"Environment detection completed in {self._environment_data.detection_time:.2f}s"
        )
        logger.info(
            f"Detected platform: {platform_info.system} {platform_info.release}"
        )
        logger.info(
            f"Detected hardware: {hardware_info.cpu_brand or 'Unknown'} with {hardware_info.cpu_count} cores"
        )
        logger.info(f"Detected runtime environment: {runtime_info.environment.name}")

        return self._environment_data

    def get_environment_info(self) -> Dict[str, Any]:
        """
        Get comprehensive environment information.

        Returns:
            Dict[str, Any]: Environment information
        """
        if self._environment_data is None:
            self.detect_environment()

        return self._environment_data.to_dict()

    def get_os_details(self) -> Dict[str, Any]:
        """
        Get detailed operating system information.

        Returns:
            Dict[str, Any]: Operating system details
        """
        return self._platform_detector.get_os_details()

    def get_hardware_capabilities(self) -> Dict[str, Any]:
        """
        Get hardware capabilities.

        Returns:
            Dict[str, Any]: Hardware capabilities
        """
        return self._hardware_detector.get_hardware_capabilities()

    def get_network_info(self) -> Dict[str, Any]:
        """
        Get network information.

        Returns:
            Dict[str, Any]: Network information
        """
        return self._runtime_detector.get_network_info()

    def get_environment_variables(self) -> Dict[str, str]:
        """
        Get environment variables.

        Returns:
            Dict[str, str]: Environment variables
        """
        return self._runtime_detector.get_environment_variables()


# Singleton instance
_environment_detector_instance = None


def get_environment_detector() -> EnvironmentDetector:
    """Get the singleton environment detector instance."""
    global _environment_detector_instance

    if _environment_detector_instance is None:
        _environment_detector_instance = EnvironmentDetector()

    return _environment_detector_instance


# Convenience functions
def detect_environment(force_refresh: bool = False) -> EnvironmentData:
    """Detect the current environment."""
    return get_environment_detector().detect_environment(force_refresh)


def is_m3_max() -> bool:
    """Check if running on M3 Max."""
    detector = get_environment_detector()
    env_data = detector.detect_environment()
    return env_data.hardware.is_m3_max


def is_container() -> bool:
    """Check if running in a container."""
    detector = get_environment_detector()
    env_data = detector.detect_environment()
    return env_data.runtime.is_container


def is_development() -> bool:
    """Check if running in development environment."""
    detector = get_environment_detector()
    env_data = detector.detect_environment()
    return env_data.runtime.environment == Environment.DEVELOPMENT


def is_production() -> bool:
    """Check if running in production environment."""
    detector = get_environment_detector()
    env_data = detector.detect_environment()
    return env_data.runtime.environment == Environment.PRODUCTION
