"""
Person Suit - Deployment Environment Detection Module (DE-1)

This module provides environment detection capabilities for the Person Suit framework,
enabling automatic identification of hardware, platform, and runtime environment.

Components:
- PlatformDetector: Detects platform characteristics
- HardwareDetector: Detects hardware capabilities
- RuntimeDetector: Detects runtime environment
- EnvironmentData: Comprehensive environment data

The Environment Detection module enables Person Suit to adapt to different
hardware and platform environments, with special focus on optimizing for
Apple M3 Max hardware.
"""

# Import from core module
from .core import (  # Classes; Functions
    EnvironmentDetector,
    detect_environment,
    get_environment_detector,
    is_container,
    is_development,
    is_m3_max,
    is_production,
)
# Import models directly
from .models import (
    Environment, # Re-export Environment from models
    EnvironmentData,
    HardwareInfo,
    PlatformInfo,
    RuntimeInfo,
)

# Import hardware detection
from .hardware_detector import (
    HardwareDetector,
    detect_hardware,
    get_hardware_capabilities,
    get_hardware_detector,
    get_optimal_thread_count,
)

# Import platform detection
from .platform_detector import (
    PlatformDetector,
    detect_platform,
    get_os_details,
    get_platform_detector,
)

# Import runtime detection
from .runtime_detector import (
    RuntimeDetector,
    detect_runtime,
    get_environment_variables,
    get_network_info,
    get_runtime_detector,
    is_ci,
)

# Version info
__version__ = "0.1.0"
