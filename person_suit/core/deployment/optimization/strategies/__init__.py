"""
Person Suit - Optimization Strategies Package (DE-1)

This package provides hardware-specific optimization strategies for the Person Suit framework,
enhancing performance by adapting to the specific characteristics of the
detected hardware environment.

Components:
- OptimizationStrategy: Base class for all optimization strategies
- MemoryOptimizationStrategy: Memory usage optimization
- ComputeOptimizationStrategy: CPU usage and computation optimization
- M3MaxOptimizationStrategy: Apple M3 Max-specific optimizations

Usage:
    # Create a strategy instance
    from person_suit.core.deployment.detection import detect_environment
    from person_suit.core.deployment.optimization.strategies import (
        MemoryOptimizationStrategy,
        ComputeOptimizationStrategy,
        M3MaxOptimizationStrategy
    )

    # Get environment data
    env_data = detect_environment()

    # Create strategy instance
    memory_strategy = MemoryOptimizationStrategy(env_data.hardware)
    compute_strategy = ComputeOptimizationStrategy(env_data.hardware)

    # Apply optimizations
    optimized_data = memory_strategy.apply(data)
    optimized_function = compute_strategy.optimize_function(function)
"""

from .base import (
    OptimizationStrategy,
)
from .compute import (
    ComputeOptimizationStrategy,
)
from .m3_max import (
    M3MaxOptimizationStrategy,
)
from .memory import (
    MemoryOptimizationStrategy,
)

__all__ = [
    "OptimizationStrategy",
    "MemoryOptimizationStrategy",
    "ComputeOptimizationStrategy",
    "M3MaxOptimizationStrategy",
]
