"""
Person Suit - Optimization Strategy Base (DE-1)

This module provides the base class for optimization strategies in the Person Suit framework,
defining the interface that all specific optimization strategies must implement.

Classes:
- OptimizationStrategy: Base class for all optimization strategies

Related Files:
- models.py: Optimization data models and enumerations
- memory.py: Memory optimization strategy
- compute.py: Compute optimization strategy
- m3_max.py: M3 Max-specific optimization strategy
- ../manager.py: Hardware-specific optimization management

Dependencies:
- person_suit.core.deployment.detection: For hardware information
"""

import logging
from typing import Any, Dict, Generic, TypeVar

from ...detection import HardwareInfo

# Type variables
T = TypeVar("T")

# Configure logger
logger = logging.getLogger("person_suit.deployment.optimization.strategies.base")


class OptimizationStrategy(Generic[T]):
    """
    Base class for optimization strategies.

    Defines the interface for implementing hardware-specific optimization
    strategies that can be applied to various components of the system.
    """

    def __init__(self, hardware_info: HardwareInfo):
        """
        Initialize the optimization strategy.

        Args:
            hardware_info: Hardware information
        """
        self._hardware_info = hardware_info
        self._initialized = False

    def initialize(self) -> bool:
        """
        Initialize the optimization strategy.

        Returns:
            bool: True if initialization was successful
        """
        self._initialized = True
        return True

    def apply(self, target: T) -> T:
        """
        Apply optimizations to a target.

        Args:
            target: Target to optimize

        Returns:
            Optimized target
        """
        if not self._initialized:
            self.initialize()
        return target

    def is_applicable(self) -> bool:
        """
        Check if this optimization strategy is applicable.

        Returns:
            bool: True if applicable
        """
        return True

    def get_optimization_parameters(self) -> Dict[str, Any]:
        """
        Get optimization parameters.

        Returns:
            Dict[str, Any]: Optimization parameters
        """
        return {}
