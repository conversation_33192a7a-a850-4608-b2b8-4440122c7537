"""
Person Suit - Deployment Environment Optimization Package (DE-1)

This package provides hardware-specific optimizations for the Person Suit framework,
enhancing performance by adapting to the specific characteristics of the
detected hardware environment, with special focus on M3 Max optimizations.

Components:
- OptimizationType: Types of hardware optimizations
- OptimizationSetting: Individual optimization setting
- OptimizationProfile: Hardware-specific optimization profile
- OptimizationStrategy: Base class for optimization strategies
- MemoryOptimizationStrategy: Memory usage optimization
- ComputeOptimizationStrategy: CPU usage optimization
- M3MaxOptimizationStrategy: Apple M3 Max optimizations
- HardwareOptimizer: Hardware-specific optimization management

Usage:
    # Basic usage (automatic optimization)
    from person_suit.core.deployment.optimization import optimize, optimize_function

    # Apply optimizations to a value
    optimized_data = optimize(data)

    # Create an optimized function
    @optimize_function
    def process_data(data):
        # Function implementation
        return result

    # Advanced usage (manual optimization)
    from person_suit.core.deployment.optimization import (
        get_hardware_optimizer,
        OptimizationType
    )

    # Get the optimizer
    optimizer = get_hardware_optimizer()

    # Initialize
    optimizer.initialize()

    # Apply specific optimizations
    result = optimizer.optimize(data, OptimizationType.MEMORY)
"""

# Import from core module
from .core import (  # Enums and models; Strategy classes; Manager and convenience functions
    ComputeOptimizationStrategy,
    HardwareOptimizer,
    M3MaxOptimizationStrategy,
    MemoryOptimizationStrategy,
    OptimizationProfile,
    OptimizationSetting,
    OptimizationStrategy,
    OptimizationType,
    get_hardware_optimizer,
    is_optimization_available,
    optimize,
    optimize_function,
)

# Define exported symbols
__all__ = [
    # Enums and models
    "OptimizationType",
    "OptimizationSetting",
    "OptimizationProfile",
    # Strategy classes
    "OptimizationStrategy",
    "MemoryOptimizationStrategy",
    "ComputeOptimizationStrategy",
    "M3MaxOptimizationStrategy",
    # Manager and convenience functions
    "HardwareOptimizer",
    "get_hardware_optimizer",
    "optimize",
    "optimize_function",
    "is_optimization_available",
]
