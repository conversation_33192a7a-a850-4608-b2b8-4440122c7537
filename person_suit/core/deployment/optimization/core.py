"""
Person Suit - Deployment Environment Optimization Core (DE-1)

This module provides hardware-specific optimizations for the Person Suit framework,
enhancing performance by adapting to the specific characteristics of the
detected hardware environment, with special focus on M3 Max optimizations.

Components:
- OptimizationType: Types of hardware optimizations
- OptimizationSetting: Individual optimization setting
- OptimizationProfile: Hardware-specific optimization profile
- OptimizationStrategy: Base class for optimization strategies
- MemoryOptimizationStrategy: Memory usage optimization
- ComputeOptimizationStrategy: CPU usage optimization
- M3MaxOptimizationStrategy: Apple M3 Max optimizations
- HardwareOptimizer: Hardware-specific optimization management

Related Files:
- detection.py: Environment detection providing hardware/platform info
- resources.py: Resource management based on environment
- configuration.py: Environment-specific configuration
- adapters.py: Deployment adapters for different environments
- security.py: Security boundaries based on environment

Dependencies:
- person_suit.core.deployment.detection: For hardware detection
- person_suit.core.infrastructure.monitoring: For metrics collection
"""

# Import from manager.py
from .manager import (
    HardwareOptimizer,
    get_hardware_optimizer,
    is_optimization_available,
    optimize,
    optimize_function,
)

# Import from models.py
from .models import (
    OptimizationProfile,
    OptimizationSetting,
    OptimizationType,
)

# Import from strategies
from .strategies import (
    ComputeOptimizationStrategy,
    M3MaxOptimizationStrategy,
    MemoryOptimizationStrategy,
    OptimizationStrategy,
)

# Define exported symbols
__all__ = [
    # Enums and models
    "OptimizationType",
    "OptimizationSetting",
    "OptimizationProfile",
    # Strategy classes
    "OptimizationStrategy",
    "MemoryOptimizationStrategy",
    "ComputeOptimizationStrategy",
    "M3MaxOptimizationStrategy",
    # Manager and convenience functions
    "HardwareOptimizer",
    "get_hardware_optimizer",
    "optimize",
    "optimize_function",
    "is_optimization_available",
]
