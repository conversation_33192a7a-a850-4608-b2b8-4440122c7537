"""
Person Suit - Hardware Optimization Manager (DE-1)

This module provides the central hardware optimization management for the Person Suit framework,
coordinating optimization strategies, profiles, and applying optimizations to targets.

Classes:
- HardwareOptimizer: Hardware-specific optimization management

Related Files:
- models.py: Optimization data models and enumerations
- strategies/base.py: Base class for optimization strategies
- strategies/memory.py: Memory optimization strategy
- strategies/compute.py: Compute optimization strategy
- strategies/m3_max.py: M3 Max-specific optimization strategy

Dependencies:
- person_suit.core.deployment.detection: For environment detection
"""

import logging
import threading
from typing import Any, Callable, List, Optional

from ..detection import detect_environment
from .models import (
    OptimizationProfile,
    OptimizationSetting,
    OptimizationType,
)
from .strategies import (
    ComputeOptimizationStrategy,
    M3MaxOptimizationStrategy,
    MemoryOptimizationStrategy,
)

# Configure logger
logger = logging.getLogger("person_suit.deployment.optimization.manager")


class HardwareOptimizer:
    """
    Hardware-specific optimization management.

    Manages optimization strategies for different hardware platforms,
    applying the most appropriate optimizations based on the detected
    hardware capabilities.
    """

    _instance = None
    _lock = threading.RLock()

    def __new__(cls):
        """Singleton implementation."""
        with cls._lock:
            if not cls._instance:
                cls._instance = super(HardwareOptimizer, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self):
        """Initialize hardware optimizer."""
        if self._initialized:
            return

        with self._lock:
            if not self._initialized:
                self._env_prefix = "PERSON_SUIT_"
                self._environment_data = None
                self._optimization_strategies = {}
                self._optimization_profiles = {}
                self._active_profile = None
                self._initialized = False

                logger.debug("Hardware optimizer created (not initialized)")

    def initialize(self) -> bool:
        """
        Initialize hardware optimizer.

        Returns:
            bool: True if initialization was successful
        """
        if self._initialized:
            logger.warning("Hardware optimizer already initialized")
            return True

        try:
            # Detect environment
            self._environment_data = detect_environment()

            # Register optimization strategies
            self._register_optimization_strategies()

            # Load optimization profiles
            self._load_optimization_profiles()

            # Select and activate profile
            self._select_optimization_profile()

            self._initialized = True
            logger.info("Hardware optimizer initialized")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize hardware optimizer: {e}")
            return False

    def _register_optimization_strategies(self) -> None:
        """Register available optimization strategies."""
        hardware_info = self._environment_data.hardware

        # Register general strategies
        self._optimization_strategies["memory"] = MemoryOptimizationStrategy(
            hardware_info
        )
        self._optimization_strategies["compute"] = ComputeOptimizationStrategy(
            hardware_info
        )

        # Register M3 Max strategy if applicable
        if hardware_info.is_m3_max:
            self._optimization_strategies["m3_max"] = M3MaxOptimizationStrategy(
                hardware_info
            )
            logger.info("Registered M3 Max optimization strategy")

    def _load_optimization_profiles(self) -> None:
        """Load optimization profiles."""
        # TODO: In a real implementation, this would load profiles from configuration files

        # Create some basic profiles for demonstration
        if "m3_max" in self._optimization_strategies:
            # Create M3 Max profile with appropriate optimizations
            self._create_m3_max_profile()

        # Create general profile for other hardware
        self._create_general_profile()

        logger.debug(f"Loaded {len(self._optimization_profiles)} optimization profiles")

    def _create_m3_max_profile(self) -> None:
        """Create M3 Max optimization profile."""
        # Get parameters from M3 Max strategy for optimization settings
        m3_strategy = self._optimization_strategies["m3_max"]
        params = m3_strategy.get_optimization_parameters()

        optimizations = {}

        # Add Neural Engine optimization if available
        if params.get("neural_engine_available", False):
            optimizations["neural_engine"] = OptimizationSetting(
                name="neural_engine",
                description="Leverage Neural Engine for AI/ML operations",
                enabled=True,
                optimization_type=OptimizationType.NEURAL_ENGINE,
                impact_level=5,
            )

        # Add Metal acceleration if available
        if params.get("metal_available", False):
            optimizations["metal_acceleration"] = OptimizationSetting(
                name="metal_acceleration",
                description="Enable Metal GPU acceleration for compute-intensive tasks",
                enabled=True,
                optimization_type=OptimizationType.METAL,
                impact_level=4,
            )

        # Add hybrid core scheduling
        if (
            params.get("performance_cores", 0) > 0
            and params.get("efficiency_cores", 0) > 0
        ):
            optimizations["hybrid_core_scheduling"] = OptimizationSetting(
                name="hybrid_core_scheduling",
                description="Optimize task distribution across performance and efficiency cores",
                enabled=True,
                optimization_type=OptimizationType.PARALLEL,
                impact_level=3,
            )

        # Create and register the profile
        profile = OptimizationProfile(
            name="m3_max",
            description="Optimizations for Apple M3 Max hardware",
            target_hardware="Apple M3 Max",
            optimizations=optimizations,
        )

        self._optimization_profiles[profile.name] = profile
        logger.debug(
            f"Created M3 Max optimization profile with {len(optimizations)} optimizations"
        )

    def _create_general_profile(self) -> None:
        """Create general optimization profile for other hardware."""
        optimizations = {
            "memory_pooling": OptimizationSetting(
                name="memory_pooling",
                description="Pool memory allocations to reduce fragmentation",
                enabled=True,
                optimization_type=OptimizationType.MEMORY,
                impact_level=3,
            ),
            "parallel_computation": OptimizationSetting(
                name="parallel_computation",
                description="Enable multi-threaded computation when applicable",
                enabled=True,
                optimization_type=OptimizationType.PARALLEL,
                impact_level=3,
            ),
            "disk_caching": OptimizationSetting(
                name="disk_caching",
                description="Cache frequently accessed data on disk",
                enabled=True,
                optimization_type=OptimizationType.CACHING,
                impact_level=2,
            ),
        }

        # Create and register the profile
        profile = OptimizationProfile(
            name="general",
            description="General optimizations for standard hardware",
            target_hardware="Generic",
            optimizations=optimizations,
        )

        self._optimization_profiles[profile.name] = profile
        logger.debug(
            f"Created general optimization profile with {len(optimizations)} optimizations"
        )

    def _select_optimization_profile(self) -> None:
        """Select and activate optimization profile."""
        hardware_info = self._environment_data.hardware

        # Select M3 Max profile if applicable
        if hardware_info.is_m3_max and "m3_max" in self._optimization_profiles:
            self.activate_profile("m3_max")
            logger.info("Activated M3 Max optimization profile")
        else:
            # Select default profile
            default_profile = self._get_default_profile()
            if default_profile:
                self.activate_profile(default_profile)
                logger.info(
                    f"Activated default optimization profile: {default_profile}"
                )
            else:
                logger.warning("No suitable optimization profile found")

    def get_optimization_profiles(self) -> List[OptimizationProfile]:
        """
        Get all available optimization profiles.

        Returns:
            List[OptimizationProfile]: Available profiles
        """
        if not self._initialized:
            self.initialize()

        return list(self._optimization_profiles.values())

    def get_profile(self, name: str) -> Optional[OptimizationProfile]:
        """
        Get profile by name.

        Args:
            name: Profile name

        Returns:
            OptimizationProfile: Profile if found, None otherwise
        """
        if not self._initialized:
            self.initialize()

        return self._optimization_profiles.get(name)

    def add_profile(self, profile: OptimizationProfile) -> bool:
        """
        Add a new optimization profile.

        Args:
            profile: Profile to add

        Returns:
            bool: True if profile was added successfully
        """
        if not self._initialized:
            self.initialize()

        with self._lock:
            # Validate profile
            if not profile.name or not profile.optimizations:
                logger.warning("Invalid optimization profile")
                return False

            self._optimization_profiles[profile.name] = profile
            logger.debug(f"Added optimization profile: {profile.name}")
            return True

    def activate_profile(self, name: str) -> bool:
        """
        Activate an optimization profile.

        Args:
            name: Profile name

        Returns:
            bool: True if profile was activated successfully
        """
        if not self._initialized:
            self.initialize()

        with self._lock:
            profile = self._optimization_profiles.get(name)
            if not profile:
                logger.warning(f"Optimization profile not found: {name}")
                return False

            # Deactivate current profile
            if self._active_profile:
                self._active_profile.is_active = False

            # Activate new profile
            profile.is_active = True
            self._active_profile = profile

            logger.info(f"Activated optimization profile: {name}")
            return True

    def get_active_profile(self) -> Optional[OptimizationProfile]:
        """
        Get active optimization profile.

        Returns:
            OptimizationProfile: Active profile if available, None otherwise
        """
        if not self._initialized:
            self.initialize()

        return self._active_profile

    def _get_default_profile(self) -> Optional[str]:
        """
        Get default profile name for current hardware.

        Returns:
            str: Default profile name if available, None otherwise
        """
        if self._environment_data.hardware.is_m3_max:
            return "m3_max" if "m3_max" in self._optimization_profiles else None

        # Default to general profile
        return "general" if "general" in self._optimization_profiles else None

    def optimize(self, target: Any, optimization_type: OptimizationType = None) -> Any:
        """
        Apply optimizations to a target.

        Args:
            target: Target to optimize
            optimization_type: Specific optimization type to apply

        Returns:
            Optimized target
        """
        if not self._initialized:
            self.initialize()

        # If no active profile, return target as is
        if not self._active_profile:
            return target

        # Apply optimizations from active profile
        result = target

        # Filter strategies by optimization type if specified
        strategies = self._optimization_strategies
        if optimization_type:
            strategies = {
                name: strategy
                for name, strategy in strategies.items()
                if any(
                    opt.optimization_type == optimization_type
                    for opt in self._active_profile.optimizations.values()
                )
            }

        # Apply applicable strategies
        for name, strategy in strategies.items():
            if strategy.is_applicable():
                try:
                    result = strategy.apply(result)
                except Exception as e:
                    logger.warning(f"Failed to apply optimization strategy {name}: {e}")

        return result

    def optimize_function(self, func: Callable) -> Callable:
        """
        Create an optimized wrapper for a function.

        Args:
            func: Function to optimize

        Returns:
            Callable: Optimized function wrapper
        """
        if not self._initialized:
            self.initialize()

        # Return original function if no active profile
        if not self._active_profile:
            return func

        # Create wrapper function
        def optimized_wrapper(*args, **kwargs):
            # Apply optimizations to function arguments
            optimized_args = [self.optimize(arg) for arg in args]
            optimized_kwargs = {k: self.optimize(v) for k, v in kwargs.items()}

            # Call the original function with optimized arguments
            result = func(*optimized_args, **optimized_kwargs)

            # Optimize the result if applicable
            return self.optimize(result)

        return optimized_wrapper


# Singleton instance
_hardware_optimizer_instance = None


def get_hardware_optimizer() -> HardwareOptimizer:
    """Get the singleton hardware optimizer instance."""
    global _hardware_optimizer_instance

    if _hardware_optimizer_instance is None:
        _hardware_optimizer_instance = HardwareOptimizer()

    return _hardware_optimizer_instance


# Convenience functions
def optimize(target: Any, optimization_type: OptimizationType = None) -> Any:
    """Apply optimizations to a target."""
    return get_hardware_optimizer().optimize(target, optimization_type)


def optimize_function(func: Callable) -> Callable:
    """Create an optimized wrapper for a function."""
    return get_hardware_optimizer().optimize_function(func)


def is_optimization_available(optimization_type: OptimizationType) -> bool:
    """Check if a specific optimization type is available."""
    optimizer = get_hardware_optimizer()
    profile = optimizer.get_active_profile()
    if not profile:
        return False
    return any(
        opt.optimization_type == optimization_type and opt.enabled
        for opt in profile.optimizations.values()
    )
