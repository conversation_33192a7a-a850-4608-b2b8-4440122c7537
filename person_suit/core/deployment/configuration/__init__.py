"""
Person Suit - Deployment Environment Configuration Package (DE-1)

This package provides runtime configuration for the Person Suit framework,
tailoring configuration settings to the specific characteristics of the
detected deployment environment.

Components:
- RuntimeConfiguration: Environment-specific configuration management
- ConfigurationProfile: Environment-specific configuration profile
- ProfileManager: Management of configuration profiles

Usage:
    # Get runtime configuration
    runtime_config = get_runtime_configuration()

    # Get environment-specific setting
    value = get_environment_setting("section", "key", default="default")

    # Set environment-specific setting
    set_environment_setting("section", "key", "value")

    # Get active profile
    profile = get_active_profile()

    # Refresh configuration
    refresh_configuration()
"""

# Import from core module
from .core import (  # Classes; Functions
    ConfigurationProfile,
    ProfileManager,
    RuntimeConfiguration,
    get_active_profile,
    get_environment_setting,
    get_runtime_configuration,
    refresh_configuration,
    set_environment_setting,
)

# Define exported symbols
__all__ = [
    # Classes
    "ConfigurationProfile",
    "ProfileManager",
    "RuntimeConfiguration",
    # Functions
    "get_runtime_configuration",
    "get_environment_setting",
    "set_environment_setting",
    "get_active_profile",
    "refresh_configuration",
]
