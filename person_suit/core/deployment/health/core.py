"""
Person Suit - Deployment Health Monitoring Core (DE-1)

This module provides health monitoring capabilities for the Person Suit framework,
enabling detection and reporting of system health issues.

Components:
- HealthSeverity: Severity levels for health issues
- HealthIssue: Health issue details
- HealthReport: Comprehensive health report
- HealthStatus: Overall health status
- HealthCheck: Base class for health checks
- ResourceHealthCheck: Checks resource health
- SecurityHealthCheck: Checks security health
- SystemHealthCheck: Checks system health
- HealthMonitor: Central health monitoring

Related Files:
- models.py: Health data models and enumerations
- checks/base.py: Base health check implementation
- checks/resource.py: Resource health check implementation
- checks/security.py: Security health check implementation
- checks/system.py: System health check implementation
- monitor.py: Health monitor implementation

Dependencies:
- person_suit.core.deployment.detection: For environment detection
- person_suit.core.deployment.resources: For resource management
- person_suit.core.deployment.security: For security management
- person_suit.core.deployment.validation: For validation
"""

# Import from checks/base.py
from .checks.base import HealthCheck

# Import from checks/resource.py
from .checks.resource import ResourceHealthCheck

# Import from checks/security.py
from .checks.security import SecurityHealthCheck

# Import from checks/system.py
from .checks.system import SystemHealthCheck

# Import from models.py
from .models import (
    HealthIssue,
    HealthReport,
    HealthSeverity,
    HealthStatus,
)

# Import from monitor.py
from .monitor import HealthMonitor, get_health_monitor


# Convenience functions
def check_health() -> HealthReport:
    """Check system health."""
    return get_health_monitor().check_health()


# Define exported symbols
__all__ = [
    # Models
    "HealthSeverity",
    "HealthIssue",
    "HealthReport",
    "HealthStatus",
    # Health checks
    "HealthCheck",
    "ResourceHealthCheck",
    "SecurityHealthCheck",
    "SystemHealthCheck",
    # Monitor
    "HealthMonitor",
    "get_health_monitor",
    # Convenience functions
    "check_health",
]
