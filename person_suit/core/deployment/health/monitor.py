"""
Person Suit - Health Monitor (DE-1)

This module provides health monitoring for the Person Suit framework,
tracking system health and generating health reports.

Classes:
- HealthMonitor: Central health monitoring system

Functions:
- get_health_monitor: Get the singleton health monitor instance
- check_health: Convenience function to check system health

Related Files:
- models.py: Health monitoring data models
- checks.py: Health check implementations
"""

import logging
import threading
import time
from collections import deque
from typing import Dict, List, Optional

from .checks import (
    HealthCheck,  # get_default_health_checks # Removed non-existent function
)
from .models import (
    HealthIssue,
    HealthReport,
    HealthSeverity,
    HealthStatus,
    create_summary_from_status,
    determine_status_from_issues,
)

# Configure logger
logger = logging.getLogger("person_suit.deployment.health.monitor")


class HealthMonitor:
    """
    Health monitor for the Person Suit framework.

    Coordinates health checks, generates health reports, and tracks health history.
    """

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        """Implement singleton pattern."""
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(HealthMonitor, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self):
        """Initialize the health monitor (only once for the singleton)."""
        if self._initialized:
            return

        self._initialized = True
        self._health_checks: Dict[str, HealthCheck] = {}

        # Report history
        self._report_history_size = 50
        self._reports = deque(maxlen=self._report_history_size)
        self._current_report = None

        # Background monitoring
        self._monitoring_thread = None
        self._monitoring_interval = 60  # Seconds
        self._monitoring_enabled = False
        self._stop_event = threading.Event()

        # Register default health checks - Functionality removed as get_default_health_checks doesn't exist
        # for check in get_default_health_checks():
        #     self.register_health_check(check)

        # Initialize the first health report
        self._current_report = self._create_blank_report()

        logger.info("Health monitor initialized")

    def register_health_check(self, health_check: HealthCheck) -> None:
        """
        Register a health check with the monitor.

        Args:
            health_check: The health check to register
        """
        if not isinstance(health_check, HealthCheck):
            raise TypeError("health_check must be an instance of HealthCheck")

        self._health_checks[health_check.check_id] = health_check
        logger.debug(f"Registered health check: {health_check.check_id}")

    def unregister_health_check(self, check_id: str) -> bool:
        """
        Unregister a health check by ID.

        Args:
            check_id: The ID of the health check to unregister

        Returns:
            bool: True if unregistered, False if not found
        """
        if check_id in self._health_checks:
            del self._health_checks[check_id]
            logger.debug(f"Unregistered health check: {check_id}")
            return True
        return False

    def get_health_check(self, check_id: str) -> Optional[HealthCheck]:
        """
        Get a health check by ID.

        Args:
            check_id: The ID of the health check to get

        Returns:
            Optional[HealthCheck]: The health check or None if not found
        """
        return self._health_checks.get(check_id)

    def list_health_checks(self) -> List[str]:
        """
        List all registered health check IDs.

        Returns:
            List[str]: List of health check IDs
        """
        return list(self._health_checks.keys())

    def configure_health_check(self, check_id: str, **config) -> bool:
        """
        Configure a health check.

        Args:
            check_id: The ID of the health check to configure
            **config: Configuration parameters

        Returns:
            bool: True if configured, False if not found
        """
        check = self.get_health_check(check_id)
        if check:
            check.configure(**config)
            logger.debug(f"Configured health check: {check_id}")
            return True
        return False

    def check_health(self) -> HealthReport:
        """
        Run all health checks and generate a health report.

        Returns:
            HealthReport: The generated health report
        """
        report = self._create_blank_report()
        all_issues = []
        all_metrics = []

        # Run each health check
        for check_id, check in self._health_checks.items():
            if check.should_check():
                try:
                    logger.debug(f"Running health check: {check_id}")
                    issues, metrics = check.check()

                    # Add issues and metrics to the lists
                    all_issues.extend(issues)
                    all_metrics.extend(metrics)

                    # Log issues
                    for issue in issues:
                        if issue.severity == HealthSeverity.CRITICAL:
                            logger.critical(f"Health critical issue: {issue.message}")
                        elif issue.severity == HealthSeverity.ERROR:
                            logger.error(f"Health error issue: {issue.message}")
                        elif issue.severity == HealthSeverity.WARNING:
                            logger.warning(f"Health warning issue: {issue.message}")
                        else:
                            logger.info(f"Health info issue: {issue.message}")

                except Exception as e:
                    logger.error(
                        f"Error running health check {check_id}: {e}", exc_info=True
                    )

                    # Add an issue for the check failure
                    error_issue = HealthIssue(
                        component=check.name,
                        message=f"Health check failed: {str(e)}",
                        severity=HealthSeverity.ERROR,
                        check_id=check_id,
                        remediation="Check logs for more information and fix the underlying issue.",
                    )

                    all_issues.append(error_issue)

        # Add issues and metrics to the report
        report.issues = all_issues
        report.metrics = all_metrics

        # Determine component status
        component_status = {}

        # Group issues by component
        component_issues = {}
        for issue in all_issues:
            if issue.component not in component_issues:
                component_issues[issue.component] = []
            component_issues[issue.component].append(issue)

        # Determine status for each component
        for component, issues in component_issues.items():
            component_status[component] = determine_status_from_issues(issues)

        report.components = component_status

        # Determine overall status
        report.status = determine_status_from_issues(all_issues)

        # Create a summary
        issue_counts = {
            "critical": report.critical_issue_count,
            "error": report.error_issue_count,
            "warning": report.warning_issue_count,
            "total": len(report.issues),
        }

        report.summary = create_summary_from_status(
            report.status, issue_counts, report.components
        )

        # Update current report and history
        self._current_report = report
        self._reports.append(report)

        return report

    def get_current_report(self) -> HealthReport:
        """
        Get the most recent health report.

        Returns:
            HealthReport: The current health report
        """
        return self._current_report

    def get_report_history(self) -> List[HealthReport]:
        """
        Get the history of health reports.

        Returns:
            List[HealthReport]: List of health reports
        """
        return list(self._reports)

    def start_background_monitoring(self, interval: int = 60) -> bool:
        """
        Start background health monitoring.

        Args:
            interval: Seconds between health checks

        Returns:
            bool: True if started, False if already running
        """
        if self._monitoring_thread is not None and self._monitoring_thread.is_alive():
            logger.warning("Background monitoring is already running")
            return False

        # Set the monitoring interval
        self._monitoring_interval = max(10, interval)  # Min 10 seconds

        # Create and start the monitoring thread
        self._stop_event.clear()
        self._monitoring_enabled = True
        self._monitoring_thread = threading.Thread(
            target=self._background_monitoring_loop,
            name="HealthMonitorThread",
            daemon=True,
        )
        self._monitoring_thread.start()

        logger.info(
            f"Started background health monitoring with interval {self._monitoring_interval}s"
        )
        return True

    def stop_background_monitoring(self) -> bool:
        """
        Stop background health monitoring.

        Returns:
            bool: True if stopped, False if not running
        """
        if self._monitoring_thread is None or not self._monitoring_thread.is_alive():
            logger.warning("Background monitoring is not running")
            return False

        # Stop the monitoring thread
        self._stop_event.set()
        self._monitoring_enabled = False

        # Wait for the thread to stop (with timeout)
        self._monitoring_thread.join(timeout=5.0)

        logger.info("Stopped background health monitoring")
        return True

    def _background_monitoring_loop(self) -> None:
        """Background monitoring thread function."""
        logger.debug("Background monitoring thread started")

        while not self._stop_event.is_set():
            try:
                # Check health
                self.check_health()

                # Sleep for the monitoring interval, but check for stop
                # event periodically to allow for quicker shutdown
                for _ in range(self._monitoring_interval):
                    if self._stop_event.is_set():
                        break
                    time.sleep(1)

            except Exception as e:
                logger.error(f"Error in background monitoring: {e}", exc_info=True)
                # Sleep a bit to avoid spamming errors
                time.sleep(5)

        logger.debug("Background monitoring thread stopped")

    def is_monitoring_enabled(self) -> bool:
        """
        Check if background monitoring is enabled.

        Returns:
            bool: True if enabled
        """
        return self._monitoring_enabled

    def get_system_health_status(self) -> HealthStatus:
        """
        Get the current system health status.

        Returns:
            HealthStatus: Current health status
        """
        return (
            self._current_report.status
            if self._current_report
            else HealthStatus.UNKNOWN
        )

    def _create_blank_report(self) -> HealthReport:
        """
        Create a blank health report.

        Returns:
            HealthReport: A blank health report
        """
        return HealthReport(
            timestamp=time.time(),
            status=HealthStatus.HEALTHY,
            components={},
            issues=[],
            metrics=[],
            summary="System health check pending",
            metadata={"source": "health_monitor"},
        )


# Singleton instance
_health_monitor_instance = None


def get_health_monitor() -> HealthMonitor:
    """
    Get the singleton health monitor instance.

    Returns:
        HealthMonitor: Health monitor instance
    """
    global _health_monitor_instance
    if _health_monitor_instance is None:
        _health_monitor_instance = HealthMonitor()
    return _health_monitor_instance


def check_health() -> HealthReport:
    """
    Convenience function to check system health.

    Returns:
        HealthReport: Current health report
    """
    return get_health_monitor().check_health()
