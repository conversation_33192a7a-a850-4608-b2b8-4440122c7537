"""
Person Suit - Base Health Check (DE-1)

This module provides the base health check class that all specific
health checks must inherit from.

Classes:
- HealthCheck: Abstract base class for health checks

Related Files:
- models.py: Health data models
- system.py: System health checks
- security.py: Security health checks
- resource.py: Resource health checks
"""

import logging
import time
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Tuple

from ..models import (
    HealthIssue,
    HealthMetric,
    HealthSeverity,
    HealthThreshold,
)

# Configure logger
logger = logging.getLogger("person_suit.deployment.health.checks")


class HealthCheck(ABC):
    """
    Abstract base class for all health checks.

    This class defines the interface that all health checks must implement,
    and provides common functionality for health checking.
    """

    def __init__(self, name: str, enabled: bool = True):
        """
        Initialize the health check.

        Args:
            name: The name of the health check
            enabled: Whether the health check is enabled by default
        """
        self.name = name
        self.enabled = enabled
        self.thresholds: Dict[str, HealthThreshold] = {}

    @abstractmethod
    def check(self) -> List[HealthIssue]:
        """
        Perform the health check.

        This method must be implemented by all health check classes.

        Returns:
            List[HealthIssue]: List of health issues found
        """
        pass

    def register_threshold(self, threshold: HealthThreshold) -> None:
        """
        Register a threshold for a metric.

        Args:
            threshold: The threshold to register
        """
        self.thresholds[threshold.metric_name] = threshold

    def execute(self) -> Tuple[List[HealthIssue], List[HealthMetric], float]:
        """
        Execute the health check with timing.

        Returns:
            Tuple of (issues, metrics, execution_time_ms)
        """
        if not self.enabled:
            logger.debug(f"Health check {self.name} is disabled, skipping")
            return [], [], 0

        start_time = time.time()

        try:
            # Execute the health check
            issues = self.check()

            # Calculate execution time
            execution_time_ms = (time.time() - start_time) * 1000

            # Log the result
            if issues:
                logger.info(
                    f"Health check {self.name} found {len(issues)} issues (in {execution_time_ms:.2f}ms)"
                )
            else:
                logger.debug(
                    f"Health check {self.name} found no issues (in {execution_time_ms:.2f}ms)"
                )

            return issues, [], execution_time_ms

        except Exception as e:
            # Calculate execution time even for failures
            execution_time_ms = (time.time() - start_time) * 1000

            # Log the error
            logger.error(
                f"Error executing health check {self.name}: {e}", exc_info=True
            )

            # Create an issue for the failure
            issue = HealthIssue(
                component=self.name,
                message=f"Health check failed: {str(e)}",
                severity=HealthSeverity.ERROR,
                details={"exception": str(e), "exception_type": type(e).__name__},
            )

            return [issue], [], execution_time_ms

    def evaluate_metric(self, metric: HealthMetric) -> Optional[HealthIssue]:
        """
        Evaluate a metric against registered thresholds.

        Args:
            metric: The metric to evaluate

        Returns:
            Optional[HealthIssue]: A health issue if the threshold is exceeded, None otherwise
        """
        # Check if we have a threshold for this metric
        if metric.name not in self.thresholds:
            return None

        threshold = self.thresholds[metric.name]
        exceeded, severity = threshold.evaluate(metric)

        if not exceeded or severity is None:
            return None

        # Create an issue for the threshold violation
        if threshold.comparison == "greater":
            message = f"{metric.name} value {metric.value} is too high"
            if metric.unit:
                message += f" ({metric.value} {metric.unit})"
        else:
            message = f"{metric.name} value {metric.value} is too low"
            if metric.unit:
                message += f" ({metric.value} {metric.unit})"

        # Include threshold values in details
        details = {
            "value": metric.value,
            "unit": metric.unit,
            "comparison": threshold.comparison,
        }

        if threshold.warning_threshold is not None:
            details["warning_threshold"] = threshold.warning_threshold
        if threshold.error_threshold is not None:
            details["error_threshold"] = threshold.error_threshold
        if threshold.critical_threshold is not None:
            details["critical_threshold"] = threshold.critical_threshold

        return HealthIssue(
            component=metric.component,
            message=message,
            severity=severity,
            details=details,
        )

    def record_metrics(self, metrics: List[HealthMetric]) -> List[HealthIssue]:
        """
        Record metrics and evaluate them against thresholds.

        Args:
            metrics: List of metrics to record and evaluate

        Returns:
            List[HealthIssue]: List of health issues found from metric threshold violations
        """
        issues = []

        for metric in metrics:
            issue = self.evaluate_metric(metric)
            if issue:
                issues.append(issue)

        return issues
