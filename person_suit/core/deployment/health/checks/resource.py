"""
Person Suit - Resource Health Check (DE-1)

This module provides resource health check implementation for monitoring
resource utilization and availability in the Person Suit deployment.

Classes:
- ResourceHealthCheck: Health check for system resources

Related Files:
- base.py: Base health check abstract class
- system.py: System health checks (more general system checks)
"""

import logging
from datetime import datetime
from typing import List, Tuple

# Try to import optional dependencies, some of which might not be available
try:
    import psutil

    HAVE_PSUTIL = True
except ImportError:
    HAVE_PSUTIL = False

from .base import HealthCheck
from ..models import (
    HealthIssue,
    HealthMetric,
    HealthSeverity,
    HealthThreshold,
)

# Configure logger
logger = logging.getLogger("person_suit.deployment.health.checks.resource")


class ResourceHealthCheck(HealthCheck):
    """
    Health check for system resources.

    Focuses on resource monitoring and utilization trends over time,
    complementing the SystemHealthCheck with more detailed resource analysis.
    """

    def __init__(
        self,
        cpu_sustained_threshold: float = 70.0,
        memory_growth_threshold: float = 10.0,
        io_usage_threshold: float = 80.0,
        network_usage_threshold: float = 80.0,
        enabled: bool = True,
    ):
        """
        Initialize the resource health check.

        Args:
            cpu_sustained_threshold: CPU usage percentage that triggers a warning when sustained
            memory_growth_threshold: Memory growth percentage that triggers a warning
            io_usage_threshold: IO usage percentage that triggers a warning
            network_usage_threshold: Network usage percentage that triggers a warning
            enabled: Whether the health check is enabled
        """
        super().__init__(name="resource", enabled=enabled)

        # Set up thresholds
        self.register_threshold(
            HealthThreshold(
                metric_name="cpu_sustained_usage",
                warning_threshold=cpu_sustained_threshold,
                error_threshold=cpu_sustained_threshold + 15,
                comparison="greater",
            )
        )

        self.register_threshold(
            HealthThreshold(
                metric_name="memory_growth_rate",
                warning_threshold=memory_growth_threshold,
                error_threshold=memory_growth_threshold * 2,
                comparison="greater",
            )
        )

        self.register_threshold(
            HealthThreshold(
                metric_name="io_usage",
                warning_threshold=io_usage_threshold,
                error_threshold=io_usage_threshold + 15,
                comparison="greater",
            )
        )

        self.register_threshold(
            HealthThreshold(
                metric_name="network_usage",
                warning_threshold=network_usage_threshold,
                error_threshold=network_usage_threshold + 15,
                comparison="greater",
            )
        )

        # Historical data (used to track changes over time)
        self.history = {"cpu": [], "memory": [], "disk_io": [], "network": []}
        self.max_history_points = 60  # Store up to 60 historical data points

        # Last check time
        self.last_check_time = None

    def check(self) -> List[HealthIssue]:
        """
        Perform the resource health check.

        Returns:
            List[HealthIssue]: List of health issues found
        """
        issues = []
        now = datetime.now()

        # Track time between checks for rate calculations
        if self.last_check_time is not None:
            time_delta_sec = (now - self.last_check_time).total_seconds()
        else:
            time_delta_sec = 0

        self.last_check_time = now

        # Skip trend analysis if we don't have enough history
        skip_trends = len(self.history["cpu"]) < 3

        # Basic CPU checks
        cpu_issues, cpu_metrics = self._check_cpu(time_delta_sec, skip_trends)
        issues.extend(cpu_issues)

        # Memory checks
        memory_issues, memory_metrics = self._check_memory(time_delta_sec, skip_trends)
        issues.extend(memory_issues)

        # Disk I/O checks
        disk_issues, disk_metrics = self._check_disk_io(time_delta_sec, skip_trends)
        issues.extend(disk_issues)

        # Network checks
        network_issues, network_metrics = self._check_network(
            time_delta_sec, skip_trends
        )
        issues.extend(network_issues)

        # Update history
        self._update_history("cpu", cpu_metrics)
        self._update_history("memory", memory_metrics)
        self._update_history("disk_io", disk_metrics)
        self._update_history("network", network_metrics)

        return issues

    def _update_history(self, resource_type: str, metrics: List[HealthMetric]) -> None:
        """
        Update the historical data for a resource type.

        Args:
            resource_type: The type of resource (cpu, memory, disk_io, network)
            metrics: The metrics to add to the history
        """
        # Add metrics to history
        self.history[resource_type].extend(metrics)

        # Trim history if it's too long
        if len(self.history[resource_type]) > self.max_history_points:
            self.history[resource_type] = self.history[resource_type][
                -self.max_history_points :
            ]

    def _check_cpu(
        self, time_delta_sec: float, skip_trends: bool
    ) -> Tuple[List[HealthIssue], List[HealthMetric]]:
        """
        Check CPU resource utilization.

        Args:
            time_delta_sec: Time in seconds since the last check
            skip_trends: Whether to skip trend analysis

        Returns:
            Tuple of (issues, metrics)
        """
        issues = []
        metrics = []

        if not HAVE_PSUTIL:
            return [], []

        try:
            # Get current CPU usage
            current_cpu_percent = psutil.cpu_percent(interval=0.5)

            # Create metrics
            metrics.append(
                HealthMetric(
                    name="cpu_usage",
                    value=current_cpu_percent,
                    unit="%",
                    component="resource.cpu",
                )
            )

            # Check for sustained high CPU usage
            if not skip_trends and len(self.history["cpu"]) > 0:
                cpu_history = [
                    m.value for m in self.history["cpu"] if m.name == "cpu_usage"
                ]
                if cpu_history:
                    avg_cpu = sum(cpu_history) / len(cpu_history)
                    metrics.append(
                        HealthMetric(
                            name="cpu_sustained_usage",
                            value=avg_cpu,
                            unit="%",
                            component="resource.cpu",
                        )
                    )

                    # Check if it exceeds the threshold
                    sustained_issue = self.evaluate_metric(metrics[-1])
                    if sustained_issue:
                        issues.append(sustained_issue)

                    # Check for CPU usage pattern (spikes or constant high usage)
                    if max(cpu_history) - min(cpu_history) > 50:
                        issues.append(
                            HealthIssue(
                                component="resource.cpu",
                                message="CPU usage shows significant fluctuations",
                                severity=HealthSeverity.INFO,
                                details={
                                    "min_cpu": min(cpu_history),
                                    "max_cpu": max(cpu_history),
                                    "avg_cpu": avg_cpu,
                                },
                                remediation="Monitor for possible CPU-intensive batch processes.",
                            )
                        )

            # Check per-core usage (potential imbalance)
            try:
                per_cpu = psutil.cpu_percent(interval=0.1, percpu=True)
                if per_cpu:
                    for i, usage in enumerate(per_cpu):
                        metrics.append(
                            HealthMetric(
                                name=f"cpu_core_{i}_usage",
                                value=usage,
                                unit="%",
                                component="resource.cpu.cores",
                            )
                        )

                    # Check for imbalance
                    if max(per_cpu) - min(per_cpu) > 70 and max(per_cpu) > 80:
                        issues.append(
                            HealthIssue(
                                component="resource.cpu.cores",
                                message="CPU core usage is significantly imbalanced",
                                severity=HealthSeverity.WARNING,
                                details={
                                    "min_core_usage": min(per_cpu),
                                    "max_core_usage": max(per_cpu),
                                },
                                remediation="Check if workloads can be better balanced across CPU cores.",
                            )
                        )
            except Exception:
                # Skip core check if not available
                pass

        except Exception as e:
            logger.error(f"Error checking CPU resources: {e}", exc_info=True)
            issues.append(
                HealthIssue(
                    component="resource.cpu",
                    message=f"Error checking CPU resources: {str(e)}",
                    severity=HealthSeverity.ERROR,
                    details={"exception": str(e), "exception_type": type(e).__name__},
                )
            )

        return issues, metrics

    def _check_memory(
        self, time_delta_sec: float, skip_trends: bool
    ) -> Tuple[List[HealthIssue], List[HealthMetric]]:
        """
        Check memory resource utilization.

        Args:
            time_delta_sec: Time in seconds since the last check
            skip_trends: Whether to skip trend analysis

        Returns:
            Tuple of (issues, metrics)
        """
        issues = []
        metrics = []

        if not HAVE_PSUTIL:
            return [], []

        try:
            # Get current memory usage
            memory = psutil.virtual_memory()

            # Create metrics
            metrics.append(
                HealthMetric(
                    name="memory_usage",
                    value=memory.percent,
                    unit="%",
                    component="resource.memory",
                )
            )

            metrics.append(
                HealthMetric(
                    name="memory_available",
                    value=memory.available / (1024 * 1024),  # MB
                    unit="MB",
                    component="resource.memory",
                )
            )

            # Check for memory leaks (steady increase over time)
            if (
                not skip_trends
                and len(self.history["memory"]) > 5
                and time_delta_sec > 0
            ):
                memory_history = [
                    m.value for m in self.history["memory"] if m.name == "memory_usage"
                ]
                if memory_history and (memory_history[-1] - memory_history[0]) > 0:
                    # Calculate the rate of memory growth in % per minute
                    start_time = self.history["memory"][0].timestamp
                    end_time = self.history["memory"][-1].timestamp
                    minutes_elapsed = (end_time - start_time).total_seconds() / 60

                    if minutes_elapsed > 0:
                        growth_rate = (
                            memory_history[-1] - memory_history[0]
                        ) / minutes_elapsed
                        metrics.append(
                            HealthMetric(
                                name="memory_growth_rate",
                                value=growth_rate,
                                unit="% per minute",
                                component="resource.memory",
                            )
                        )

                        # Check if it exceeds the threshold
                        growth_issue = self.evaluate_metric(metrics[-1])
                        if growth_issue:
                            issues.append(growth_issue)

            # Check swap usage
            try:
                swap = psutil.swap_memory()
                metrics.append(
                    HealthMetric(
                        name="swap_usage",
                        value=swap.percent,
                        unit="%",
                        component="resource.memory.swap",
                    )
                )

                if swap.percent > 80:
                    issues.append(
                        HealthIssue(
                            component="resource.memory.swap",
                            message=f"High swap memory usage: {swap.percent:.1f}%",
                            severity=HealthSeverity.WARNING,
                            details={
                                "swap_used_percent": swap.percent,
                                "swap_used_mb": swap.used / (1024 * 1024),
                            },
                            remediation="System is heavily relying on swap space. Consider increasing RAM.",
                        )
                    )
            except Exception:
                # Skip swap check if not available
                pass

        except Exception as e:
            logger.error(f"Error checking memory resources: {e}", exc_info=True)
            issues.append(
                HealthIssue(
                    component="resource.memory",
                    message=f"Error checking memory resources: {str(e)}",
                    severity=HealthSeverity.ERROR,
                    details={"exception": str(e), "exception_type": type(e).__name__},
                )
            )

        return issues, metrics

    def _check_disk_io(
        self, time_delta_sec: float, skip_trends: bool
    ) -> Tuple[List[HealthIssue], List[HealthMetric]]:
        """
        Check disk I/O resource utilization.

        Args:
            time_delta_sec: Time in seconds since the last check
            skip_trends: Whether to skip trend analysis

        Returns:
            Tuple of (issues, metrics)
        """
        issues = []
        metrics = []

        if not HAVE_PSUTIL:
            return [], []

        try:
            # Get current disk I/O counters
            disk_io = psutil.disk_io_counters()

            if disk_io:
                # Create metrics
                metrics.append(
                    HealthMetric(
                        name="disk_read_bytes",
                        value=disk_io.read_bytes / (1024 * 1024),  # MB
                        unit="MB",
                        component="resource.disk.io",
                    )
                )

                metrics.append(
                    HealthMetric(
                        name="disk_write_bytes",
                        value=disk_io.write_bytes / (1024 * 1024),  # MB
                        unit="MB",
                        component="resource.disk.io",
                    )
                )

                # Calculate I/O rates if we have history
                if (
                    not skip_trends
                    and len(self.history["disk_io"]) > 0
                    and time_delta_sec > 0
                ):
                    prev_read = next(
                        (
                            m.value
                            for m in reversed(self.history["disk_io"])
                            if m.name == "disk_read_bytes"
                        ),
                        None,
                    )
                    prev_write = next(
                        (
                            m.value
                            for m in reversed(self.history["disk_io"])
                            if m.name == "disk_write_bytes"
                        ),
                        None,
                    )

                    if prev_read is not None and prev_write is not None:
                        read_rate = (
                            disk_io.read_bytes / (1024 * 1024) - prev_read
                        ) / time_delta_sec  # MB/s
                        write_rate = (
                            disk_io.write_bytes / (1024 * 1024) - prev_write
                        ) / time_delta_sec  # MB/s

                        metrics.append(
                            HealthMetric(
                                name="disk_read_rate",
                                value=read_rate,
                                unit="MB/s",
                                component="resource.disk.io",
                            )
                        )

                        metrics.append(
                            HealthMetric(
                                name="disk_write_rate",
                                value=write_rate,
                                unit="MB/s",
                                component="resource.disk.io",
                            )
                        )

                        # Check for high I/O rates
                        if (
                            read_rate > 100 or write_rate > 100
                        ):  # 100 MB/s is quite high for most systems
                            issues.append(
                                HealthIssue(
                                    component="resource.disk.io",
                                    message=f"High disk I/O rates: {read_rate:.1f} MB/s read, {write_rate:.1f} MB/s write",
                                    severity=HealthSeverity.WARNING,
                                    details={
                                        "read_rate_mb_per_sec": read_rate,
                                        "write_rate_mb_per_sec": write_rate,
                                    },
                                    remediation="Check for disk-intensive processes that might affect performance.",
                                )
                            )

            # Per-disk detailed metrics
            try:
                for disk, usage in psutil.disk_io_counters(perdisk=True).items():
                    metrics.append(
                        HealthMetric(
                            name=f"disk_{disk}_read_bytes",
                            value=usage.read_bytes / (1024 * 1024),  # MB
                            unit="MB",
                            component=f"resource.disk.{disk}",
                        )
                    )

                    metrics.append(
                        HealthMetric(
                            name=f"disk_{disk}_write_bytes",
                            value=usage.write_bytes / (1024 * 1024),  # MB
                            unit="MB",
                            component=f"resource.disk.{disk}",
                        )
                    )

                    # Check for busy disks (high read/write counts)
                    metrics.append(
                        HealthMetric(
                            name=f"disk_{disk}_read_count",
                            value=usage.read_count,
                            unit="ops",
                            component=f"resource.disk.{disk}",
                        )
                    )

                    metrics.append(
                        HealthMetric(
                            name=f"disk_{disk}_write_count",
                            value=usage.write_count,
                            unit="ops",
                            component=f"resource.disk.{disk}",
                        )
                    )
            except Exception:
                # Skip per-disk check if not available
                pass

        except Exception as e:
            logger.error(f"Error checking disk I/O resources: {e}", exc_info=True)
            issues.append(
                HealthIssue(
                    component="resource.disk.io",
                    message=f"Error checking disk I/O resources: {str(e)}",
                    severity=HealthSeverity.ERROR,
                    details={"exception": str(e), "exception_type": type(e).__name__},
                )
            )

        return issues, metrics

    def _check_network(
        self, time_delta_sec: float, skip_trends: bool
    ) -> Tuple[List[HealthIssue], List[HealthMetric]]:
        """
        Check network resource utilization.

        Args:
            time_delta_sec: Time in seconds since the last check
            skip_trends: Whether to skip trend analysis

        Returns:
            Tuple of (issues, metrics)
        """
        issues = []
        metrics = []

        if not HAVE_PSUTIL:
            return [], []

        try:
            # Get current network I/O counters
            net_io = psutil.net_io_counters()

            if net_io:
                # Create metrics
                metrics.append(
                    HealthMetric(
                        name="net_bytes_sent",
                        value=net_io.bytes_sent / (1024 * 1024),  # MB
                        unit="MB",
                        component="resource.network",
                    )
                )

                metrics.append(
                    HealthMetric(
                        name="net_bytes_recv",
                        value=net_io.bytes_recv / (1024 * 1024),  # MB
                        unit="MB",
                        component="resource.network",
                    )
                )

                # Calculate network rates if we have history
                if (
                    not skip_trends
                    and len(self.history["network"]) > 0
                    and time_delta_sec > 0
                ):
                    prev_sent = next(
                        (
                            m.value
                            for m in reversed(self.history["network"])
                            if m.name == "net_bytes_sent"
                        ),
                        None,
                    )
                    prev_recv = next(
                        (
                            m.value
                            for m in reversed(self.history["network"])
                            if m.name == "net_bytes_recv"
                        ),
                        None,
                    )

                    if prev_sent is not None and prev_recv is not None:
                        sent_rate = (
                            net_io.bytes_sent / (1024 * 1024) - prev_sent
                        ) / time_delta_sec  # MB/s
                        recv_rate = (
                            net_io.bytes_recv / (1024 * 1024) - prev_recv
                        ) / time_delta_sec  # MB/s

                        metrics.append(
                            HealthMetric(
                                name="net_send_rate",
                                value=sent_rate,
                                unit="MB/s",
                                component="resource.network",
                            )
                        )

                        metrics.append(
                            HealthMetric(
                                name="net_recv_rate",
                                value=recv_rate,
                                unit="MB/s",
                                component="resource.network",
                            )
                        )

                        # Calculate utilization percentage (very rough estimate)
                        # Assuming 1 Gbps network = 125 MB/s max throughput
                        estimated_capacity = 125  # MB/s (1 Gbps)
                        utilization = (
                            (sent_rate + recv_rate) / estimated_capacity
                        ) * 100

                        metrics.append(
                            HealthMetric(
                                name="network_usage",
                                value=utilization,
                                unit="%",
                                component="resource.network",
                            )
                        )

                        # Check if it exceeds the threshold
                        utilization_issue = self.evaluate_metric(metrics[-1])
                        if utilization_issue:
                            issues.append(utilization_issue)

                        # Check for very high network rates
                        if (
                            sent_rate > 50 or recv_rate > 50
                        ):  # 50 MB/s is quite high for many systems
                            issues.append(
                                HealthIssue(
                                    component="resource.network",
                                    message=f"High network rates: {sent_rate:.1f} MB/s sent, {recv_rate:.1f} MB/s received",
                                    severity=HealthSeverity.INFO,
                                    details={
                                        "send_rate_mb_per_sec": sent_rate,
                                        "recv_rate_mb_per_sec": recv_rate,
                                    },
                                    remediation="Monitor for unexpected network-intensive operations.",
                                )
                            )

            # Per-interface detailed metrics
            try:
                for nic, counters in psutil.net_io_counters(pernic=True).items():
                    # Skip local loopback
                    if nic.lower() == "lo" or nic.lower().startswith("loop"):
                        continue

                    metrics.append(
                        HealthMetric(
                            name=f"net_{nic}_bytes_sent",
                            value=counters.bytes_sent / (1024 * 1024),  # MB
                            unit="MB",
                            component=f"resource.network.{nic}",
                        )
                    )

                    metrics.append(
                        HealthMetric(
                            name=f"net_{nic}_bytes_recv",
                            value=counters.bytes_recv / (1024 * 1024),  # MB
                            unit="MB",
                            component=f"resource.network.{nic}",
                        )
                    )

                    # Check for high error rates
                    if counters.errin > 0 or counters.errout > 0:
                        metrics.append(
                            HealthMetric(
                                name=f"net_{nic}_errors",
                                value=counters.errin + counters.errout,
                                unit="errors",
                                component=f"resource.network.{nic}",
                            )
                        )

                        if counters.errin + counters.errout > 100:
                            issues.append(
                                HealthIssue(
                                    component=f"resource.network.{nic}",
                                    message=f"High network error count on {nic}: {counters.errin} in, {counters.errout} out",
                                    severity=HealthSeverity.WARNING,
                                    details={
                                        "errin": counters.errin,
                                        "errout": counters.errout,
                                        "interface": nic,
                                    },
                                    remediation="Check network interface or connection for issues.",
                                )
                            )
            except Exception:
                # Skip per-interface check if not available
                pass

        except Exception as e:
            logger.error(f"Error checking network resources: {e}", exc_info=True)
            issues.append(
                HealthIssue(
                    component="resource.network",
                    message=f"Error checking network resources: {str(e)}",
                    severity=HealthSeverity.ERROR,
                    details={"exception": str(e), "exception_type": type(e).__name__},
                )
            )

        return issues, metrics
