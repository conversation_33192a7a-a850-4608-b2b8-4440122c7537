"""
Person Suit - Security Policies (DE-1)

This module provides security policy management for the Person Suit framework,
defining and creating environment-specific security policies.

Functions:
- create_development_policy: Create security policy for development environments
- create_testing_policy: Create security policy for testing environments
- create_staging_policy: Create security policy for staging environments
- create_production_policy: Create security policy for production environments

Related Files:
- models.py: Security data models and enumerations
- boundary.py: Security isolation boundary implementation
- manager.py: Security management and policy enforcement
- token.py: Security token handling and verification

Dependencies:
- person_suit.core.infrastructure.configuration.environment: For environment types
"""

import logging

from .models import (
    BoundaryType,
    SecurityConstraint,
    SecurityLevel,
    SecurityPolicy,
)
from ...infrastructure.configuration.environment.handler import (
    Environment,
)

# Configure logger
logger = logging.getLogger("person_suit.deployment.security.policy")


def create_development_policy() -> SecurityPolicy:
    """
    Create security policy for development environments.

    Returns:
        SecurityPolicy: Development security policy
    """
    constraints = {
        "process_boundary": SecurityConstraint(
            name="process_boundary",
            description="Process isolation boundary",
            boundary_type=BoundaryType.PROCESS,
            security_level=SecurityLevel.RELAXED,
        ),
        "filesystem_boundary": SecurityConstraint(
            name="filesystem_boundary",
            description="Filesystem access boundary",
            boundary_type=BoundaryType.FILESYSTEM,
            security_level=SecurityLevel.RELAXED,
        ),
        "network_boundary": SecurityConstraint(
            name="network_boundary",
            description="Network access boundary",
            boundary_type=BoundaryType.NETWORK,
            security_level=SecurityLevel.RELAXED,
        ),
    }

    policy = SecurityPolicy(
        name="development",
        description="Security policy for development environments",
        environment=Environment.DEVELOPMENT,
        constraints=constraints,
        default_security_level=SecurityLevel.RELAXED,
    )

    logger.debug("Created development security policy")
    return policy


def create_testing_policy() -> SecurityPolicy:
    """
    Create security policy for testing environments.

    Returns:
        SecurityPolicy: Testing security policy
    """
    constraints = {
        "process_boundary": SecurityConstraint(
            name="process_boundary",
            description="Process isolation boundary",
            boundary_type=BoundaryType.PROCESS,
            security_level=SecurityLevel.STANDARD,
        ),
        "filesystem_boundary": SecurityConstraint(
            name="filesystem_boundary",
            description="Filesystem access boundary",
            boundary_type=BoundaryType.FILESYSTEM,
            security_level=SecurityLevel.STANDARD,
        ),
        "network_boundary": SecurityConstraint(
            name="network_boundary",
            description="Network access boundary",
            boundary_type=BoundaryType.NETWORK,
            security_level=SecurityLevel.STANDARD,
        ),
    }

    policy = SecurityPolicy(
        name="testing",
        description="Security policy for testing environments",
        environment=Environment.TESTING,
        constraints=constraints,
        default_security_level=SecurityLevel.STANDARD,
    )

    logger.debug("Created testing security policy")
    return policy


def create_staging_policy() -> SecurityPolicy:
    """
    Create security policy for staging environments.

    Returns:
        SecurityPolicy: Staging security policy
    """
    constraints = {
        "process_boundary": SecurityConstraint(
            name="process_boundary",
            description="Process isolation boundary",
            boundary_type=BoundaryType.PROCESS,
            security_level=SecurityLevel.STANDARD,
        ),
        "filesystem_boundary": SecurityConstraint(
            name="filesystem_boundary",
            description="Filesystem access boundary",
            boundary_type=BoundaryType.FILESYSTEM,
            security_level=SecurityLevel.STRICT,
        ),
        "network_boundary": SecurityConstraint(
            name="network_boundary",
            description="Network access boundary",
            boundary_type=BoundaryType.NETWORK,
            security_level=SecurityLevel.STRICT,
        ),
    }

    policy = SecurityPolicy(
        name="staging",
        description="Security policy for staging environments",
        environment=Environment.STAGING,
        constraints=constraints,
        default_security_level=SecurityLevel.STANDARD,
    )

    logger.debug("Created staging security policy")
    return policy


def create_production_policy() -> SecurityPolicy:
    """
    Create security policy for production environments.

    Returns:
        SecurityPolicy: Production security policy
    """
    constraints = {
        "process_boundary": SecurityConstraint(
            name="process_boundary",
            description="Process isolation boundary",
            boundary_type=BoundaryType.PROCESS,
            security_level=SecurityLevel.STRICT,
        ),
        "filesystem_boundary": SecurityConstraint(
            name="filesystem_boundary",
            description="Filesystem access boundary",
            boundary_type=BoundaryType.FILESYSTEM,
            security_level=SecurityLevel.STRICT,
        ),
        "network_boundary": SecurityConstraint(
            name="network_boundary",
            description="Network access boundary",
            boundary_type=BoundaryType.NETWORK,
            security_level=SecurityLevel.STRICT,
        ),
        "memory_boundary": SecurityConstraint(
            name="memory_boundary",
            description="Memory isolation boundary",
            boundary_type=BoundaryType.MEMORY,
            security_level=SecurityLevel.STRICT,
        ),
    }

    policy = SecurityPolicy(
        name="production",
        description="Security policy for production environments",
        environment=Environment.PRODUCTION,
        constraints=constraints,
        default_security_level=SecurityLevel.STRICT,
    )

    logger.debug("Created production security policy")
    return policy
