"""
Person Suit - Deployment Security Package (DE-1)

This package provides security boundaries for the Person Suit framework,
enabling secure operation across different deployment environments by
implementing environment-specific security controls and isolation mechanisms.

Components:
- BoundaryType: Types of security boundaries
- SecurityLevel: Security enforcement levels
- SecurityConstraint: Security constraint definition
- SecurityPolicy: Environment-specific security policy
- SecurityBoundary: Security isolation boundary
- SecurityManager: Central security management

Usage:
    # Get the security manager
    manager = get_security_manager()

    # Get a security boundary
    boundary = get_boundary("my_component", BoundaryType.PROCESS)

    # Create a security token
    token = create_token(TokenType.COMPONENT, AccessLevel.STANDARD, SecurityRole.SYSTEM)

    # Grant access through a boundary
    boundary.grant_access(token)
"""

# Import from core module
from .core import (  # Models; Security classes; Manager functions; Token functions; Boundary functions; Context and policy functions; Policy creation functions
    AccessLevel,
    BoundaryType,
    SecurityBoundary,
    SecurityConstraint,
    SecurityContext,
    SecurityLevel,
    SecurityManager,
    SecurityPolicy,
    SecurityRole,
    TokenCredential,
    TokenType,
    create_development_policy,
    create_production_policy,
    create_staging_policy,
    create_testing_policy,
    create_token,
    extend_token,
    get_active_policy,
    get_boundary,
    get_security_context,
    get_security_manager,
    revoke_token,
    verify_token,
)

# All functionality is now imported from the core module

# Define exported symbols
__all__ = [
    # Models
    "BoundaryType",
    "SecurityLevel",
    "SecurityConstraint",
    "SecurityPolicy",
    # Security classes
    "SecurityBoundary",
    "SecurityManager",
    "SecurityContext",
    "TokenCredential",
    "TokenType",
    "AccessLevel",
    "SecurityRole",
    # Manager functions
    "get_security_manager",
    # Token functions
    "create_token",
    "verify_token",
    "revoke_token",
    "extend_token",
    # Boundary functions
    "get_boundary",
    # Context and policy functions
    "get_security_context",
    "get_active_policy",
    # Policy creation functions
    "create_development_policy",
    "create_testing_policy",
    "create_staging_policy",
    "create_production_policy",
]
