"""
Person Suit - Security Manager (DE-1)

This module provides the central security management for the Person Suit framework,
coordinating security policies, boundaries, and token management.

Classes:
- SecurityManager: Central security management singleton

Related Files:
- models.py: Security data models and enumerations
- boundary.py: Security isolation boundary implementation
- token.py: Security token handling and verification
- policy.py: Security policy management

Dependencies:
- person_suit.core.deployment.detection: For environment detection
- person_suit.core.infrastructure.security: For core security types
"""

import logging
import threading
from typing import Dict, Optional

from ..detection import detect_environment
from .boundary import SecurityBoundary
from .models import (
    BoundaryType,
    SecurityLevel,
    SecurityPolicy,
)
from .policy import (
    create_development_policy,
    create_production_policy,
    create_staging_policy,
    create_testing_policy,
)
from .token import create_token as _create_token
from .token import revoke_token as _revoke_token
from .token import verify_token as _verify_token
from ...infrastructure.security import (
    AccessLevel,
    SecurityContext,
    SecurityRole,
    TokenType,
)
from ...infrastructure.security.authentication.auth_manager import (
    TokenCredential,
)

# Configure logger
logger = logging.getLogger("person_suit.deployment.security.manager")


class SecurityManager:
    """
    Security management for deployment environments.

    Manages security policies, boundaries, and enforcement for
    different deployment environments.
    """

    _instance = None
    _lock = threading.RLock()

    def __new__(cls):
        """Singleton implementation."""
        with cls._lock:
            if not cls._instance:
                cls._instance = super(SecurityManager, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self):
        """Initialize security manager."""
        if self._initialized:
            return

        with self._lock:
            if not self._initialized:
                self._environment_data = None
                self._security_policies = {}
                self._active_policy = None
                self._security_context = None
                self._boundaries = {}
                self._initialized = False

                logger.debug("Security manager created (not initialized)")

    def initialize(self) -> bool:
        """
        Initialize security manager.

        Returns:
            bool: True if initialization was successful
        """
        if self._initialized:
            logger.warning("Security manager already initialized")
            return True

        try:
            # Detect environment
            self._environment_data = detect_environment()

            # Load security policies
            self._load_security_policies()

            # Select and activate policy
            self._select_security_policy()

            # Create security context
            self._create_security_context()

            self._initialized = True
            logger.info("Security manager initialized")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize security manager: {e}")
            return False

    def _load_security_policies(self) -> None:
        """Load security policies."""
        # Create default policies
        dev_policy = create_development_policy()
        test_policy = create_testing_policy()
        staging_policy = create_staging_policy()
        prod_policy = create_production_policy()

        # Register policies
        self._security_policies[dev_policy.name] = dev_policy
        self._security_policies[test_policy.name] = test_policy
        self._security_policies[staging_policy.name] = staging_policy
        self._security_policies[prod_policy.name] = prod_policy

        logger.debug(f"Loaded {len(self._security_policies)} security policies")

    def _select_security_policy(self) -> None:
        """Select and activate security policy."""
        current_env = self._environment_data.runtime.environment

        # Find policy for current environment
        for policy in self._security_policies.values():
            if policy.environment == current_env:
                policy.is_active = True
                self._active_policy = policy
                logger.info(f"Activated security policy: {policy.name}")
                return

        # Fallback to standard policy
        fallback_policy = self._security_policies.get("standard")
        if fallback_policy:
            fallback_policy.is_active = True
            self._active_policy = fallback_policy
            logger.info(f"Activated fallback security policy: {fallback_policy.name}")
        else:
            logger.warning(
                "No suitable security policy found, security will be limited"
            )

    def _create_security_context(self) -> None:
        """Create security context from active policy."""
        if self._active_policy:
            policy = self._active_policy
            constraints = {
                name: constraint for name, constraint in policy.constraints.items()
            }

            self._security_context = SecurityContext(
                security_level=policy.default_security_level,
                environment=policy.environment,
                constraints=constraints,
            )

            logger.info(
                f"Created security context with level: {policy.default_security_level.name}"
            )
        else:
            # Create default context
            self._security_context = SecurityContext(
                security_level=SecurityLevel.STANDARD,
                environment=self._environment_data.runtime.environment,
                constraints={},
            )

            logger.warning("Created default security context, security may be limited")

    def get_security_policies(self) -> Dict[str, SecurityPolicy]:
        """
        Get security policies.

        Returns:
            Dict[str, SecurityPolicy]: Security policies
        """
        if not self._initialized:
            self.initialize()

        return self._security_policies.copy()

    def get_active_policy(self) -> Optional[SecurityPolicy]:
        """
        Get active security policy.

        Returns:
            SecurityPolicy: Active security policy if available, None otherwise
        """
        if not self._initialized:
            self.initialize()

        return self._active_policy

    def get_security_context(self) -> Optional[SecurityContext]:
        """
        Get security context.

        Returns:
            SecurityContext: Security context if available, None otherwise
        """
        if not self._initialized:
            self.initialize()

        return self._security_context

    def get_boundary(self, name: str, boundary_type: BoundaryType) -> SecurityBoundary:
        """
        Get or create a security boundary.

        Args:
            name: Boundary name
            boundary_type: Boundary type

        Returns:
            SecurityBoundary: Security boundary
        """
        if not self._initialized:
            self.initialize()

        with self._lock:
            boundary_key = f"{name}:{boundary_type.name}"

            # Create boundary if it doesn't exist
            if boundary_key not in self._boundaries:
                boundary = SecurityBoundary(name, boundary_type)
                boundary.initialize(self._security_context)
                self._boundaries[boundary_key] = boundary
                logger.debug(f"Created security boundary: {boundary_key}")

            return self._boundaries[boundary_key]

    def create_token(
        self, token_type: TokenType, access_level: AccessLevel, role: SecurityRole
    ) -> TokenCredential:
        """
        Create a security token.

        Args:
            token_type: Token type
            access_level: Access level
            role: Security role

        Returns:
            TokenCredential: Created token
        """
        if not self._initialized:
            self.initialize()

        return _create_token(token_type, access_level, role)

    def verify_token(self, token: TokenCredential) -> bool:
        """
        Verify a security token.

        Args:
            token: Security token to verify

        Returns:
            bool: True if token is valid
        """
        if not self._initialized:
            self.initialize()

        return _verify_token(token)

    def revoke_token(self, token: TokenCredential) -> bool:
        """
        Revoke a security token.

        Args:
            token: Security token to revoke

        Returns:
            bool: True if token was revoked
        """
        if not self._initialized:
            self.initialize()

        # Revoke the token
        token_revoked = _revoke_token(token)

        # Revoke access to all boundaries
        for boundary in self._boundaries.values():
            boundary.revoke_access(token)

        return token_revoked


# Singleton instance
_security_manager_instance = None


def get_security_manager() -> SecurityManager:
    """Get the singleton security manager instance."""
    global _security_manager_instance

    if _security_manager_instance is None:
        _security_manager_instance = SecurityManager()

    return _security_manager_instance
