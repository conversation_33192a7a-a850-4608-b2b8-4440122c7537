# Codebase Inventory: person_suit/core/

## Overall Purpose of `person_suit/core/`

*   Contains foundational elements, core abstractions, and essential services used across the entire Person Suit application. This includes configuration, logging, event systems, fundamental CAW (Contextual Adaptive Wave) constructs, security primitives, memory management interfaces, and other cross-cutting concerns.

---

## Top-Level Files in `person_suit/core/`

*   `__init__.py`:
    *   **Purpose:** Package initializer for the `person_suit.core` module. Likely exports key classes and functions for easier access from other parts of the application and may set up package-level configurations or logging.
*   `di.py`:
    *   **Purpose:** Manages Dependency Injection (DI) setup and registration for core services. Provides mechanisms for components to receive their dependencies without hardcoding them.
    *   **Key Classes/Functions:** (e.g., `CoreContainer`, `register_service`, `get_service`)
*   `meta_system.py`:
    *   **Purpose:** Defines core abstractions or base classes related to the Meta-Systems (Persona Core, Analyst, Predictor). This might include base classes for meta-system components or shared logic.
    *   **Key Classes:** (e.g., `BaseMetaSystemComponent`, `MetaSystemInterface`)
*   `README.md`:
    *   **Purpose:** Provides an overview of the `person_suit/core/` directory, its structure, and key components. *(Self-referential, but good to note its existence in the inventory)*

---

## Sub-directory: `person_suit/core/actors/`

*   **Purpose:** Contains core components related to the Actor Model implementation, likely including base actor classes, actor system management, and message definitions, fundamental to CAW actor implementation.
*   **Key Modules/Files:**
    *   `actor_system.py`:
        *   **Purpose:** Implements the core asynchronous Actor System using `asyncio`. Manages actor lifecycle (create, stop, restart), message dispatching, integrates Death Watch/Supervision, and includes capability checks. Aligns with CAW principles.
        *   **Key Classes:** `ActorSystem`.
        *   **Key Functions:** `__init__`, `set_capability_service_ref`, `start`, `stop`, `create_actor`, `_cleanup_failed_start`, `get_actor_ref`, `send`, `deliver_message`, `ask`, `resolve_ask_future`, `watch`, `unwatch`, `notify_termination`, `handle_actor_failure`, `stop_actor`, `restart_actor`.
        *   **Actors:** None.
    *   `actor_system_watch.py`:
        *   **Purpose:** Implements the death watch functionality for the Actor System, managing watch relationships and notifying actors when others terminate. Includes capability checks for watch operations.
        *   **Key Classes:** `DeathWatchManager`.
        *   **Key Functions:** `__init__`, `clear`, `watch`, `unwatch`, `notify_termination`.
        *   **Actors:** None.
    *   *(Files will be listed here with their details)*

---

## Sub-directory: `person_suit/core/adaptivity/`

*   **Purpose:** Houses modules related to the system's adaptive capabilities, potentially including mechanisms for Adaptive Computational Fidelity (ACF) or other dynamic adjustments based on context, as per CAW principles.
*   **Key Modules/Files:**
    *   *(To be filled by listing files)*

---

## Sub-directory: `person_suit/core/application/`

*   **Purpose:** Contains core application logic, orchestration, or services that are not specific to a single meta-system but are foundational to the application's overall operation. This might include handlers or core use case implementations.
*   **Key Modules/Files:**
    *   *(To be filled by listing files)*

---

## Sub-directory: `person_suit/core/capabilities/`

*   **Purpose:** Implements the core logic for Capability-Based Security (CBS), including defining capabilities, managing capability tokens, and performing authorization checks. Central to CAW's security model.
*   **Key Modules/Files:**
    *   *(To be filled by listing files)*

---

## Sub-directory: `person_suit/core/caw/`

*   **Purpose:** Central hub for the fundamental constructs of Contextual Adaptive Wave (CAW) programming. This would include definitions for duality, contextual computation, CAW actors, choreographies, effects, and potentially interfaces to advanced mathematical structures.
*   **Key Modules/Files:**
    *   *(To be filled by listing files)*

---

## Sub-directory: `person_suit/core/config/`

*   **Purpose:** (From README.md) Contains the Python code responsible for loading, parsing, validating, and providing access to configuration data for the Person Suit framework. It provides the *logic* to interact with actual configuration files (e.g., stored in a root `/config/` directory).
*   **Key Modules/Files:**
    *   `loader.py`:
        *   **Purpose:** Provides utilities for loading application configuration settings from an external YAML file (defaulting to `config.yaml` in the project root). It includes caching for the loaded configuration and provides a helper function to retrieve specific values using a dot-separated key.
        *   **Key Classes:** None.
        *   **Key Functions:**
            *   `load_config(config_path: str = DEFAULT_CONFIG_PATH) -> Dict[str, Any]`: Loads configuration from a YAML file, caches the result, and handles errors.
            *   `get_config_value(key: str, default: Any = None) -> Any`: Retrieves a specific value from the loaded configuration cache, supporting dot-notation for nested keys.
        *   **Actors:** None.
    *   `README.md`:
        *   **Purpose:** Describes the `person_suit/core/config/` directory, its purpose (loading, parsing, validating, providing access to configuration data), core components (`loader.py`), design alignment (Separation of Concerns, Modularity), and usage patterns (suggesting an `IConfigManager` interface for DI).

---

## Sub-directory: `person_suit/core/constants/`

*   **Purpose:** Defines global constants, enums, and other fixed values used throughout the core system or wider application.
*   **Key Modules/Files:**
    *   `hardware.py`:
        *   **Purpose:** Defines hardware-specific constants (CPU, memory, disk, GPU, Apple-specific, optimization flags) and determines a hardware capability level.
        *   **Key Constants:** `CPU_COUNT`, `TOTAL_MEMORY`, `GPU_AVAILABLE`, `IS_APPLE_SILICON`, `HARDWARE_OPTIMIZATIONS`, `HARDWARE_CAPABILITY_LEVEL`, and many others.
        *   **Key Classes:** `HardwareCapabilityLevel`.
        *   **Key Functions:** `_determine_hardware_capability_level`.
        *   **Actors:** None.
    *   `performance.py`:
        *   **Purpose:** Defines constants for performance tuning, including batch/chunk/page sizes, cache configurations, thread/process pool sizes, thresholds, timeouts, retry logic, database settings, and vector operation parameters. Includes optimization strategies and M3-specific settings.
        *   **Key Constants:** `DEFAULT_BATCH_SIZE`, `DEFAULT_CACHE_SIZE`, `DEFAULT_THREAD_POOL_SIZE`, `DEFAULT_CPU_THRESHOLD`, `DEFAULT_OPERATION_TIMEOUT`, `DEFAULT_RETRY_COUNT`, `DEFAULT_DB_CONNECTION_POOL_SIZE`, `DEFAULT_VECTOR_DIMENSION` (2048), `DEFAULT_OPTIMIZATION_STRATEGIES`, `M3_OPTIMIZATIONS`, and many others.
        *   **Key Classes:** None.
        *   **Key Functions:** None.
        *   **Actors:** None.
    *   `system.py`:
        *   **Purpose:** Defines system-wide operational constants including timeouts, resource limits, platform detection, M3 architecture details, default settings (encoding, locale, etc.), file system params, network settings, and process/thread counts.
        *   **Key Constants:** Numerous constants such as `DEFAULT_OPERATION_TIMEOUT`, `MAX_CONCURRENT_OPERATIONS`, `IS_APPLE_SILICON`, `M3_ARCHITECTURE`, `DEFAULT_ENCODING`, `MAX_PATH_LENGTH`, `DEFAULT_PORT`, `DEFAULT_PROCESS_COUNT`, and `DEFAULT_MEMORY_LIMIT`.
        *   **Key Classes:** None.
        *   **Key Functions:** None.
        *   **Actors:** None.
    *   `__init__.py`:
        *   **Purpose:** Package initializer for `person_suit.core.constants`. Defines the constant organization structure and re-exports key constants from sub-modules (`system`, `security`, `performance`, `hardware`) for convenient access.
        *   **Key Constants:** Re-exports numerous constants from other files in this directory (e.g., `DEFAULT_OPERATION_TIMEOUT`, `SECURITY_LEVEL`, `DEFAULT_BATCH_SIZE`, `CPU_COUNT`), as listed in `__all__`. Does not define new constants.
        *   **Key Classes:** None.
        *   **Key Functions:** None.
        *   **Actors:** None.
    *   `README.md`:
        *   **Purpose:** Provides an overview of the `person_suit/core/constants/` directory, explaining its purpose (defining system-wide constants) and listing the main constant files within it (`system.py`, `security.py`, `performance.py`, `hardware.py`) and how to use them.
    *   `security.py`:
        *   **Purpose:** Defines constants related to security configurations and practices (cryptography, security levels, auth, capabilities, zero-trust, audit, quantum-resistant algorithms, formal verification, secure communication).
        *   **Key Constants:** Numerous constants covering cryptographic settings, authentication, capabilities, zero-trust, audit, quantum-resistant algorithms, formal verification, and secure communication protocols.
        *   **Key Classes:** `SecurityLevel`.
        *   **Key Functions:** None.
        *   **Actors:** None.
    *   *(Other files in constants/ will be added here)*

---

## Sub-directory: `person_suit/core/context/`

*   **Purpose:** Manages contextual information that is pervasive throughout the application. This is critical for CAW, supporting context propagation and its influence on computation.
*   **Key Modules/Files:**
    *   *(e.g., `context_manager.py`, `context_aware.py`)*

---

## Sub-directory: `person_suit/core/deployment/`

*   **Purpose:** Contains modules related to the deployment and operational aspects of the core system, potentially including health checks, resource management, or environment detection.
*   **Key Modules/Files:**
    *   *(To be filled by listing files)*

---

## Sub-directory: `person_suit/core/effects/`

*   **Purpose:** Implements the Effect System, making side effects explicit and manageable. This aligns with CAW's principle of managing operational consequences.
*   **Key Modules/Files:**
    *   *(e.g., `effect_handler.py`, `effect_types.py`)*

---

## Sub-directory: `person_suit/core/event_logging/`

*   **Purpose:** Specialized logging for system events, likely more structured than general application logging, perhaps for audit trails or specific event sourcing patterns.
*   **Key Modules/Files:**
    *   `arango_log_service.py`:
        *   **Purpose:** Concrete implementation of the Event/Effect Log Service using ArangoDB. Persists event history to an ArangoDB collection, providing query capabilities, and manages snapshot metadata.
        *   **Key Classes:** `ArangoDBEventLogService`.
        *   **Key Functions:** `__init__`, `_ensure_indexes`, `_serialize_dataclass`, `log_event`, `query_events`, `get_latest_event`, `record_snapshot_metadata`, `get_latest_snapshot_metadata`.
        *   **Actors:** None.

---

## Sub-directory: `person_suit/core/events/`

*   **Purpose:** Defines core event system abstractions and interfaces, including base event types and interfaces for event managers/buses.
*   **Key Modules/Files:**
    *   `__init__.py`:
        *   **Purpose:** Package initializer for `person_suit.core.events`. Defines the constant organization structure and re-exports key event-related components, currently `InfrastructureEventManager`, from the infrastructure layer.
        *   **Key Classes:** None defined here, but re-exports `InfrastructureEventManager`. (Docstring mentions base types/interfaces, but not visible in snippet).
        *   **Key Functions:** None.
        *   **Actors:** None.

---

## Sub-directory: `person_suit/core/formal_methods/`

*   **Purpose:** Includes components or integrations related to formal verification or mathematically rigorous specification of system parts, enhancing reliability and security.
*   **Key Modules/Files:**
    *   *(To be filled by listing files)*

---

## Sub-directory: `person_suit/core/information/`

*   **Purpose:** Deals with the core representation, processing, and flow of information within the system, potentially including aspects of CAW's information duality (wave/particle).
*   **Key Modules/Files:**
    *   *(To be filled by listing files)*

---

## Sub-directory: `person_suit/core/infrastructure/`

*   **Purpose:** Provides low-level infrastructure services and abstractions, such as communication protocols, data persistence layers (or interfaces to them), and other foundational technical capabilities supporting the core logic. The `dual_wave` components likely reside here.
*   **Key Modules/Files:**
    *   *(e.g., `dual_wave/`, `communication/`, `persistence/`)*

---

## Sub-directory: `person_suit/core/memory/`

*   **Purpose:** Defines interfaces and core logic for the Person Suit memory system, abstracting different memory layers (sensory, working, long-term) and processes (encoding, retrieval).
*   **Key Modules/Files:**
    *   *(e.g., `memory_interface.py`, `cache_manager.py`)*

---

## Sub-directory: `person_suit/core/notification/`

*   **Purpose:** Manages system-wide notifications, which could be internal alerts or hooks for external notification systems.
*   **Key Modules/Files:**
    *   *(To be filled by listing files)*

---

## Sub-directory: `person_suit/core/pathway/`

*   **Purpose:** Likely relates to the "Folded Mind" architecture, defining core logic for computational-functional and subjective-experiential pathways, or general CAW choreographies.
*   **Key Modules/Files:**
    *   *(To be filled by listing files)*

---

## Sub-directory: `person_suit/core/security/`

*   **Purpose:** Contains broader security mechanisms beyond capabilities, such as authentication, encryption utilities, and security policy enforcement.
*   **Key Modules/Files:**
    *   *(e.g., `auth_service.py`, `encryption.py`)*

---

## Sub-directory: `person_suit/core/state_storage/`

*   **Purpose:** Provides abstractions and implementations for persisting and managing the state of core components or the overall application. This might be distinct from the primary "memory" system if it deals more with operational state.
*   **Key Modules/Files:**
    *   *(To be filled by listing files)*

---

## Sub-directory: `person_suit/core/actors/`

*   **Purpose:** Provides the implementation of the **Actor Model** for concurrency and state management, supporting the **CAW Actors** principle. Enables building concurrent, distributed, and fault-tolerant systems through independent actors that communicate via asynchronous messages.
*   **Key Concepts Introduced:** Actor, Message, Actor System, Supervision, Central State Actor, Choreography.
*   **Key Modules/Files:**
    *   `actor_system.py`:
        *   **Purpose:** Implements the core asynchronous Actor System using `asyncio`. Manages actor lifecycle (create, stop, restart), message dispatching, integrates Death Watch/Supervision, and includes capability checks. Aligns with CAW principles.
        *   **Key Classes:** `ActorSystem`.
        *   **Key Functions:** `__init__`, `set_capability_service_ref`, `start`, `stop`, `create_actor`, `_cleanup_failed_start`, `get_actor_ref`, `send`, `deliver_message`, `ask`, `resolve_ask_future`, `watch`, `unwatch`, `notify_termination`, `handle_actor_failure`, `stop_actor`, `restart_actor`.
        *   **Actors:** None.
    *   `actor_system_watch.py`:
        *   **Purpose:** Implements the death watch functionality for the Actor System, managing watch relationships and notifying actors when others terminate. Includes capability checks for watch operations.
        *   **Key Classes:** `DeathWatchManager`.
        *   **Key Functions:** `__init__`, `clear`, `watch`, `unwatch`, `notify_termination`.
        *   **Actors:** None.
    *   *(Files will be listed here with their details)*
*   **Key Sub-directories:**
    *   `choreography/`: (Purpose to be detailed later - likely contains specific choreography implementations or related logic)
    *   `central_state/`: (Purpose to be detailed later - likely contains specific components related to the Central State Actor or its state management)

---

## Sub-directory: `person_suit/core/adaptivity/`

*   **Purpose:** Houses modules related to the system's adaptive capabilities, potentially including mechanisms for Adaptive Computational Fidelity (ACF) or other dynamic adjustments based on context, as per CAW principles.
*   **Key Modules/Files:**
    *   *(To be filled by listing files)*

---

## Sub-directory: `person_suit/core/application/`

*   **Purpose:** Contains core application logic, orchestration, or services that are not specific to a single meta-system but are foundational to the application's overall operation. This might include handlers or core use case implementations.
*   **Key Modules/Files:**
    *   *(To be filled by listing files)*

---

## Sub-directory: `person_suit/core/capabilities/`

*   **Purpose:** Implements the core logic for Capability-Based Security (CBS), including defining capabilities, managing capability tokens, and performing authorization checks. Central to CAW's security model.
*   **Key Modules/Files:**
    *   *(To be filled by listing files)*

---

## Sub-directory: `person_suit/core/caw/`

*   **Purpose:** Central hub for the fundamental constructs of Contextual Adaptive Wave (CAW) programming. This would include definitions for duality, contextual computation, CAW actors, choreographies, effects, and potentially interfaces to advanced mathematical structures.
*   **Key Modules/Files:**
    *   *(To be filled by listing files)*

---

## Sub-directory: `person_suit/core/config/`

*   **Purpose:** (From README.md) Contains the Python code responsible for loading, parsing, validating, and providing access to configuration data for the Person Suit framework. It provides the *logic* to interact with actual configuration files (e.g., stored in a root `/config/` directory).
*   **Key Modules/Files:**
    *   `loader.py`:
        *   **Purpose:** Provides utilities for loading application configuration settings from an external YAML file (defaulting to `config.yaml` in the project root). It includes caching for the loaded configuration and provides a helper function to retrieve specific values using a dot-separated key.
        *   **Key Classes:** None.
        *   **Key Functions:**
            *   `load_config(config_path: str = DEFAULT_CONFIG_PATH) -> Dict[str, Any]`: Loads configuration from a YAML file, caches the result, and handles errors.
            *   `get_config_value(key: str, default: Any = None) -> Any`: Retrieves a specific value from the loaded configuration cache, supporting dot-notation for nested keys.
        *   **Actors:** None.
    *   `README.md`:
        *   **Purpose:** Describes the `person_suit/core/config/` directory, its purpose (loading, parsing, validating, providing access to configuration data), core components (`loader.py`), design alignment (Separation of Concerns, Modularity), and usage patterns (suggesting an `IConfigManager` interface for DI).

---

## Sub-directory: `person_suit/core/constants/`

*   **Purpose:** Defines global constants, enums, and other fixed values used throughout the core system or wider application.
*   **Key Modules/Files:**
    *   `hardware.py`:
        *   **Purpose:** Defines hardware-specific constants (CPU, memory, disk, GPU, Apple-specific, optimization flags) and determines a hardware capability level.
        *   **Key Constants:** `CPU_COUNT`, `TOTAL_MEMORY`, `GPU_AVAILABLE`, `IS_APPLE_SILICON`, `HARDWARE_OPTIMIZATIONS`, `HARDWARE_CAPABILITY_LEVEL`, and many others.
        *   **Key Classes:** `HardwareCapabilityLevel`.
        *   **Key Functions:** `_determine_hardware_capability_level`.
        *   **Actors:** None.
    *   `performance.py`:
        *   **Purpose:** Defines constants for performance tuning, including batch/chunk/page sizes, cache configurations, thread/process pool sizes, thresholds, timeouts, retry logic, database settings, and vector operation parameters. Includes optimization strategies and M3-specific settings.
        *   **Key Constants:** `DEFAULT_BATCH_SIZE`, `DEFAULT_CACHE_SIZE`, `DEFAULT_THREAD_POOL_SIZE`, `DEFAULT_CPU_THRESHOLD`, `DEFAULT_OPERATION_TIMEOUT`, `DEFAULT_RETRY_COUNT`, `DEFAULT_DB_CONNECTION_POOL_SIZE`, `DEFAULT_VECTOR_DIMENSION` (2048), `DEFAULT_OPTIMIZATION_STRATEGIES`, `M3_OPTIMIZATIONS`, and many others.
        *   **Key Classes:** None.
        *   **Key Functions:** None.
        *   **Actors:** None.
    *   `system.py`:
        *   **Purpose:** Defines system-wide operational constants including timeouts, resource limits, platform detection, M3 architecture details, default settings (encoding, locale, etc.), file system params, network settings, and process/thread counts.
        *   **Key Constants:** Numerous constants such as `DEFAULT_OPERATION_TIMEOUT`, `MAX_CONCURRENT_OPERATIONS`, `IS_APPLE_SILICON`, `M3_ARCHITECTURE`, `DEFAULT_ENCODING`, `MAX_PATH_LENGTH`, `DEFAULT_PORT`, `DEFAULT_PROCESS_COUNT`, and `DEFAULT_MEMORY_LIMIT`.
        *   **Key Classes:** None.
        *   **Key Functions:** None.
        *   **Actors:** None.
    *   `__init__.py`:
        *   **Purpose:** Package initializer for `person_suit.core.constants`. Defines the constant organization structure and re-exports key constants from sub-modules (`system`, `security`, `performance`, `hardware`) for convenient access.
        *   **Key Constants:** Re-exports numerous constants from other files in this directory (e.g., `DEFAULT_OPERATION_TIMEOUT`, `SECURITY_LEVEL`, `DEFAULT_BATCH_SIZE`, `CPU_COUNT`), as listed in `__all__`. Does not define new constants.
        *   **Key Classes:** None.
        *   **Key Functions:** None.
        *   **Actors:** None.
    *   `README.md`:
        *   **Purpose:** Provides an overview of the `person_suit/core/constants/` directory, explaining its purpose (defining system-wide constants) and listing the main constant files within it (`system.py`, `security.py`, `performance.py`, `hardware.py`) and how to use them.
    *   `security.py`:
        *   **Purpose:** Defines constants related to security configurations and practices (cryptography, security levels, auth, capabilities, zero-trust, audit, quantum-resistant algorithms, formal verification, secure communication).
        *   **Key Constants:** Numerous constants covering cryptographic settings, authentication, capabilities, zero-trust, audit, quantum-resistant algorithms, formal verification, and secure communication protocols.
        *   **Key Classes:** `SecurityLevel`.
        *   **Key Functions:** None.
        *   **Actors:** None.
    *   *(Other files in constants/ will be added here)*

---

## Sub-directory: `person_suit/core/context/`

*   **Purpose:** Manages contextual information that is pervasive throughout the application. This is critical for CAW, supporting context propagation and its influence on computation.

---

## Sub-directory: `person_suit/core/deployment/`

*   **Purpose:** Contains modules related to the deployment and operational aspects of the core system, potentially including health checks, resource management, or environment detection.
*   **Key Modules/Files:**
    *   *(To be filled by listing files)*

---

## Sub-directory: `person_suit/core/effects/`

*   **Purpose:** Implements the Effect System, making side effects explicit and manageable. This aligns with CAW's principle of managing operational consequences.
*   **Key Modules/Files:**
    *   *(e.g., `effect_handler.py`, `effect_types.py`)*

---

## Sub-directory: `person_suit/core/event_logging/`

*   **Purpose:** Specialized logging for system events, likely more structured than general application logging, perhaps for audit trails or specific event sourcing patterns.
*   **Key Modules/Files:**
    *   `arango_log_service.py`:
        *   **Purpose:** Concrete implementation of the Event/Effect Log Service using ArangoDB. Persists event history to an ArangoDB collection, providing query capabilities, and manages snapshot metadata.
        *   **Key Classes:** `ArangoDBEventLogService`.
        *   **Key Functions:** `__init__`, `_ensure_indexes`, `_serialize_dataclass`, `log_event`, `query_events`, `get_latest_event`, `record_snapshot_metadata`, `get_latest_snapshot_metadata`.
        *   **Actors:** None.

---

## Sub-directory: `person_suit/core/events/`

*   **Purpose:** Defines core event system abstractions and interfaces, including base event types and interfaces for event managers/buses.
*   **Key Modules/Files:**
    *   `__init__.py`:
        *   **Purpose:** Package initializer for `person_suit.core.events`. Defines the constant organization structure and re-exports key event-related components, currently `InfrastructureEventManager`, from the infrastructure layer.
        *   **Key Classes:** None defined here, but re-exports `InfrastructureEventManager`. (Docstring mentions base types/interfaces, but not visible in snippet).
        *   **Key Functions:** None.
        *   **Actors:** None.

---

## Sub-directory: `person_suit/core/formal_methods/`

*   **Purpose:** Includes components or integrations related to formal verification or mathematically rigorous specification of system parts, enhancing reliability and security.
*   **Key Modules/Files:**
    *   *(To be filled by listing files)*

---

## Sub-directory: `person_suit/core/information/`

*   **Purpose:** Deals with the core representation, processing, and flow of information within the system, potentially including aspects of CAW's information duality (wave/particle).
*   **Key Modules/Files:**
    *   *(To be filled by listing files)*

---

## Sub-directory: `person_suit/core/infrastructure/`

*   **Purpose:** Provides low-level infrastructure services and abstractions, such as communication protocols, data persistence layers (or interfaces to them), and other foundational technical capabilities supporting the core logic. The `dual_wave` components likely reside here.
*   **Key Modules/Files:**
    *   *(e.g., `dual_wave/`, `communication/`, `persistence/`)*

---

## Sub-directory: `person_suit/core/memory/`

*   **Purpose:** Defines interfaces and core logic for the Person Suit memory system, abstracting different memory layers (sensory, working, long-term) and processes (encoding, retrieval).
*   **Key Modules/Files:**
    *   *(e.g., `memory_interface.py`, `cache_manager.py`)*

---

## Sub-directory: `person_suit/core/notification/`

*   **Purpose:** Manages system-wide notifications, which could be internal alerts or hooks for external notification systems.
*   **Key Modules/Files:**
    *   *(To be filled by listing files)*

---

## Sub-directory: `person_suit/core/pathway/`

*   **Purpose:** Likely relates to the "Folded Mind" architecture, defining core logic for computational-functional and subjective-experiential pathways, or general CAW choreographies.
*   **Key Modules/Files:**
    *   *(To be filled by listing files)*

---

## Sub-directory: `person_suit/core/security/`

*   **Purpose:** Contains broader security mechanisms beyond capabilities, such as authentication, encryption utilities, and security policy enforcement.
*   **Key Modules/Files:**
    *   *(e.g., `auth_service.py`, `encryption.py`)*

---

## Sub-directory: `person_suit/core/state_storage/`

*   **Purpose:** Provides abstractions and implementations for persisting and managing the state of core components or the overall application. This might be distinct from the primary "memory" system if it deals more with operational state.
*   **Key Modules/Files:**
    *   *(To be filled by listing files)*